<?php
session_start();
require_once __DIR__ . '/includes/functions.php';

$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html>
<head>
    <title>API调试</title>
    <script src="/assets/js/jquery.min.js"></script>
</head>
<body>
    <h2>API调试页面</h2>
    
    <h3>用户状态</h3>
    <p>Session User ID: <?php echo $_SESSION['user_id'] ?? 'null'; ?></p>
    <p>Current User: <?php echo $currentUser ? json_encode($currentUser) : 'null'; ?></p>
    
    <h3>测试API调用</h3>
    <button onclick="testSimpleAPI()">测试简单API</button>
    <button onclick="testFavoriteAPI()">测试收藏API</button>
    <button onclick="testLikeAPI()">测试点赞API</button>
    <button onclick="testBasicAPI()">测试基础API</button>
    
    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
    
    <script>
    function testSimpleAPI() {
        console.log('测试简单API...');
        fetch('/api/simple_test.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                test: 'simple'
            })
        })
        .then(response => {
            console.log('简单API响应状态:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('简单API原始响应:', text);
            document.getElementById('results').innerHTML = '<h4>简单API响应:</h4><pre>' + text + '</pre>';
            try {
                const data = JSON.parse(text);
                console.log('简单API解析后数据:', data);
            } catch (e) {
                console.error('JSON解析失败:', e);
            }
        })
        .catch(error => {
            console.error('简单API错误:', error);
            document.getElementById('results').innerHTML = '<h4>简单API错误:</h4><pre>' + error.toString() + '</pre>';
        });
    }

    function testFavoriteAPI() {
        console.log('测试收藏API...');
        fetch('/api/favorites.php?action=toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: 'album',
                id: 1
            })
        })
        .then(response => {
            console.log('收藏API响应状态:', response.status);
            console.log('响应头:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('收藏API原始响应:', text);
            document.getElementById('results').innerHTML = '<h4>收藏API响应:</h4><pre>' + text + '</pre>';
            try {
                const data = JSON.parse(text);
                console.log('收藏API解析后数据:', data);
            } catch (e) {
                console.error('JSON解析失败:', e);
            }
        })
        .catch(error => {
            console.error('收藏API错误:', error);
            document.getElementById('results').innerHTML = '<h4>收藏API错误:</h4><pre>' + error.toString() + '</pre>';
        });
    }
    
    function testLikeAPI() {
        console.log('测试点赞API...');
        fetch('/api/like.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: 'album',
                id: 1
            })
        })
        .then(response => {
            console.log('点赞API响应状态:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('点赞API原始响应:', text);
            document.getElementById('results').innerHTML = '<h4>点赞API响应:</h4><pre>' + text + '</pre>';
            try {
                const data = JSON.parse(text);
                console.log('点赞API解析后数据:', data);
            } catch (e) {
                console.error('JSON解析失败:', e);
            }
        })
        .catch(error => {
            console.error('点赞API错误:', error);
            document.getElementById('results').innerHTML = '<h4>点赞API错误:</h4><pre>' + error.toString() + '</pre>';
        });
    }
    
    function testBasicAPI() {
        console.log('测试基础API...');
        fetch('/api/test.php?action=test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                test: 'data'
            })
        })
        .then(response => {
            console.log('基础API响应状态:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('基础API原始响应:', text);
            document.getElementById('results').innerHTML = '<h4>基础API响应:</h4><pre>' + text + '</pre>';
        })
        .catch(error => {
            console.error('基础API错误:', error);
            document.getElementById('results').innerHTML = '<h4>基础API错误:</h4><pre>' + error.toString() + '</pre>';
        });
    }
    </script>
</body>
</html>
