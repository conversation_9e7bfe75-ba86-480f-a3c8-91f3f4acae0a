<?php
session_start();
require_once __DIR__ . '/core/Database.php';

try {
    $db = Database::getInstance();
    echo "<h2>数据库连接测试</h2>";
    
    // 测试基本连接
    echo "<h3>1. 基本连接测试</h3>";
    $result = $db->query("SELECT 1 as test");
    if ($result) {
        echo "✅ 数据库连接成功<br>";
    } else {
        echo "❌ 数据库连接失败<br>";
    }
    
    // 测试表是否存在
    echo "<h3>2. 表存在性测试</h3>";
    $tables = ['lp_users', 'lp_favorites', 'lp_user_likes', 'lp_comments'];
    foreach ($tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->rowCount() > 0) {
            echo "✅ 表 $table 存在<br>";
        } else {
            echo "❌ 表 $table 不存在<br>";
        }
    }
    
    // 测试用户session
    echo "<h3>3. 用户Session测试</h3>";
    echo "Session ID: " . session_id() . "<br>";
    echo "User ID: " . ($_SESSION['user_id'] ?? 'null') . "<br>";
    
    if (isset($_SESSION['user_id'])) {
        $user = $db->fetch("SELECT * FROM lp_users WHERE id = :id", ['id' => $_SESSION['user_id']]);
        if ($user) {
            echo "✅ 用户信息获取成功: " . $user['username'] . "<br>";
        } else {
            echo "❌ 用户信息获取失败<br>";
        }
    } else {
        echo "⚠️ 用户未登录<br>";
    }
    
    // 测试API调用
    echo "<h3>4. 模拟API调用测试</h3>";
    if (isset($_SESSION['user_id'])) {
        // 测试收藏查询
        $favoriteCount = $db->fetchColumn(
            "SELECT COUNT(*) FROM lp_favorites WHERE user_id = :user_id AND content_type = 'album' AND content_id = 1",
            ['user_id' => $_SESSION['user_id']]
        );
        echo "用户对套图1的收藏状态: " . ($favoriteCount > 0 ? '已收藏' : '未收藏') . "<br>";
        
        // 测试点赞查询
        $likeCount = $db->fetchColumn(
            "SELECT COUNT(*) FROM lp_user_likes WHERE user_id = :user_id AND content_type = 'album' AND content_id = 1",
            ['user_id' => $_SESSION['user_id']]
        );
        echo "用户对套图1的点赞状态: " . ($likeCount > 0 ? '已点赞' : '未点赞') . "<br>";
    }
    
} catch (Exception $e) {
    echo "<h3>错误</h3>";
    echo "错误信息: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . "<br>";
    echo "错误行号: " . $e->getLine() . "<br>";
}
?>
