<?php
session_start();

// 引入必要的文件
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/models/Album.php';
require_once __DIR__ . '/models/Video.php';

// 初始化数据
try {
    $albumModel = new Album();
    $videoModel = new Video();

    // 获取最新套图
    $latestAlbums = $albumModel->getList([
        'page' => 1,
        'page_size' => 12,
        'order' => 'latest'
    ]);

    // 获取热门套图
    $hotAlbums = $albumModel->getList([
        'page' => 1,
        'page_size' => 8,
        'order' => 'view'
    ]);

    // 获取最新视频
    $latestVideos = $videoModel->getList([
        'page' => 1,
        'page_size' => 8,
        'order' => 'latest'
    ]);

    // 获取热门视频
    $hotVideos = $videoModel->getList([
        'page' => 1,
        'page_size' => 6,
        'order' => 'view'
    ]);
} catch (Exception $e) {
    // 如果数据库查询失败，使用空数据
    $latestAlbums = ['list' => [], 'total' => 0];
    $hotAlbums = ['list' => [], 'total' => 0];
    $latestVideos = ['list' => [], 'total' => 0];
    $hotVideos = ['list' => [], 'total' => 0];
    
    // 记录错误（在生产环境中应该记录到日志文件）
    error_log("首页数据加载失败: " . $e->getMessage());
}

// 处理套图数据
foreach ($latestAlbums['list'] as $index => $album) {
    $latestAlbums['list'][$index]['is_free'] = $album['is_free'];
    $latestAlbums['list'][$index]['cover'] = $album['cover'] ?: '/assets/images/placeholder.svg';
    $latestAlbums['list'][$index]['slug'] = $album['slug'] ?: 'album-' . $album['id'];
    $latestAlbums['list'][$index]['image_count'] = $album['image_count'] ?: 0;
}
unset($album); // 清除引用

foreach ($hotAlbums['list'] as $index => $album) {
    $hotAlbums['list'][$index]['is_free'] = $album['is_free'];
    $hotAlbums['list'][$index]['cover'] = $album['cover'] ?: '/assets/images/placeholder.svg';
    $hotAlbums['list'][$index]['slug'] = $album['slug'] ?: 'album-' . $album['id'];
    $hotAlbums['list'][$index]['image_count'] = $album['image_count'] ?: 0;
}
unset($album); // 清除引用

// 处理视频数据
foreach ($latestVideos['list'] as $index => $video) {
    $latestVideos['list'][$index]['is_free'] = $video['is_free'];
    $latestVideos['list'][$index]['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
    $latestVideos['list'][$index]['slug'] = $video['slug'] ?: 'video-' . $video['id'];
    $latestVideos['list'][$index]['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
}
unset($video); // 清除引用

foreach ($hotVideos['list'] as $index => $video) {
    $hotVideos['list'][$index]['is_free'] = $video['is_free'];
    $hotVideos['list'][$index]['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
    $hotVideos['list'][$index]['slug'] = $video['slug'] ?: 'video-' . $video['id'];
    $hotVideos['list'][$index]['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
}
unset($video); // 清除引用

// 设置页面信息
$pageTitle = Config::getSiteName() . ' - ' . Config::getSiteDescription();
$pageDescription = Config::getSiteDescription() . '，提供高质量的美女图片和视频内容';
$pageKeywords = Config::getSiteKeywords();

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 轮播图 - 暂时注释掉，如需使用请删除注释 -->
    <!--
    <div class="row mb-4">
        <div class="col-12">
            <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-indicators">
                    <?php for ($i = 0; $i < min(5, count($hotAlbums['list'])); $i++): ?>
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?php echo $i; ?>"
                            <?php echo $i === 0 ? 'class="active"' : ''; ?>></button>
                    <?php endfor; ?>
                </div>

                <div class="carousel-inner">
                    <?php foreach (array_slice($hotAlbums['list'], 0, 5) as $index => $album): ?>
                    <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                        <img src="<?php echo htmlspecialchars($album['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                             class="d-block w-100" style="height: 400px; object-fit: cover;"
                             alt="<?php echo htmlspecialchars($album['title'] ?? ''); ?>"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="carousel-caption d-none d-md-block">
                            <h5><?php echo htmlspecialchars($album['title'] ?? ''); ?></h5>
                            <p><?php echo htmlspecialchars(mb_substr($album['description'] ?? '', 0, 100) . '...'); ?></p>
                            <a href="/album/<?php echo $album['id']; ?>" class="btn btn-warning">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                </button>
            </div>
        </div>
    </div>
    -->

    <!-- 最新套图 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-images me-2"></i>最新套图
                </h3>
                <a href="/albums" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            
            <div class="album-grid">
                <?php foreach ($latestAlbums['list'] as $album): ?>
                <a href="/album/<?php echo $album['id']; ?>" class="album-card text-decoration-none">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($album['cover'] ?? ''); ?>"
                             alt="<?php echo htmlspecialchars($album['title'] ?? ''); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <span><i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?></span>
                                <span><i class="fas fa-images me-1"></i><?php echo $album['image_count']; ?>张</span>
                            </div>
                        </div>
                        <?php if (!$album['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($album['title'] ?? ''); ?>
                        </div>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($album['description'] ?? '', 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($album['category_name'] ?? '未分类'); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($album['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 热门视频 - 暂时注释掉，如需使用请删除注释 -->
    <!--
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-video me-2"></i>热门视频
                </h3>
                <a href="/videos" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>

            <div class="album-grid">
                <?php foreach ($hotVideos['list'] as $video): ?>
                <a href="/video/<?php echo $video['id']; ?>" class="album-card video-card text-decoration-none">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($video['cover'] ?? ''); ?>"
                             alt="<?php echo htmlspecialchars($video['title'] ?? ''); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?></span>
                                    <span><i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?></span>
                                </div>
                            </div>
                            <div class="play-button">
                                <i class="fas fa-play-circle"></i>
                            </div>
                        </div>
                        <?php if (!$video['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($video['title'] ?? ''); ?>
                        </div>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($video['description'] ?? '', 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($video['category_name'] ?? '未分类'); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($video['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    -->

    <!-- 最新视频 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-film me-2"></i>最新视频
                </h3>
                <a href="/videos?order=latest" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            
            <div class="album-grid">
                <?php foreach ($latestVideos['list'] as $video): ?>
                <a href="/video/<?php echo $video['id']; ?>" class="album-card video-card text-decoration-none">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($video['cover'] ?? ''); ?>"
                             alt="<?php echo htmlspecialchars($video['title'] ?? ''); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?></span>
                                    <span><i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?></span>
                                </div>
                            </div>
                            <div class="play-button">
                                <i class="fas fa-play-circle"></i>
                            </div>
                        </div>
                        <?php if (!$video['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <div class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($video['title'] ?? ''); ?>
                        </div>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($video['description'] ?? '', 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i><?php echo date('m-d', strtotime($video['created_at'])); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($video['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 硬编码的友情链接已删除，现在使用footer中的动态友情链接 -->
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
// 确保页面内容立即显示
document.addEventListener('DOMContentLoaded', function() {
    // 移除可能的加载遮罩
    const loadingMask = document.querySelector('.loading-mask');
    if (loadingMask) {
        loadingMask.remove();
    }
    
    // 确保所有图片都能正常显示
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        // 如果图片已经加载，移除占位符样式
        if (img.complete && img.naturalHeight !== 0) {
            img.classList.remove('img-placeholder');
            img.classList.add('loaded');
        } else {
            // 监听图片加载完成
            img.addEventListener('load', function() {
                this.classList.remove('img-placeholder');
                this.classList.add('loaded');
            });
            
            // 监听图片加载失败
            img.addEventListener('error', function() {
                this.classList.remove('img-placeholder');
                this.classList.add('loaded');
                if (!this.src.includes('placeholder.svg')) {
                    this.src = '/assets/images/placeholder.svg';
                }
            });
        }
    });
    
    // 初始化轮播图 - 暂时注释掉，如需使用请删除注释
    /*
    if (typeof bootstrap !== 'undefined' && document.querySelector('#heroCarousel')) {
        try {
            new bootstrap.Carousel(document.querySelector('#heroCarousel'), {
                interval: 5000,
                wrap: true
            });
        } catch (e) {
            console.log('轮播图初始化失败:', e);
        }
    }
    */
});
";

// 包含布局模板
include __DIR__ . '/templates/layout.php';
?>
