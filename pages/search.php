<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/Video.php';
require_once __DIR__ . '/../includes/functions.php';

// 获取搜索参数
$keyword = trim($_GET['q'] ?? '');
$type = $_GET['type'] ?? 'all'; // all, album, video
$page = max(1, intval($_GET['page'] ?? 1));

// 初始化模型
$albumModel = new Album();
$videoModel = new Video();

$searchResults = [];
$total = 0;

if (!empty($keyword)) {
    try {
        $allResults = [];
        
        // 搜索套图
        if ($type === 'all' || $type === 'album') {
            $albums = $albumModel->getList([
                'keyword' => $keyword,
                'page' => 1,
                'page_size' => 100,
                'order' => 'latest'
            ]);



            foreach ($albums['list'] as $album) {
                $album['content_type'] = 'album';
                // 注意：数据库中字段名是is_free，不需要转换
                $album['cover'] = $album['cover'] ?: '/assets/images/placeholder.svg';
                $album['slug'] = $album['slug'] ?: 'album-' . $album['id'];
                // 注意：数据库中字段名是image_count，不需要转换
                $allResults[] = $album;
            }
        }
        
        // 搜索视频
        if ($type === 'all' || $type === 'video') {
            $videos = $videoModel->getList([
                'keyword' => $keyword,
                'page' => 1,
                'page_size' => 100,
                'order' => 'latest'
            ]);
            
            foreach ($videos['list'] as $video) {
                $video['content_type'] = 'video';
                // 注意：数据库中字段名是is_free，不需要转换
                $video['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
                $video['slug'] = $video['slug'] ?: 'video-' . $video['id'];
                $video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
                $allResults[] = $video;
            }
        }
        
        // 按相关性排序（这里简单按时间排序）
        usort($allResults, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        // 分页处理
        $pageSize = 20;
        $total = count($allResults);
        $totalPages = ceil($total / $pageSize);
        $offset = ($page - 1) * $pageSize;
        $searchResults = array_slice($allResults, $offset, $pageSize);
        
    } catch (Exception $e) {
        error_log("搜索失败: " . $e->getMessage());
    }
}

// 设置页面信息
$pageTitle = !empty($keyword) ? 
    '搜索: ' . htmlspecialchars($keyword) . ' - ' . getConfig('site_name', Config::getSiteName()) : 
    '搜索 - ' . getConfig('site_name', Config::getSiteName());
$pageDescription = !empty($keyword) ? 
    '搜索 "' . htmlspecialchars($keyword) . '" 的相关内容' : 
    '搜索您感兴趣的内容';
$pageKeywords = '搜索,' . htmlspecialchars($keyword);

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item active">搜索</li>
        </ol>
    </nav>

    <!-- 搜索框 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="/search">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control form-control-lg" 
                                           name="q" value="<?php echo htmlspecialchars($keyword); ?>" 
                                           placeholder="输入关键词搜索..." required>
                                    <button class="btn btn-warning" type="submit">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select form-select-lg" name="type">
                                    <option value="all" <?php echo $type === 'all' ? 'selected' : ''; ?>>全部内容</option>
                                    <option value="album" <?php echo $type === 'album' ? 'selected' : ''; ?>>套图</option>
                                    <option value="video" <?php echo $type === 'video' ? 'selected' : ''; ?>>视频</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($keyword)): ?>
    <!-- 搜索结果 -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="text-warning mb-3">
                <i class="fas fa-search me-2"></i>搜索结果
            </h3>
            <p class="text-muted">
                关键词 "<strong><?php echo htmlspecialchars($keyword); ?></strong>" 共找到 <?php echo number_format($total); ?> 个结果
            </p>

        </div>
    </div>

    <?php if (!empty($searchResults)): ?>
    <!-- 结果列表 -->
    <div class="row">
        <?php foreach ($searchResults as $item): ?>
        <div class="col-lg-6 col-12 mb-4">
            <div class="card search-result-item h-100">
                <div class="row g-0 h-100">
                    <div class="col-4">
                        <div class="position-relative h-100">
                            <img src="<?php echo htmlspecialchars($item['cover']); ?>" 
                                 alt="<?php echo htmlspecialchars($item['title']); ?>" 
                                 class="img-fluid h-100 w-100"
                                 style="object-fit: cover;"
                                 onerror="this.src='/assets/images/placeholder.svg'">
                            
                            <!-- 内容类型标识 -->
                            <div class="position-absolute top-0 start-0 m-2">
                                <?php if ($item['content_type'] === 'video'): ?>
                                <span class="badge bg-danger">
                                    <i class="fas fa-video me-1"></i>视频
                                </span>
                                <?php else: ?>
                                <span class="badge bg-primary">
                                    <i class="fas fa-images me-1"></i>套图
                                </span>
                                <?php endif; ?>
                            </div>
                            
                            <!-- VIP标识 -->
                            <?php if (!$item['is_free']): ?>
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-warning text-dark">VIP</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-8">
                        <div class="card-body d-flex flex-column h-100">
                            <h5 class="card-title mb-2">
                                <a href="/<?php echo $item['content_type']; ?>/<?php echo $item['id']; ?>"
                                   class="text-warning text-decoration-none">
                                    <?php 
                                    // 高亮关键词
                                    $title = htmlspecialchars($item['title']);
                                    $highlightedTitle = str_ireplace($keyword, '<mark class="bg-warning text-dark">' . htmlspecialchars($keyword) . '</mark>', $title);
                                    echo $highlightedTitle;
                                    ?>
                                </a>
                            </h5>
                            
                            <p class="card-text text-muted flex-grow-1">
                                <?php 
                                // 高亮描述中的关键词
                                $description = htmlspecialchars(mb_substr($item['description'], 0, 100) . '...');
                                $highlightedDesc = str_ireplace($keyword, '<mark class="bg-warning text-dark">' . htmlspecialchars($keyword) . '</mark>', $description);
                                echo $highlightedDesc;
                                ?>
                            </p>
                            
                            <div class="search-meta mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($item['view_count']); ?>
                                            <i class="fas fa-heart ms-2 me-1"></i><?php echo number_format($item['favorite_count']); ?>
                                            <?php if ($item['content_type'] === 'video'): ?>
                                            <i class="fas fa-clock ms-2 me-1"></i><?php echo $item['duration_formatted']; ?>
                                            <?php else: ?>
                                            <i class="fas fa-images ms-2 me-1"></i><?php echo $item['image_count']; ?>张
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('Y-m-d', strtotime($item['created_at'])); ?>
                                    </small>
                                </div>
                                <?php if ($item['category_name']): ?>
                                <div class="mt-1">
                                    <small class="text-muted">
                                        <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($item['category_name']); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- 分页 -->
    <?php if (isset($totalPages) && $totalPages > 1): ?>
    <nav class="mt-5">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?q=<?php echo urlencode($keyword); ?>&type=<?php echo $type; ?>&page=<?php echo $page - 1; ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?q=<?php echo urlencode($keyword); ?>&type=<?php echo $type; ?>&page=<?php echo $i; ?>">
                    <?php echo $i; ?>
                </a>
            </li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?q=<?php echo urlencode($keyword); ?>&type=<?php echo $type; ?>&page=<?php echo $page + 1; ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- 无结果 -->
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">未找到相关内容</h4>
        <p class="text-muted">请尝试使用其他关键词搜索</p>
        <div class="mt-3">
            <a href="/" class="btn btn-warning me-2">
                <i class="fas fa-home me-1"></i>返回首页
            </a>
            <a href="/albums" class="btn btn-outline-warning me-2">
                <i class="fas fa-images me-1"></i>浏览套图
            </a>
            <a href="/videos" class="btn btn-outline-warning">
                <i class="fas fa-video me-1"></i>浏览视频
            </a>
        </div>
    </div>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- 搜索提示 -->
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-warning mb-3"></i>
        <h4 class="text-warning">开始搜索</h4>
        <p class="text-muted">输入关键词搜索您感兴趣的内容</p>
        
        <!-- 热门搜索建议 -->
        <div class="mt-4">
            <h6 class="text-muted mb-3">热门搜索</h6>
            <div class="d-flex flex-wrap justify-content-center gap-2">
                <a href="?q=美女" class="btn btn-outline-warning btn-sm">美女</a>
                <a href="?q=写真" class="btn btn-outline-warning btn-sm">写真</a>
                <a href="?q=时尚" class="btn btn-outline-warning btn-sm">时尚</a>
                <a href="?q=艺术" class="btn btn-outline-warning btn-sm">艺术</a>
                <a href="?q=古风" class="btn btn-outline-warning btn-sm">古风</a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.search-result-item {
    transition: transform 0.2s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.search-meta {
    font-size: 0.85em;
}

mark {
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
}
</style>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 搜索结果项目点击事件
    document.querySelectorAll('.search-result-item').forEach(item => {
        item.addEventListener('click', function(e) {
            if (!e.target.closest('a')) {
                const link = this.querySelector('a');
                if (link) {
                    window.location.href = link.href;
                }
            }
        });
    });
    
    // 搜索框自动聚焦
    const searchInput = document.querySelector('input[name=\"q\"]');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
