<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/Video.php';
require_once __DIR__ . '/../includes/functions.php';

// 获取排行类型
$type = $_GET['type'] ?? 'view';
$category = $_GET['category'] ?? 'album'; // album 或 video

// 初始化模型
$albumModel = new Album();
$videoModel = new Video();

// 获取排行数据
try {
    if ($category === 'video') {
        $rankingData = $videoModel->getList([
            'page' => 1,
            'page_size' => 50,
            'order' => $type
        ]);
    } else {
        $rankingData = $albumModel->getList([
            'page' => 1,
            'page_size' => 50,
            'order' => $type
        ]);
    }
    
    $items = $rankingData['list'];
} catch (Exception $e) {
    $items = [];
    error_log("排行榜数据加载失败: " . $e->getMessage());
}

// 处理数据
foreach ($items as $index => $item) {
    // 注意：数据库中字段名是is_free，不需要转换
    if ($category === 'video') {
        $items[$index]['cover'] = $item['cover'] ?: '/assets/images/placeholder.svg';
        $items[$index]['slug'] = $item['slug'] ?: 'video-' . $item['id'];
        $items[$index]['duration_formatted'] = $item['duration'] ? gmdate("H:i:s", $item['duration']) : '00:00';
    } else {
        $items[$index]['cover'] = $item['cover'] ?: '/assets/images/placeholder.svg';
        $items[$index]['slug'] = $item['slug'] ?: 'album-' . $item['id'];
        // 注意：数据库中字段名是image_count，不需要转换
    }
}
unset($item); // 清除引用

// 设置页面信息
$typeNames = [
    'view' => '点击排行',
    'favorite' => '收藏排行',
    'like' => '点赞排行',
    'download' => '下载排行'
];

$categoryNames = [
    'album' => '套图',
    'video' => '视频'
];

$pageTitle = ($categoryNames[$category] ?? '内容') . ' - ' . ($typeNames[$type] ?? '排行榜') . ' - ' . getConfig('site_name', Config::getSiteName());
$pageDescription = '最受欢迎的' . ($categoryNames[$category] ?? '内容') . '排行榜';
$pageKeywords = '排行榜,热门,推荐';

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item active">排行榜</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="text-warning mb-3">
                <i class="fas fa-trophy me-2"></i>排行榜
            </h2>
        </div>
    </div>

    <!-- 排行榜导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- 内容类型切换 -->
                    <div class="btn-group me-3 mb-2" role="group">
                        <a href="?category=album&type=<?php echo $type; ?>"
                           class="btn <?php echo $category === 'album' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-images me-1"></i>套图排行
                        </a>
                        <a href="?category=video&type=<?php echo $type; ?>"
                           class="btn <?php echo $category === 'video' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-video me-1"></i>视频排行
                        </a>
                    </div>

                    <!-- 排行类型切换 -->
                    <div class="btn-group mb-2" role="group">
                        <a href="?category=<?php echo $category; ?>&type=view"
                           class="btn <?php echo $type === 'view' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-eye me-1"></i>点击排行
                        </a>
                        <a href="?category=<?php echo $category; ?>&type=favorite"
                           class="btn <?php echo $type === 'favorite' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-heart me-1"></i>收藏排行
                        </a>
                        <a href="?category=<?php echo $category; ?>&type=like"
                           class="btn <?php echo $type === 'like' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-thumbs-up me-1"></i>点赞排行
                        </a>
                        <a href="?category=<?php echo $category; ?>&type=download"
                           class="btn <?php echo $type === 'download' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?>">
                            <i class="fas fa-download me-1"></i>下载排行
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 排行榜列表 -->
    <?php if (!empty($items)): ?>
    <div class="row">
        <?php foreach ($items as $index => $item): ?>
        <div class="col-12 mb-3">
            <div class="card ranking-item">
                <div class="card-body">
                    <div class="row align-items-center">
                        <!-- 排名 -->
                        <div class="col-auto">
                            <div class="ranking-number <?php echo $index < 3 ? 'top-three' : ''; ?>">
                                <?php if ($index === 0): ?>
                                    <i class="fas fa-crown text-warning"></i>
                                <?php elseif ($index === 1): ?>
                                    <i class="fas fa-medal text-secondary"></i>
                                <?php elseif ($index === 2): ?>
                                    <i class="fas fa-award text-warning"></i>
                                <?php else: ?>
                                    <span class="h5 mb-0 text-warning"><?php echo $index + 1; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 封面 -->
                        <div class="col-auto">
                            <img src="<?php echo htmlspecialchars($item['cover'] ?? ''); ?>"
                                 alt="<?php echo htmlspecialchars($item['title'] ?? ''); ?>"
                                 class="ranking-thumb"
                                 style="width: 120px; height: 80px; object-fit: cover;"
                                 onerror="this.src='/assets/images/placeholder.svg'">
                        </div>

                        <!-- 内容信息 -->
                        <div class="col">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="mb-1">
                                        <a href="/<?php echo $category; ?>/<?php echo $item['id']; ?>"
                                           class="text-warning text-decoration-none">
                                            <?php echo htmlspecialchars($item['title'] ?? ''); ?>
                                        </a>
                                        <?php if (!$item['is_free']): ?>
                                        <span class="badge bg-warning text-dark ms-2">VIP</span>
                                        <?php endif; ?>
                                    </h5>
                                    <p class="text-muted mb-2">
                                        <?php echo htmlspecialchars(mb_substr($item['description'] ?? '', 0, 100) . '...'); ?>
                                    </p>
                                    <div class="ranking-stats">
                                        <span class="me-3">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($item['view_count']); ?>
                                        </span>
                                        <span class="me-3">
                                            <i class="fas fa-heart me-1"></i><?php echo number_format($item['favorite_count']); ?>
                                        </span>
                                        <?php if ($category === 'video'): ?>
                                        <span class="me-3">
                                            <i class="fas fa-clock me-1"></i><?php echo $item['duration_formatted']; ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="me-3">
                                            <i class="fas fa-images me-1"></i><?php echo $item['image_count']; ?>张
                                        </span>
                                        <?php endif; ?>
                                        <span class="text-muted">
                                            <i class="fas fa-calendar me-1"></i><?php echo date('m-d', strtotime($item['created_at'])); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 排行数据 -->
                                <div class="text-end">
                                    <div class="ranking-value">
                                        <?php
                                        switch ($type) {
                                            case 'view':
                                                echo '<i class="fas fa-eye text-primary"></i><br>' . number_format($item['view_count']);
                                                break;
                                            case 'favorite':
                                                echo '<i class="fas fa-heart text-danger"></i><br>' . number_format($item['favorite_count']);
                                                break;
                                            case 'like':
                                                echo '<i class="fas fa-thumbs-up text-success"></i><br>' . number_format($item['like_count']);
                                                break;
                                            case 'download':
                                                echo '<i class="fas fa-download text-info"></i><br>' . number_format($item['download_count']);
                                                break;
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php else: ?>
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">暂无排行数据</h4>
        <p class="text-muted">请稍后再来查看排行榜</p>
    </div>
    <?php endif; ?>
</div>

<style>
.ranking-item {
    transition: transform 0.2s ease;
}

.ranking-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.ranking-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    font-weight: bold;
}

.ranking-number.top-three {
    font-size: 1.5em;
}

.ranking-value {
    text-align: center;
    font-weight: bold;
    color: #ffc107;
}

.ranking-thumb {
    border-radius: 8px;
    border: 2px solid #495057;
}
</style>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 排行榜项目点击事件
    document.querySelectorAll('.ranking-item').forEach(item => {
        item.addEventListener('click', function(e) {
            if (!e.target.closest('a')) {
                const link = this.querySelector('a');
                if (link) {
                    window.location.href = link.href;
                }
            }
        });
    });
});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
