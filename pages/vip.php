<?php
require_once __DIR__ . "/../core/Config.php";
session_start();
require_once __DIR__ . '/../core/Database.php';

// 检查用户登录状态
$isLoggedIn = isset($_SESSION['user_id']);
$userInfo = null;

if ($isLoggedIn) {
    $db = Database::getInstance();
    $userInfo = $db->fetch(
        "SELECT * FROM {$db->getPrefix()}users WHERE id = :id",
        ['id' => $_SESSION['user_id']]
    );
}

// 获取用户组信息（所有启用的用户组，用于显示套餐）
$db = Database::getInstance();
$userGroups = $db->fetchAll("
    SELECT * FROM {$db->getPrefix()}user_groups
    WHERE status = 1 AND group_key != 'free'
    ORDER BY sort_order ASC, duration_days ASC
");

// 获取购买链接映射
$purchaseLinks = $db->fetchAll("
    SELECT group_key, purchase_link FROM {$db->getPrefix()}user_groups
    WHERE status = 1 AND purchase_link IS NOT NULL AND purchase_link != ''
");

// 创建购买链接映射
$linkMap = [];
foreach ($purchaseLinks as $link) {
    $linkMap[$link['group_key']] = $link['purchase_link'];
}

// 定义套餐对应的用户组key（根据实际数据库中的group_key调整）
$packageGroups = [
    1 => 'daily',    // 日费会员
    2 => 'monthly',  // 月费会员
    3 => 'yearly',   // 年费会员
    4 => 'lifetime' // 永久会员
];

// 设置页面信息
$pageTitle = 'VIP会员 - ' . Config::getSiteName();
$pageDescription = '升级VIP会员，享受更多特权和优质内容';

// 页面内容
ob_start();
?>

<div class="container mt-4">
    <!-- VIP会员介绍 -->
    <div class="text-center mb-5">
        <h1 class="text-warning mb-3">
            <i class="fas fa-crown me-2"></i>VIP会员特权
        </h1>
        <p class="lead text-muted">升级VIP会员，解锁全站优质内容，享受专属特权</p>
    </div>

    <?php if ($isLoggedIn && $userInfo): ?>
        <!-- 当前用户状态 -->
        <div class="card bg-dark border-secondary mb-4">
            <div class="card-body text-center">
                <h5 class="text-warning">当前账户状态</h5>
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-muted">用户名</h6>
                        <p class="text-light"><?php echo htmlspecialchars($userInfo['username']); ?></p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">当前积分</h6>
                        <p class="text-warning"><?php echo number_format($userInfo['points']); ?></p>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-muted">会员状态</h6>
                        <p class="text-info">
                            <?php if (!empty($userInfo['group_expire_time']) && strtotime($userInfo['group_expire_time']) > time()): ?>
                                VIP会员 (至<?php echo date('Y-m-d', strtotime($userInfo['group_expire_time'])); ?>)
                            <?php else: ?>
                                普通会员
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- VIP特权介绍 -->
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="card bg-dark border-warning h-100">
                <div class="card-body text-center">
                    <i class="fas fa-eye fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">无限浏览</h5>
                    <p class="text-muted">VIP会员可以无限制浏览所有收费内容，不受积分限制</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-dark border-warning h-100">
                <div class="card-body text-center">
                    <i class="fas fa-download fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">高速下载</h5>
                    <p class="text-muted">享受高速下载通道，无需等待，批量下载</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-dark border-warning h-100">
                <div class="card-body text-center">
                    <i class="fas fa-star fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">专属内容</h5>
                    <p class="text-muted">访问VIP专属内容，享受更多精彩资源</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 移除了重复的VIP套餐选择部分 -->

    <?php if (!$isLoggedIn): ?>
    <!-- 未登录提示 -->
    <div class="text-center mb-4">
        <div class="card bg-dark border-secondary">
            <div class="card-body">
                <h5 class="text-warning">立即登录，享受VIP特权</h5>
                <p class="text-muted">登录后可以查看详细的VIP套餐信息</p>
                <div class="d-flex gap-2 justify-content-center">
                    <a href="/login" class="btn btn-warning" style="color: #333;">
                        <i class="fas fa-sign-in-alt me-2"></i>立即登录
                    </a>
                    <a href="/register" class="btn btn-outline-warning">
                        <i class="fas fa-user-plus me-2"></i>注册账号
                    </a>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 移除了重复的VIP介绍和用户状态部分 -->

    <!-- VIP特权介绍 -->
    <div class="row mb-5">
        <div class="col-md-4 col-sm-6 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-unlock fa-3x text-warning mb-3"></i>
                    <h5 class="card-title text-warning">无限制访问</h5>
                    <p class="card-text text-muted">解锁全站所有VIP专享内容，无任何限制</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 col-sm-6 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-eye-slash fa-3x text-warning mb-3"></i>
                    <h5 class="card-title text-warning">无广告体验</h5>
                    <p class="card-text text-muted">享受纯净无广告的浏览体验</p>
                </div>
            </div>
        </div>

        <div class="col-md-4 col-sm-6 mb-4">
            <div class="card h-100 text-center">
                <div class="card-body">
                    <i class="fas fa-headset fa-3x text-warning mb-3"></i>
                    <h5 class="card-title text-warning">专属客服</h5>
                    <p class="card-text text-muted">享受VIP专属客服支持</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 价格套餐 -->
    <div class="row mb-5" id="pricing">
        <div class="col-12">
            <h3 class="text-warning mb-4 text-center">
                <i class="fas fa-tags me-2"></i>会员套餐
            </h3>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header text-center bg-secondary">
                    <h5 class="mb-0">月度VIP</h5>
                </div>
                <div class="card-body text-center">
                    <div class="price mb-3">
                        <span class="h2 text-warning">¥39</span>
                        <span class="text-muted">/月</span>
                    </div>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP套图</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>高速下载</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>无广告体验</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>专属客服</li>
                    </ul>
                </div>
                <div class="card-footer text-center">
                    <?php if (isset($linkMap[$packageGroups[1]])): ?>
                    <a href="<?php echo htmlspecialchars($linkMap[$packageGroups[1]]); ?>"
                       target="_blank" class="btn btn-outline-warning w-100">
                        立即购买
                    </a>
                    <?php else: ?>
                    <button class="btn btn-outline-warning w-100" onclick="alert('购买链接未配置，请联系管理员')">
                        立即购买
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-warning">
                <div class="card-header text-center bg-warning text-dark">
                    <h5 class="mb-0">年度VIP <span class="badge bg-danger">超值</span></h5>
                </div>
                <div class="card-body text-center">
                    <div class="price mb-3">
                        <span class="h2 text-warning">¥139</span>
                        <span class="text-muted">/年</span>
                        <div class="text-success small">节省¥329</div>
                    </div>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP套图</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>高速下载</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>无广告体验</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>专属客服</li>
                    </ul>
                </div>
                <div class="card-footer text-center">
                    <?php if (isset($linkMap[$packageGroups[2]])): ?>
                    <a href="<?php echo htmlspecialchars($linkMap[$packageGroups[2]]); ?>"
                       target="_blank" class="btn btn-warning w-100">
                        立即购买
                    </a>
                    <?php else: ?>
                    <button class="btn btn-warning w-100" onclick="alert('购买链接未配置，请联系管理员')">
                        立即购买
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-success">
                <div class="card-header text-center bg-success">
                    <h5 class="mb-0 text-white">超级VIP <span class="badge bg-warning text-dark">推荐</span></h5>
                </div>
                <div class="card-body text-center">
                    <div class="price mb-3">
                        <span class="h2 text-warning">¥199</span>
                        <span class="text-muted">/3年</span>
                        <div class="text-success small">节省¥218</div>
                    </div>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP套图</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP视频</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>高速下载</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>无广告体验</li>
                        <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>专属客服</li>
                    </ul>
                </div>
                <div class="card-footer text-center">
                    <?php if (isset($linkMap[$packageGroups[3]])): ?>
                    <a href="<?php echo htmlspecialchars($linkMap[$packageGroups[3]]); ?>"
                       target="_blank" class="btn btn-success w-100">
                        立即购买
                    </a>
                    <?php else: ?>
                    <button class="btn btn-success w-100" onclick="alert('购买链接未配置，请联系管理员')">
                        立即购买
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card h-100 border-danger">
                <div class="card-header text-center bg-danger">
                    <h5 class="mb-0 text-white">永久VIP <span class="badge bg-warning text-dark">至尊</span></h5>
                </div>
                <div class="card-body text-center">
                    <div class="price mb-3">
                        <span class="h2 text-warning">¥499</span>
                        <span class="text-muted">/永久</span>
                        <div class="text-success small">一次购买，终身享受</div>
                    </div>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP套图</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>解锁VIP视频</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>高速打包下载</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>无广告体验</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>专属客服</li>
                        <li class="mb-2"><i class="fas fa-check text-success me-2"></i>超级专属隐藏资源</li>
                    </ul>
                </div>
                <div class="card-footer text-center">
                    <?php if (isset($linkMap[$packageGroups[4]])): ?>
                    <a href="<?php echo htmlspecialchars($linkMap[$packageGroups[4]]); ?>"
                       target="_blank" class="btn btn-danger w-100">
                        立即购买
                    </a>
                    <?php else: ?>
                    <button class="btn btn-danger w-100" onclick="alert('购买链接未配置，请联系管理员')">
                        立即购买
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 常见问题 -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="text-warning mb-4 text-center">
                <i class="fas fa-question-circle me-2"></i>常见问题
            </h3>
        </div>
        
        <div class="col-12">
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item bg-dark border-secondary">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed bg-dark text-light border-0" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                            VIP会员有什么特权？
                        </button>
                    </h2>
                    <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body text-muted">
                            VIP会员可以无限制访问全站所有内容，享受高速下载通道，无广告浏览体验，以及专属客服支持（具体特权以套餐内注明为准）。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item bg-dark border-secondary">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed bg-dark text-light border-0" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                            如何购买VIP会员？
                        </button>
                    </h2>
                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body text-muted">
                            选择合适的套餐后点击"立即购买"按钮，系统会引导您完成支付流程。支持多种支付方式。
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item bg-dark border-secondary">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed bg-dark text-light border-0" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                            VIP会员可以退款吗？
                        </button>
                    </h2>
                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body text-muted">
                            根据相关规定，数字产品一经购买不支持退款。请在购买前仔细考虑。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系客服 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h5 class="text-warning mb-3">
                        <i class="fas fa-headset me-2"></i>需要帮助？
                    </h5>
                    <p class="text-muted mb-3">如有任何问题，请联系我们的客服团队</p>
                    <a href="/contact" class="btn btn-warning">
                        <i class="fas fa-comments me-1"></i>联系客服
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 平滑滚动到价格区域
    const pricingLinks = document.querySelectorAll('a[href=\"#pricing\"]');
    pricingLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('pricing').scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
