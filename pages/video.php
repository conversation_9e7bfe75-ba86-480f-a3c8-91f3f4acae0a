<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Video.php';
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../includes/functions.php';

// 获取视频ID
$videoId = intval($_GET['id'] ?? 0);
if (!$videoId) {
    header('Location: /videos');
    exit;
}

// 初始化视频模型
$videoModel = new Video();
$userId = $_SESSION['user_id'] ?? null;

// 获取视频详情
try {
    $video = $videoModel->getDetailById($videoId, $userId);
} catch (Exception $e) {
    error_log("视频详情加载失败: " . $e->getMessage());
    $video = null;
}

if (!$video) {
    header('HTTP/1.0 404 Not Found');
    include __DIR__ . '/404.php';
    exit;
}

// 处理视频数据
// 注意：数据库中字段名是is_free，不需要转换
$video['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
$video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';

// 检查用户是否可以观看/下载视频
$canWatch = false;
if ($video['is_free']) {
    $canWatch = true; // 免费视频所有人都可以观看
} elseif ($userId) {
    // VIP视频需要检查用户权限
    require_once __DIR__ . '/../models/User.php';
    $userModel = new User();
    $userInfo = $userModel->getUserById($userId);

    if ($userInfo && !empty($userInfo['group_expire_time']) && strtotime($userInfo['group_expire_time']) > time()) {
        $canWatch = true; // VIP用户可以观看
    }
}

// 增加浏览次数
$videoModel->incrementViewCount($video['id'], $userId, $_SERVER['REMOTE_ADDR'] ?? '');

// 处理评论操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_comment') {
    if (!$userId) {
        header('Location: /login');
        exit;
    }

    $comment = trim($_POST['comment'] ?? '');
    if (!empty($comment)) {
        $db = Database::getInstance();
        $result = $db->insert('comments', [
            'user_id' => $userId,
            'content_type' => 'video',
            'content_id' => $video['id'],
            'content' => $comment,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $commentMessage = '评论发表成功';
        } else {
            $commentMessage = '评论发表失败';
        }
    }

    // 重定向回当前页面，避免重复提交
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// 处理收藏操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'toggle_favorite') {
    if (!$userId) {
        header('Location: /login');
        exit;
    }

    $db = Database::getInstance();
    $favoriteExists = $db->fetchColumn(
        "SELECT COUNT(*) FROM {$db->getPrefix()}favorites
         WHERE user_id = :user_id AND content_type = 'video' AND content_id = :content_id",
        ['user_id' => $userId, 'content_id' => $video['id']]
    );

    if ($favoriteExists) {
        // 取消收藏
        $db->delete('favorites',
            'user_id = :user_id AND content_type = :content_type AND content_id = :content_id',
            ['user_id' => $userId, 'content_type' => 'video', 'content_id' => $video['id']]
        );
        $favoriteMessage = '已取消收藏';
    } else {
        // 添加收藏
        $db->insert('favorites', [
            'user_id' => $userId,
            'content_type' => 'video',
            'content_id' => $video['id'],
            'created_at' => date('Y-m-d H:i:s')
        ]);
        $favoriteMessage = '已添加收藏';
    }

    // 重定向回当前页面，避免重复提交
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// 检查收藏状态
$isFavorited = false;
if ($userId) {
    $db = Database::getInstance();
    $favoriteCount = $db->fetchColumn(
        "SELECT COUNT(*) FROM {$db->getPrefix()}favorites
         WHERE user_id = :user_id AND content_type = 'video' AND content_id = :content_id",
        ['user_id' => $userId, 'content_id' => $video['id']]
    );
    $isFavorited = $favoriteCount > 0;
}

// 获取评论列表
$db = Database::getInstance();
$comments = $db->fetchAll(
    "SELECT c.*, u.username, u.nickname, u.avatar
     FROM {$db->getPrefix()}comments c
     LEFT JOIN {$db->getPrefix()}users u ON c.user_id = u.id
     WHERE c.content_type = 'video' AND c.content_id = :content_id AND c.status = 1
     ORDER BY c.created_at DESC",
    ['content_id' => $video['id']]
);

// 获取相关推荐视频
$relatedVideos = [];
if ($video['category_id']) {
    $db = Database::getInstance();
    $relatedVideos = $db->fetchAll(
        "SELECT id, title, cover, view_count, duration, is_free
         FROM {$db->getPrefix()}videos
         WHERE category_id = :category_id AND id != :current_id AND status = 1
         ORDER BY view_count DESC, created_at DESC
         LIMIT 6",
        ['category_id' => $video['category_id'], 'current_id' => $video['id']]
    );

    // 如果同分类的视频不够，补充其他热门视频
    if (count($relatedVideos) < 6) {
        $excludeIds = array_column($relatedVideos, 'id');
        $excludeIds[] = $video['id'];
        $placeholders = str_repeat('?,', count($excludeIds) - 1) . '?';

        $additionalVideos = $db->fetchAll(
            "SELECT id, title, cover, view_count, duration, is_free
             FROM {$db->getPrefix()}videos
             WHERE id NOT IN ($placeholders) AND status = 1
             ORDER BY view_count DESC, created_at DESC
             LIMIT " . (6 - count($relatedVideos)),
            $excludeIds
        );

        $relatedVideos = array_merge($relatedVideos, $additionalVideos);
    }
}

// 设置页面信息
$pageTitle = htmlspecialchars($video['title'] ?? '') . ' - ' . getConfig('site_name', Config::getSiteName());
$pageDescription = htmlspecialchars(mb_substr($video['description'] ?? '', 0, 150));
$pageKeywords = $video['tags'] ?: '视频,在线观看';

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item"><a href="/videos" class="text-warning">视频</a></li>
            <?php if ($video['category_name']): ?>
            <li class="breadcrumb-item"><a href="/videos/cat/<?php echo $video['category_slug']; ?>" class="text-warning"><?php echo htmlspecialchars($video['category_name'] ?? ''); ?></a></li>
            <?php endif; ?>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($video['title'] ?? ''); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- 视频播放区域 -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-body p-0">
                    <?php if ($video['is_free'] || (isset($_SESSION['user_id']) && $_SESSION['can_view_video'])): ?>
                    <!-- 视频播放器 -->
                    <div class="video-player-container">
                        <video controls class="w-100" style="max-height: 500px;" poster="<?php echo htmlspecialchars($video['cover'] ?? ''); ?>">
                            <source src="<?php echo htmlspecialchars($video['video_url'] ?? ''); ?>" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <?php else: ?>
                    <!-- 权限提示 -->
                    <div class="video-locked text-center py-5">
                        <img src="<?php echo htmlspecialchars($video['cover'] ?? ''); ?>" alt="<?php echo htmlspecialchars($video['title'] ?? ''); ?>"
                             class="img-fluid mb-3" style="max-height: 300px; filter: blur(5px);">
                        <div class="overlay-content">
                            <i class="fas fa-lock fa-3x text-warning mb-3"></i>
                            <h4 class="text-warning">VIP专享内容</h4>
                            <p class="text-muted">抱歉，您不是超级VIP会员，暂无权限观看。</p>
                            <p class="text-muted">请升级三年会员或永久会员享受完整视频内容。</p>
                            <div class="mt-3">
                                <?php if (!isset($_SESSION['user_id'])): ?>
                                <a href="/login" class="btn btn-warning me-2" style="color: #333;">
                                    <i class="fas fa-sign-in-alt me-1"></i>立即登录
                                </a>
                                <a href="/register" class="btn btn-outline-warning">
                                    <i class="fas fa-user-plus me-1"></i>注册账号
                                </a>
                                <?php else: ?>
                                <a href="/vip" class="btn btn-warning" style="color: #333;">
                                    <i class="fas fa-crown me-1"></i>立即升级VIP
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 视频信息 -->
            <div class="card">
                <div class="card-body">
                    <!-- 视频标题 -->
                    <div class="mb-3">
                        <h1 class="h3 text-warning mb-2"><?php echo htmlspecialchars($video['title'] ?? ''); ?></h1>
                        <div class="video-meta text-muted">
                            <span class="me-3">
                                <i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?> 次播放
                            </span>
                            <span class="me-3">
                                <i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?>
                            </span>
                            <span class="me-3">
                                <i class="fas fa-calendar me-1"></i><?php echo date('Y-m-d', strtotime($video['created_at'])); ?>
                            </span>
                            <?php if (!$video['is_free']): ?>
                            <span class="badge bg-warning text-dark">VIP</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 操作按钮区域 -->
                    <div class="video-actions-bar mb-4 p-3 bg-dark rounded">
                        <div class="d-flex flex-wrap gap-2 justify-content-center justify-content-md-start">
                            <?php if ($userId): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="toggle_favorite">
                                    <button type="submit" class="btn <?php echo $isFavorited ? 'btn-danger' : 'btn-outline-danger'; ?> btn-sm">
                                        <i class="<?php echo $isFavorited ? 'fas' : 'far'; ?> fa-heart me-1"></i><?php echo $isFavorited ? '已收藏' : '收藏'; ?>
                                    </button>
                                </form>
                            <?php else: ?>
                                <a href="/login" class="btn btn-outline-danger btn-sm">
                                    <i class="far fa-heart me-1"></i>收藏
                                </a>
                            <?php endif; ?>

                            <button class="btn btn-warning btn-sm" style="color: #495057;" onclick="shareVideo()">
                                <i class="fas fa-share me-1"></i>分享
                            </button>

                            <?php if ($canWatch): ?>
                                <button class="btn btn-success btn-sm" onclick="downloadVideo()">
                                    <i class="fas fa-download me-1"></i>下载
                                </button>
                            <?php else: ?>
                                <a href="/vip" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-download me-1"></i>下载 (需VIP)
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 视频描述 -->
                    <?php if ($video['description']): ?>
                    <div class="video-description">
                        <h6 class="text-warning">视频简介</h6>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($video['description'] ?? '')); ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- 标签 -->
                    <?php if (!empty($video['tags_array'])): ?>
                    <div class="video-tags mt-3">
                        <h6 class="text-warning">相关标签</h6>
                        <?php foreach ($video['tags_array'] as $tag): ?>
                        <span class="badge bg-secondary me-1 mb-1"><?php echo htmlspecialchars(trim($tag ?? '')); ?></span>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 评论区 -->
            <div class="card">
                <div class="card-body">
                    <h5 class="text-light">评论 <span class="text-muted">(<?php echo count($comments); ?>)</span></h5>

                    <?php if ($userId): ?>
                    <form class="comment-form mb-4" method="POST">
                        <input type="hidden" name="action" value="add_comment">
                        <div class="mb-3">
                            <textarea class="form-control" name="comment" rows="3" placeholder="写下你的评论..." required></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">支持 Markdown 语法</small>
                            <button type="submit" class="btn btn-warning" style="color: #333;">发表评论</button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">登录后可以发表评论</p>
                        <a href="/login" class="btn btn-warning" style="color: #333;">立即登录</a>
                    </div>
                    <?php endif; ?>

                    <div class="comments-list">
                        <?php if (!empty($comments)): ?>
                            <?php foreach ($comments as $comment): ?>
                            <div class="comment-item border-bottom border-secondary pb-3 mb-3">
                                <div class="d-flex">
                                    <img src="<?php echo htmlspecialchars($comment['avatar'] ?: '/assets/images/default-avatar.png'); ?>"
                                         alt="<?php echo htmlspecialchars($comment['nickname'] ?: $comment['username']); ?>"
                                         class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="text-warning mb-0"><?php echo htmlspecialchars($comment['nickname'] ?: $comment['username']); ?></h6>
                                            <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($comment['created_at'])); ?></small>
                                        </div>
                                        <p class="text-light mb-0"><?php echo nl2br(htmlspecialchars($comment['content'])); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>暂无评论，来抢沙发吧~</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 分集列表 -->
            <?php if (!empty($video['episodes']) && count($video['episodes']) > 1): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-warning">
                        <i class="fas fa-list me-2"></i>分集列表 (共<?php echo count($video['episodes']); ?>集)
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="episode-list">
                        <?php foreach ($video['episodes'] as $episode): ?>
                        <div class="episode-item p-3 border-bottom border-secondary">
                            <div class="d-flex">
                                <img src="<?php echo htmlspecialchars($episode['cover'] ?? ''); ?>"
                                     alt="第<?php echo $episode['episode_number']; ?>集"
                                     class="episode-thumb me-3"
                                     style="width: 80px; height: 60px; object-fit: cover;">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 text-light">第<?php echo $episode['episode_number']; ?>集</h6>
                                    <p class="text-muted small mb-1"><?php echo htmlspecialchars($episode['title'] ?? ''); ?></p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i><?php echo $episode['duration_formatted']; ?>
                                        <i class="fas fa-play ms-2 me-1"></i><?php echo number_format($episode['view_count']); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 相关推荐 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0 text-warning">
                        <i class="fas fa-thumbs-up me-2"></i>相关推荐
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($relatedVideos)): ?>
                        <?php foreach ($relatedVideos as $relatedVideo): ?>
                        <div class="d-flex mb-3 pb-3 border-bottom border-secondary">
                            <div class="position-relative me-3">
                                <img src="<?php echo htmlspecialchars($relatedVideo['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                                     alt="<?php echo htmlspecialchars($relatedVideo['title']); ?>"
                                     class="rounded" style="width: 80px; height: 60px; object-fit: cover;">
                                <?php if (!$relatedVideo['is_free']): ?>
                                <span class="position-absolute top-0 end-0 badge bg-warning text-dark" style="font-size: 0.6rem;">VIP</span>
                                <?php endif; ?>
                                <?php if ($relatedVideo['duration']): ?>
                                <span class="position-absolute bottom-0 end-0 bg-dark text-white px-1" style="font-size: 0.6rem; border-radius: 2px;">
                                    <?php echo gmdate("i:s", $relatedVideo['duration']); ?>
                                </span>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 text-light">
                                    <a href="/video/<?php echo $relatedVideo['id']; ?>" class="text-light text-decoration-none">
                                        <?php echo htmlspecialchars($relatedVideo['title']); ?>
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i><?php echo number_format($relatedVideo['view_count']); ?>次观看
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center">暂无相关推荐</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 视频播放器事件
    const video = document.querySelector('video');
    if (video) {
        video.addEventListener('loadstart', function() {
            console.log('视频开始加载');
        });
        
        video.addEventListener('error', function() {
            console.error('视频加载失败');
        });
    }
    
    // 分集切换
    document.querySelectorAll('.episode-item').forEach(item => {
        item.addEventListener('click', function() {
            // 这里可以添加分集切换逻辑
            console.log('切换分集');
        });
    });

    // 收藏功能现在使用表单提交，不需要JavaScript
});

// 分享功能
function shareVideo() {
    console.log('分享按钮被点击');
    const title = document.querySelector('h1').textContent;
    const url = window.location.href;
    console.log('标题:', title, '链接:', url);

    if (navigator.share) {
        // 使用原生分享API
        navigator.share({
            title: title,
            url: url
        }).catch(err => console.log('分享失败:', err));
    } else {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showShareToast('链接已复制到剪贴板');
            }).catch(() => {
                showShareDialog(title, url);
            });
        } else {
            showShareDialog(title, url);
        }
    }
}

// 显示分享对话框
function showShareDialog(title, url) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML =
        '<div class=\"modal-dialog\">' +
            '<div class=\"modal-content bg-dark border-secondary\">' +
                '<div class=\"modal-header border-secondary\">' +
                    '<h5 class=\"modal-title text-light\">分享视频</h5>' +
                    '<button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\"></button>' +
                '</div>' +
                '<div class=\"modal-body\">' +
                    '<p class=\"text-light mb-3\">' + title + '</p>' +
                    '<div class=\"input-group mb-3\">' +
                        '<input type=\"text\" class=\"form-control\" value=\"' + url + '\" id=\"shareUrl\" readonly>' +
                        '<button class=\"btn btn-warning\" type=\"button\" onclick=\"copyShareUrl()\" style=\"color: #495057;\">' +
                            '<i class=\"fas fa-copy\"></i> 复制' +
                        '</button>' +
                    '</div>' +
                    '<div class=\"d-flex gap-2\">' +
                        '<a href=\"https://www.weibo.com/share/share.php?url=' + encodeURIComponent(url) + '&title=' + encodeURIComponent(title) + '\" ' +
                           'target=\"_blank\" class=\"btn btn-outline-warning btn-sm\">' +
                            '<i class=\"fab fa-weibo\"></i> 微博' +
                        '</a>' +
                        '<a href=\"https://connect.qq.com/widget/shareqq/index.html?url=' + encodeURIComponent(url) + '&title=' + encodeURIComponent(title) + '\" ' +
                           'target=\"_blank\" class=\"btn btn-outline-warning btn-sm\">' +
                            '<i class=\"fab fa-qq\"></i> QQ' +
                        '</a>' +
                    '</div>' +
                '</div>' +
            '</div>' +
        '</div>';

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// 复制分享链接
function copyShareUrl() {
    const input = document.getElementById('shareUrl');
    input.select();
    document.execCommand('copy');
    showShareToast('链接已复制到剪贴板');
}

// 显示分享提示
function showShareToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    toast.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 12px 20px; border-radius: 4px; background-color: #28a745; color: white; z-index: 9999; transition: opacity 0.3s;';

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 下载视频功能
function downloadVideo() {
    const videoUrl = '" . addslashes($video['video_url'] ?? '') . "';
    const videoTitle = '" . addslashes($video['title'] ?? '') . "';

    if (!videoUrl) {
        showShareToast('视频文件不存在');
        return;
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = videoTitle + '.mp4';
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showShareToast('开始下载视频...');
}
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
