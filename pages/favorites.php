<?php
require_once __DIR__ . "/../core/Config.php";
session_start();

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = '';

// 处理取消收藏
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'remove_favorite') {
    $contentId = intval($_POST['content_id']);
    $contentType = $_POST['content_type'] ?? 'album';
    
    $result = $db->delete('favorites',
        'user_id = :user_id AND target_type = :target_type AND target_id = :target_id',
        [
            'user_id' => $_SESSION['user_id'],
            'target_type' => $contentType,
            'target_id' => $contentId
        ]
    );
    
    if ($result) {
        $message = '取消收藏成功';
        $messageType = 'success';
    } else {
        $message = '取消收藏失败';
        $messageType = 'danger';
    }
}

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$contentType = $_GET['type'] ?? 'album';
$limit = 24;
$offset = ($page - 1) * $limit;

// 获取收藏列表
if ($contentType === 'album') {
    $favorites = $db->fetchAll("
        SELECT a.*, c.name as category_name, f.created_at as favorited_at
        FROM {$db->getPrefix()}favorites f
        JOIN {$db->getPrefix()}albums a ON f.target_id = a.id
        LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
        WHERE f.user_id = :user_id AND f.target_type = 'album'
        ORDER BY f.created_at DESC
        LIMIT $limit OFFSET $offset
    ", ['user_id' => $_SESSION['user_id']]);
} else {
    $favorites = $db->fetchAll("
        SELECT v.*, c.name as category_name, f.created_at as favorited_at
        FROM {$db->getPrefix()}favorites f
        JOIN {$db->getPrefix()}videos v ON f.target_id = v.id
        LEFT JOIN {$db->getPrefix()}categories c ON v.category_id = c.id
        WHERE f.user_id = :user_id AND f.target_type = 'video'
        ORDER BY f.created_at DESC
        LIMIT $limit OFFSET $offset
    ", ['user_id' => $_SESSION['user_id']]);
}

// 获取总数
$total = $db->fetchColumn("
    SELECT COUNT(*) FROM {$db->getPrefix()}favorites
    WHERE user_id = :user_id AND target_type = :target_type
", [
    'user_id' => $_SESSION['user_id'],
    'target_type' => $contentType
]) ?: 0;

$totalPages = ceil($total / $limit);

// 获取统计信息
$albumCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}favorites WHERE user_id = :user_id AND target_type = 'album'", ['user_id' => $_SESSION['user_id']]) ?: 0;
$videoCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}favorites WHERE user_id = :user_id AND target_type = 'video'", ['user_id' => $_SESSION['user_id']]) ?: 0;

// 设置页面信息
$pageTitle = '我的收藏 - ' . Config::getSiteName();
$pageDescription = '查看我收藏的套图和视频';

// 页面内容
ob_start();
?>

<div class="container mt-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item"><a href="/profile" class="text-warning">个人中心</a></li>
            <li class="breadcrumb-item active">我的收藏</li>
        </ol>
    </nav>
    
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- 页面标题和统计 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-warning">
            <i class="fas fa-heart me-2"></i>我的收藏
        </h2>
        <div class="text-muted">
            共收藏 <span class="text-warning"><?php echo number_format($albumCount + $videoCount); ?></span> 个内容
        </div>
    </div>
    
    <!-- 内容类型切换 -->
    <div class="card bg-dark border-secondary mb-4">
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="?type=album" class="btn <?php echo $contentType === 'album' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                    <i class="fas fa-images me-2"></i>套图收藏
                    <span class="badge bg-secondary ms-2"><?php echo $albumCount; ?></span>
                </a>
                <a href="?type=video" class="btn <?php echo $contentType === 'video' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                    <i class="fas fa-video me-2"></i>视频收藏
                    <span class="badge bg-secondary ms-2"><?php echo $videoCount; ?></span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- 收藏内容 -->
    <div class="card bg-dark border-secondary">

        <div class="card-body">
            <?php if (empty($favorites)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-heart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无收藏内容</h5>
                    <p class="text-muted">
                        去发现更多精彩的
                        <?php if ($contentType === 'album'): ?>
                            <a href="/albums" class="text-warning">套图内容</a>
                        <?php else: ?>
                            <a href="/videos" class="text-warning">视频内容</a>
                        <?php endif; ?>
                        吧
                    </p>
                </div>
            <?php else: ?>
                <div class="row g-3">
                    <?php foreach ($favorites as $favorite): ?>
                        <div class="col-xl-2 col-lg-3 col-md-4 col-6">
                            <div class="card bg-secondary h-100">
                                <div class="position-relative">
                                    <a href="/<?php echo $contentType; ?>/<?php echo htmlspecialchars($favorite['slug']); ?>">
                                        <img src="<?php echo htmlspecialchars($favorite['cover'] ?? $favorite['thumbnail'] ?? ''); ?>"
                                             class="card-img-top" alt="<?php echo htmlspecialchars($favorite['title'] ?? ''); ?>"
                                             style="height: 200px; object-fit: cover;">
                                    </a>
                                    <div class="position-absolute top-0 end-0 p-2">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="remove_favorite">
                                            <input type="hidden" name="content_type" value="<?php echo $contentType; ?>">
                                            <input type="hidden" name="content_id" value="<?php echo $favorite['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger" 
                                                    onclick="return confirm('确定要取消收藏吗？')" title="取消收藏">
                                                <i class="fas fa-heart-broken"></i>
                                            </button>
                                        </form>
                                    </div>
                                    <?php if ($contentType === 'video' && !empty($favorite['duration'])): ?>
                                        <div class="position-absolute bottom-0 end-0 bg-dark bg-opacity-75 text-white px-2 py-1 small">
                                            <?php echo gmdate('H:i:s', $favorite['duration']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-body p-2">
                                    <h6 class="card-title text-light mb-1">
                                        <a href="/<?php echo $contentType; ?>/<?php echo htmlspecialchars($favorite['slug']); ?>" 
                                           class="text-decoration-none text-light" title="<?php echo htmlspecialchars($favorite['title']); ?>">
                                            <?php echo htmlspecialchars(mb_substr($favorite['title'], 0, 30)); ?>
                                        </a>
                                    </h6>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <?php echo date('Y-m-d', strtotime($favorite['favorited_at'])); ?>
                                        </small>
                                        <?php if (!empty($favorite['category_name'])): ?>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($favorite['category_name']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($favorite['view_count']); ?>
                                            <?php if ($contentType === 'album' && !empty($favorite['image_count'])): ?>
                                                <i class="fas fa-images ms-2 me-1"></i><?php echo $favorite['image_count']; ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <nav class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $page - 1; ?>">上一页</a>
                </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                </li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $page + 1; ?>">下一页</a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
