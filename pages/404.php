<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../includes/functions.php';

// 404页面
$pageTitle = '页面未找到 - <?php echo Config::getSiteName(); ?>';

// 页面内容
ob_start();
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="text-center py-5">
                <!-- 404图标 -->
                <div class="mb-4">
                    <div class="error-404">
                        <span class="error-number">404</span>
                        <i class="fas fa-search error-icon"></i>
                    </div>
                </div>
                
                <!-- 错误信息 -->
                <h2 class="h4 mb-3">页面未找到</h2>
                <p class="text-muted mb-4">
                    抱歉，您访问的页面不存在或已被删除。<br>
                    可能是网址输入错误，或者内容已经移动到了其他位置。
                </p>
                
                <!-- 搜索框 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-search me-2"></i>搜索内容
                        </h6>
                        <form action="/albums" method="GET" class="d-flex gap-2">
                            <input type="text" name="keyword" class="form-control" 
                                   placeholder="输入关键词搜索套图..." required>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- 推荐链接 -->
                <div class="row g-3 mb-4">
                    <div class="col-6 col-md-3">
                        <a href="/" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-home d-block mb-1"></i>
                            <small>首页</small>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="/albums" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-images d-block mb-1"></i>
                            <small>套图</small>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="/videos" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-video d-block mb-1"></i>
                            <small>视频</small>
                        </a>
                    </div>
                    <div class="col-6 col-md-3">
                        <a href="/categories" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-tags d-block mb-1"></i>
                            <small>分类</small>
                        </a>
                    </div>
                </div>
                
                <!-- 热门内容 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-fire me-2"></i>热门推荐
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <!-- 这里可以添加热门内容，暂时显示占位内容 -->
                            <div class="col-6 col-md-4">
                                <div class="card">
                                    <img src="/assets/images/placeholder.svg" class="card-img-top" alt="热门套图">
                                    <div class="card-body p-2">
                                        <small class="card-title">热门套图 1</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-4">
                                <div class="card">
                                    <img src="/assets/images/placeholder.svg" class="card-img-top" alt="热门套图">
                                    <div class="card-body p-2">
                                        <small class="card-title">热门套图 2</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 col-md-4">
                                <div class="card">
                                    <img src="/assets/images/placeholder.svg" class="card-img-top" alt="热门套图">
                                    <div class="card-body p-2">
                                        <small class="card-title">热门套图 3</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <a href="/albums" class="btn btn-warning btn-sm">查看更多</a>
                        </div>
                    </div>
                </div>
                
                <!-- 帮助信息 -->
                <div class="mt-4">
                    <small class="text-muted">
                        如果问题持续存在，请 
                        <a href="/contact" class="text-decoration-none">联系我们</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-404 {
    position: relative;
    display: inline-block;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    color: #ffc107;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.error-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #6c757d;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}
</style>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
// 自动对焦搜索框
document.querySelector('input[name=\"keyword\"]').focus();

// 随机显示励志文案
const messages = [
    '没关系，我们都会迷路，重要的是找到回家的路。',
    '每一次错误都是新发现的开始。',
    '山重水复疑无路，柳暗花明又一村。',
    '不要因为走得太远，而忘记为什么出发。'
];

const randomMessage = messages[Math.floor(Math.random() * messages.length)];
setTimeout(() => {
    const messageEl = document.createElement('div');
    messageEl.className = 'alert alert-info mt-3';
    messageEl.innerHTML = '<i class=\"fas fa-quote-left me-2\"></i>' + randomMessage;
    document.querySelector('.container .col-md-8').appendChild(messageEl);
}, 2000);
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
