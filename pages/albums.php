<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../includes/functions.php';

$albumModel = new Album();
$db = Database::getInstance();

// 获取分类信息
$categorySlug = $_GET['category'] ?? '';
$categoryId = 0;
$categoryName = '全部套图';

if ($categorySlug) {
    $category = $db->fetch(
        "SELECT * FROM {$db->getPrefix()}categories WHERE slug = :slug AND type = 'album' AND status = 1",
        ['slug' => $categorySlug]
    );
    
    if ($category) {
        $categoryId = $category['id'];
        $categoryName = $category['name'];
    }
}

// 获取筛选参数
$params = [
    'page' => max(1, intval($_GET['page'] ?? 1)),
    'page_size' => 20,
    'order' => $_GET['order'] ?? 'latest',
    'keyword' => trim($_GET['keyword'] ?? ''),
];

if ($categoryId) {
    $params['category_id'] = $categoryId;
}

// 免费/付费筛选
if (isset($_GET['free'])) {
    $params['is_free'] = $_GET['free'] == '1' ? 1 : 0;
}

// 获取套图列表
$result = $albumModel->getList($params);

// 获取所有分类
$categories = $db->fetchAll(
    "SELECT * FROM {$db->getPrefix()}categories WHERE type = 'album' AND status = 1 ORDER BY sort ASC"
);

// 设置页面信息
$siteName = Config::getSiteName();
$pageTitle = $categoryName . ' - ' . $siteName;
if ($params['keyword']) {
    $pageTitle = "搜索: {$params['keyword']} - {$siteName}";
}
$pageDescription = "浏览精美的{$categoryName}，高质量美女图片，多种筛选方式";
$pageCSS = ['/assets/css/albums.css?v=' . time()];

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 页面标题和筛选 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2 class="text-warning mb-0">
                    <i class="fas fa-images me-2"></i><?php echo htmlspecialchars($categoryName); ?>
                    <small class="text-muted">（共 <?php echo number_format($result['total']); ?> 套）</small>
                </h2>
                
                <!-- 视图切换 -->
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-warning active" onclick="toggleView('grid')">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="toggleView('list')">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            
            <!-- 筛选条件 -->
            <div class="card">
                <div class="card-body">
                    <form method="GET" id="filterForm" class="row g-3">
                        <?php if ($categorySlug): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($categorySlug); ?>">
                        <?php endif; ?>
                        
                        <!-- 分类筛选 -->
                        <div class="col-md-3">
                            <label class="form-label">分类</label>
                            <select name="category" class="form-select" onchange="updateCategory()">
                                <option value="">全部分类</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['slug']; ?>" <?php echo $cat['id'] == $categoryId ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name'] ?? ''); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- 免费/付费筛选 -->
                        <div class="col-md-2">
                            <label class="form-label">类型</label>
                            <select name="free" class="form-select">
                                <option value="">全部</option>
                                <option value="1" <?php echo ($_GET['free'] ?? '') === '1' ? 'selected' : ''; ?>>免费</option>
                                <option value="0" <?php echo ($_GET['free'] ?? '') === '0' ? 'selected' : ''; ?>>VIP</option>
                            </select>
                        </div>
                        
                        <!-- 排序方式 -->
                        <div class="col-md-2">
                            <label class="form-label">排序</label>
                            <select name="order" class="form-select">
                                <option value="latest" <?php echo $params['order'] === 'latest' ? 'selected' : ''; ?>>最新发布</option>
                                <option value="view" <?php echo $params['order'] === 'view' ? 'selected' : ''; ?>>浏览最多</option>
                                <option value="like" <?php echo $params['order'] === 'like' ? 'selected' : ''; ?>>点赞最多</option>
                                <option value="favorite" <?php echo $params['order'] === 'favorite' ? 'selected' : ''; ?>>收藏最多</option>
                                <option value="download" <?php echo $params['order'] === 'download' ? 'selected' : ''; ?>>下载最多</option>
                            </select>
                        </div>
                        
                        <!-- 搜索关键词 -->
                        <div class="col-md-3">
                            <label class="form-label">搜索</label>
                            <input type="text" name="keyword" class="form-control" 
                                   value="<?php echo htmlspecialchars($params['keyword'] ?? ''); ?>"
                                   placeholder="输入关键词搜索">
                        </div>
                        
                        <!-- 搜索按钮 -->
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-search me-1"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 套图列表 -->
    <div class="row">
        <div class="col-12">
            <?php if (empty($result['list'])): ?>
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-search fa-4x text-muted"></i>
                </div>
                <h5 class="text-muted">没有找到相关套图</h5>
                <p class="text-muted">尝试更换筛选条件或关键词</p>
                <a href="/albums" class="btn btn-warning">浏览全部套图</a>
            </div>
            <?php else: ?>
            
            <div id="albumGrid" class="album-grid">
                <?php foreach ($result['list'] as $album): ?>
                <div class="album-card" data-id="<?php echo $album['id']; ?>">
                    <a href="/album/<?php echo $album['id']; ?>" class="card-link-overlay"></a>
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($album['cover'] ?: '/assets/images/placeholder.php?w=300&h=400&text=' . urlencode($album['title'] ?? '')); ?>"
                             alt="<?php echo htmlspecialchars($album['title'] ?? ''); ?>"
                             class="img-fluid" loading="lazy">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?></span>
                                    <span><i class="fas fa-images me-1"></i><?php echo $album['image_count']; ?>张</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- VIP标识 -->
                        <?php if (!$album['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                        
                        <!-- 操作按钮 -->
                        <div class="position-absolute top-0 start-0 m-2" style="z-index: 10;">
                            <div class="btn-group-vertical btn-group-sm">
                                <button class="btn btn-dark btn-sm opacity-75 btn-favorite"
                                        data-type="album" data-id="<?php echo $album['id']; ?>"
                                        title="收藏">
                                    <i class="far fa-heart"></i>
                                </button>
                                <button class="btn btn-dark btn-sm opacity-75 btn-like"
                                        data-type="album" data-id="<?php echo $album['id']; ?>"
                                        title="点赞">
                                    <i class="far fa-thumbs-up"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="card-title">
                            <?php echo htmlspecialchars($album['title'] ?? ''); ?>
                        </div>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($album['description'] ?? '', 0, 60) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">
                                    <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($album['category_name'] ?? '未分类'); ?>
                                </span>
                                <small class="text-muted">
                                    <?php
                                    $dateTime = $album['published_at'] ?? $album['created_at'] ?? date('Y-m-d H:i:s');
                                    echo date('m-d H:i', strtotime($dateTime));
                                    ?>
                                </small>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span class="text-muted">
                                    <i class="fas fa-heart me-1"></i><span class="count"><?php echo number_format($album['favorite_count']); ?></span>
                                </span>
                                <span class="text-muted">
                                    <i class="fas fa-download me-1"></i><?php echo number_format($album['download_count']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- 分页导航 -->
            <?php if ($result['total_pages'] > 1): ?>
            <nav aria-label="套图分页" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php
                    $currentPage = $result['page'];
                    $totalPages = $result['total_pages'];
                    $baseUrl = $_SERVER['REQUEST_URI'];
                    $baseUrl = preg_replace('/[?&]page=\d+/', '', $baseUrl);
                    $separator = strpos($baseUrl, '?') !== false ? '&' : '?';
                    ?>
                    
                    <!-- 上一页 -->
                    <?php if ($currentPage > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo $baseUrl . $separator . 'page=' . ($currentPage - 1); ?>">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <!-- 页码 -->
                    <?php
                    $start = max(1, $currentPage - 2);
                    $end = min($totalPages, $currentPage + 2);
                    
                    if ($start > 1) {
                        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . $separator . 'page=1">1</a></li>';
                        if ($start > 2) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                    }
                    
                    for ($i = $start; $i <= $end; $i++) {
                        $active = $i == $currentPage ? 'active' : '';
                        echo '<li class="page-item ' . $active . '">';
                        echo '<a class="page-link" href="' . $baseUrl . $separator . 'page=' . $i . '">' . $i . '</a>';
                        echo '</li>';
                    }
                    
                    if ($end < $totalPages) {
                        if ($end < $totalPages - 1) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                        echo '<li class="page-item"><a class="page-link" href="' . $baseUrl . $separator . 'page=' . $totalPages . '">' . $totalPages . '</a></li>';
                    }
                    ?>
                    
                    <!-- 下一页 -->
                    <?php if ($currentPage < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo $baseUrl . $separator . 'page=' . ($currentPage + 1); ?>">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
            
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
// 更新分类
function updateCategory() {
    const categorySelect = document.querySelector('[name=\"category\"]');
    const categoryValue = categorySelect.value;
    
    if (categoryValue) {
        window.location.href = '/albums/cat/' + categoryValue;
    } else {
        window.location.href = '/albums';
    }
}

// 切换视图
function toggleView(view) {
    const gridView = document.getElementById('albumGrid');
    const buttons = document.querySelectorAll('.btn-group button');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    if (view === 'list') {
        gridView.className = 'row';
        // 修改每个卡片为列表样式
        document.querySelectorAll('.album-card').forEach(card => {
            card.className = 'col-12 mb-3';
            card.innerHTML = card.innerHTML; // 触发重新渲染
        });
    } else {
        gridView.className = 'album-grid';
        // 恢复网格样式
        document.querySelectorAll('.album-card').forEach(card => {
            card.className = 'album-card';
        });
    }
}

// 无限滚动加载
let loading = false;
let hasMore = " . ($result['page'] < $result['total_pages'] ? 'true' : 'false') . ";

window.addEventListener('scroll', function() {
    if (loading || !hasMore) return;
    
    const scrollHeight = document.documentElement.scrollHeight;
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    const clientHeight = document.documentElement.clientHeight;
    
    if (scrollTop + clientHeight >= scrollHeight - 100) {
        loadMoreAlbums();
    }
});

function loadMoreAlbums() {
    if (loading || !hasMore) return;
    
    loading = true;
    const currentPage = " . $result['page'] . ";
    const nextPage = currentPage + 1;
    
    // 显示加载指示器
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'text-center my-4';
    loadingDiv.innerHTML = '<div class=\"loading\"></div> 加载中...';
    document.getElementById('albumGrid').appendChild(loadingDiv);
    
    // 构建URL
    const url = new URL(window.location.href);
    url.searchParams.set('page', nextPage);
    
    fetch(url.toString())
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newCards = doc.querySelectorAll('.album-card');
            
            if (newCards.length === 0) {
                hasMore = false;
                loadingDiv.innerHTML = '<small class=\"text-muted\">没有更多内容了</small>';
            } else {
                newCards.forEach(card => {
                    document.getElementById('albumGrid').appendChild(card);
                });
                loadingDiv.remove();
            }
            
            loading = false;
        })
        .catch(error => {
            console.error('Error:', error);
            loadingDiv.innerHTML = '<small class=\"text-danger\">加载失败，请重试</small>';
            loading = false;
        });
}

// 卡片链接覆盖层样式
.card-link-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    text-decoration: none !important;
}

.album-card {
    position: relative;
}

.album-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
}
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
