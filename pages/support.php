<?php
session_start();
require_once __DIR__ . "/../core/Config.php";

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login?redirect=' . urlencode('/support'));
    exit;
}

$siteName = Config::getSiteName();
$pageTitle = "客服工单 - {$siteName}";
$pageDescription = "提交客服工单，获取专业技术支持";
$pageKeywords = "客服工单,技术支持,在线客服";

// 获取用户信息
$userId = $_SESSION['user_id'];
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../core/Database.php';

$userModel = new User();
$userInfo = $userModel->getUserById($userId);
$db = Database::getInstance();

// 如果用户不存在，重定向到登录页
if (!$userInfo) {
    unset($_SESSION['user_id']);
    header('Location: /login?redirect=' . urlencode('/support'));
    exit;
}

// 获取当前操作
$action = $_GET['action'] ?? 'list';
$ticketId = intval($_GET['id'] ?? 0);

// 处理成功/错误消息
$successMessage = '';
$errorMessage = '';
if (isset($_GET['success'])) {
    $successMessage = '工单提交成功，我们会尽快处理您的问题';
}
if (isset($_GET['error'])) {
    $errorMessage = htmlspecialchars($_GET['error']);
}

// 获取用户的工单列表
$userTickets = $db->fetchAll("
    SELECT f.*,
           (SELECT COUNT(*) FROM {$db->getPrefix()}feedback_replies r WHERE r.feedback_id = f.id) as reply_count,
           (SELECT MAX(r.created_at) FROM {$db->getPrefix()}feedback_replies r WHERE r.feedback_id = f.id) as last_reply_time
    FROM {$db->getPrefix()}feedback f
    WHERE f.user_id = :user_id
    ORDER BY f.created_at DESC
", ['user_id' => $userId]) ?: [];

// 如果是查看详情，获取工单详情和回复
$ticketDetail = null;
$ticketReplies = [];
if ($action === 'view' && $ticketId > 0) {
    $ticketDetail = $db->fetch("
        SELECT * FROM {$db->getPrefix()}feedback
        WHERE id = :id AND user_id = :user_id
    ", ['id' => $ticketId, 'user_id' => $userId]);

    if ($ticketDetail) {
        $ticketReplies = $db->fetchAll("
            SELECT r.*,
                   u.username, u.nickname,
                   a.username as admin_username
            FROM {$db->getPrefix()}feedback_replies r
            LEFT JOIN {$db->getPrefix()}users u ON r.user_id = u.id
            LEFT JOIN {$db->getPrefix()}admin_users a ON r.admin_id = a.id
            WHERE r.feedback_id = :feedback_id AND r.is_internal = 0
            ORDER BY r.created_at ASC
        ", ['feedback_id' => $ticketId]) ?: [];
    }
}

ob_start();
?>

<div class="container py-5">
    <?php if ($action === 'list'): ?>
    <!-- 工单列表页面 -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-warning mb-0">
                    <i class="fas fa-headset me-2"></i>我的工单
                </h2>
                <a href="/support?action=create" class="btn btn-warning">
                    <i class="fas fa-plus me-2"></i>提交新工单
                </a>
            </div>

            <?php if ($successMessage): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
            </div>
            <?php endif; ?>

            <?php if ($errorMessage): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
            </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <?php if (empty($userTickets)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无工单</h5>
                        <p class="text-muted">您还没有提交过工单</p>
                        <a href="/support?action=create" class="btn btn-warning">
                            <i class="fas fa-plus me-2"></i>提交第一个工单
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>工单ID</th>
                                    <th>主题</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>回复数</th>
                                    <th>最后更新</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($userTickets as $ticket): ?>
                                <tr>
                                    <td>#<?php echo $ticket['id']; ?></td>
                                    <td>
                                        <strong><?php echo htmlspecialchars(mb_substr($ticket['message'], 0, 50) . '...'); ?></strong><br>
                                        <small class="text-muted">
                                            <?php
                                            $subjectText = [
                                                'account' => '账户问题',
                                                'payment' => '支付问题',
                                                'content' => '内容问题',
                                                'technical' => '技术问题',
                                                'feature' => '功能建议',
                                                'bug' => 'Bug反馈',
                                                'other' => '其他问题'
                                            ];
                                            echo $subjectText[$ticket['subject']] ?? $ticket['subject'];
                                            ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $priorityClass = [
                                            'low' => 'success',
                                            'medium' => 'warning',
                                            'high' => 'danger'
                                        ];
                                        $priorityText = [
                                            'low' => '低',
                                            'medium' => '中',
                                            'high' => '高'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $priorityClass[$ticket['priority']]; ?>">
                                            <?php echo $priorityText[$ticket['priority']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'resolved' => 'success',
                                            'closed' => 'secondary'
                                        ];
                                        $statusText = [
                                            'pending' => '待处理',
                                            'processing' => '处理中',
                                            'resolved' => '已解决',
                                            'closed' => '已关闭'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $statusClass[$ticket['status']]; ?>">
                                            <?php echo $statusText[$ticket['status']]; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($ticket['reply_count'] > 0): ?>
                                            <span class="badge bg-primary"><?php echo $ticket['reply_count']; ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php
                                        $lastTime = $ticket['last_reply_time'] ?: $ticket['created_at'];
                                        echo date('Y-m-d H:i', strtotime($lastTime));
                                        ?>
                                    </td>
                                    <td>
                                        <a href="/support?action=view&id=<?php echo $ticket['id']; ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>查看
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'create'): ?>
    <!-- 创建工单页面 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="text-warning mb-0">
                            <i class="fas fa-headset me-2"></i>提交工单
                        </h2>
                        <a href="/support" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回列表
                        </a>
                    </div>
                    <p class="text-muted mb-0 mt-2">提交技术支持工单，我们将为您提供专业服务</p>
                </div>
                <div class="card-body">
                    <?php if ($successMessage): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($errorMessage): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="post" action="/api/contact.php">
                        <!-- 用户信息（只读） -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">用户昵称</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="<?php echo htmlspecialchars($userInfo['nickname'] ?: $userInfo['username'] ?: ''); ?>" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="<?php echo htmlspecialchars($userInfo['email'] ?: ''); ?>" readonly>
                            </div>
                        </div>
                        
                        <!-- 工单类型 -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">工单类型 *</label>
                            <select class="form-select" id="subject" name="subject" required>
                                <option value="">请选择工单类型</option>
                                <option value="account">账户问题</option>
                                <option value="payment">支付问题</option>
                                <option value="content">内容问题</option>
                                <option value="technical">技术问题</option>
                                <option value="feature">功能建议</option>
                                <option value="bug">Bug反馈</option>
                                <option value="other">其他问题</option>
                            </select>
                        </div>
                        
                        <!-- 问题描述 -->
                        <div class="mb-3">
                            <label for="message" class="form-label">问题描述 *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required 
                                      placeholder="请详细描述您遇到的问题，包括：&#10;1. 问题发生的具体情况&#10;2. 您期望的结果&#10;3. 相关的错误信息（如有）&#10;4. 其他有助于我们解决问题的信息"></textarea>
                        </div>
                        
                        <!-- 优先级 -->
                        <div class="mb-3">
                            <label class="form-label">优先级</label>
                            <div class="d-flex gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="priority_low" value="low" checked>
                                    <label class="form-check-label" for="priority_low">
                                        <span class="badge bg-success">低</span> 一般问题
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="priority_medium" value="medium">
                                    <label class="form-check-label" for="priority_medium">
                                        <span class="badge bg-warning">中</span> 影响使用
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="priority" id="priority_high" value="high">
                                    <label class="form-check-label" for="priority_high">
                                        <span class="badge bg-danger">高</span> 紧急问题
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-paper-plane me-2"></i>提交工单
                            </button>
                            <a href="/member" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回会员中心
                            </a>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <!-- 服务说明 -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-clock me-2"></i>响应时间
                            </h6>
                            <ul class="list-unstyled text-muted">
                                <li><span class="badge bg-danger me-2">高</span>1小时内响应</li>
                                <li><span class="badge bg-warning me-2">中</span>4小时内响应</li>
                                <li><span class="badge bg-success me-2">低</span>24小时内响应</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-warning">
                                <i class="fas fa-info-circle me-2"></i>温馨提示
                            </h6>
                            <ul class="list-unstyled text-muted small">
                                <li>• 请详细描述问题，有助于我们快速定位</li>
                                <li>• 工单提交后会发送邮件通知</li>
                                <li>• 您可以在会员中心查看工单状态</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php elseif ($action === 'view' && $ticketDetail): ?>
    <!-- 工单详情页面 -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-warning mb-0">
                    <i class="fas fa-ticket-alt me-2"></i>工单 #<?php echo $ticketDetail['id']; ?>
                </h2>
                <a href="/support" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>

            <!-- 工单信息卡片 -->
            <div class="card mb-4">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0 text-dark">
                                <?php
                                $subjectText = [
                                    'account' => '账户问题',
                                    'payment' => '支付问题',
                                    'content' => '内容问题',
                                    'technical' => '技术问题',
                                    'feature' => '功能建议',
                                    'bug' => 'Bug反馈',
                                    'other' => '其他问题'
                                ];
                                echo $subjectText[$ticketDetail['subject']] ?? $ticketDetail['subject'];
                                ?>
                            </h5>
                            <small class="text-secondary">
                                创建时间: <?php echo date('Y-m-d H:i:s', strtotime($ticketDetail['created_at'])); ?>
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php
                            $priorityClass = [
                                'low' => 'success',
                                'medium' => 'warning',
                                'high' => 'danger'
                            ];
                            $priorityText = [
                                'low' => '低优先级',
                                'medium' => '中优先级',
                                'high' => '高优先级'
                            ];
                            $statusClass = [
                                'pending' => 'warning',
                                'processing' => 'info',
                                'resolved' => 'success',
                                'closed' => 'secondary'
                            ];
                            $statusText = [
                                'pending' => '待处理',
                                'processing' => '处理中',
                                'resolved' => '已解决',
                                'closed' => '已关闭'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $priorityClass[$ticketDetail['priority']]; ?> me-2">
                                <?php echo $priorityText[$ticketDetail['priority']]; ?>
                            </span>
                            <span class="badge bg-<?php echo $statusClass[$ticketDetail['status']]; ?>">
                                <?php echo $statusText[$ticketDetail['status']]; ?>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="bg-dark text-light p-3 rounded">
                        <?php echo nl2br(htmlspecialchars($ticketDetail['message'])); ?>
                    </div>
                </div>
            </div>

            <!-- 对话记录 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-dark">
                        <i class="fas fa-comments me-2"></i>对话记录
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($ticketReplies)): ?>
                    <p class="text-muted text-center py-3">暂无回复</p>
                    <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($ticketReplies as $reply): ?>
                        <div class="timeline-item mb-4">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <?php if ($reply['admin_id']): ?>
                                    <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <?php else: ?>
                                    <div class="avatar bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <strong class="text-dark">
                                                <?php if ($reply['admin_id']): ?>
                                                    客服 (<?php echo htmlspecialchars($reply['admin_username']); ?>)
                                                <?php else: ?>
                                                    <?php echo htmlspecialchars($reply['nickname'] ?: $reply['username']); ?>
                                                <?php endif; ?>
                                            </strong>
                                            <?php if ($reply['admin_id']): ?>
                                            <span class="badge bg-primary ms-2">官方回复</span>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-secondary">
                                            <?php echo date('Y-m-d H:i:s', strtotime($reply['created_at'])); ?>
                                        </small>
                                    </div>
                                    <div class="<?php echo $reply['admin_id'] ? 'bg-primary bg-opacity-10 text-dark' : 'bg-light text-dark'; ?> p-3 rounded">
                                        <?php echo nl2br(htmlspecialchars($reply['content'])); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 回复表单 -->
            <?php if (in_array($ticketDetail['status'], ['pending', 'processing'])): ?>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0 text-dark">
                        <i class="fas fa-reply me-2"></i>添加回复
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="/api/support-reply.php">
                        <input type="hidden" name="feedback_id" value="<?php echo $ticketDetail['id']; ?>">

                        <div class="mb-3">
                            <label class="form-label">回复内容 *</label>
                            <textarea name="content" class="form-control" rows="4" required
                                      placeholder="请输入您的回复内容..."></textarea>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>发送回复
                            </button>
                            <small class="text-muted align-self-center">
                                <i class="fas fa-info-circle me-1"></i>
                                回复后客服会收到通知
                            </small>
                        </div>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="alert alert-info">
                <i class="fas fa-lock me-2"></i>
                此工单已<?php echo $statusText[$ticketDetail['status']]; ?>，无法继续回复
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php else: ?>
    <!-- 工单不存在或无权访问 -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4>工单不存在</h4>
                <p class="text-muted">您访问的工单不存在或您没有权限查看</p>
                <a href="/support" class="btn btn-warning">返回工单列表</a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
