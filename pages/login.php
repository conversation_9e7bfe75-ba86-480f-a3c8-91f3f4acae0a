<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

// 如果已经登录，跳转到首页
if (isset($_SESSION['user_id'])) {
    header('Location: /');
    exit;
}

require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../includes/functions.php';

$error = '';
$success = '';

if (isset($_POST['action']) && $_POST['action'] === 'login') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = '请输入用户名和密码';
    } else {
        $userModel = new User();
        $result = $userModel->login($username, $password);
        
        if ($result['success']) {
            $user = $result['user'];
            
            // 设置Session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['group_id'] = $user['group_id'];
            $_SESSION['group_name'] = $user['group_name'] ?? '会员';
            
            $success = '登录成功，正在跳转...';
            
            // JavaScript跳转
            echo '<script>setTimeout(function(){ window.location.href = "/"; }, 1500);</script>';
        } else {
            $error = $result['message'];
        }
    }
}

// 设置页面信息
$pageTitle = '用户登录 - ' . Config::getSiteName();
$pageDescription = '登录' . Config::getSiteName() . '，享受更多精彩内容';

// 页面内容
ob_start();
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card mt-5">
                <div class="card-header text-center">
                    <h4 class="text-warning mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>用户登录
                    </h4>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                    <?php endif; ?>
                    
                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="login">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名/邮箱</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark border-secondary text-warning">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                       placeholder="请输入用户名或邮箱" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark border-secondary text-warning">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">
                                记住我
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>立即登录
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="text-muted mb-2">还没有账号？</p>
                        <a href="/register" class="btn btn-outline-warning">
                            <i class="fas fa-user-plus me-2"></i>立即注册
                        </a>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="/forgot-password" class="text-muted text-decoration-none">
                            <i class="fas fa-question-circle me-1"></i>忘记密码？
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 会员特权说明 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="text-warning mb-3">
                        <i class="fas fa-crown me-2"></i>会员特权
                    </h6>
                    <div class="row text-center">
                        <div class="col-6 mb-2">
                            <div class="text-warning">注册会员</div>
                            <small class="text-muted">10张图片免费看</small>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="text-warning">月费会员</div>
                            <small class="text-muted">30天无限浏览</small>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="text-warning">三年会员</div>
                            <small class="text-muted">图片+视频+下载</small>
                        </div>
                        <div class="col-6 mb-2">
                            <div class="text-warning">永久会员</div>
                            <small class="text-muted">永久无限制</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// 表单验证
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
