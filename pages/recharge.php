<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = '';

// 处理充值卡使用
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'use_card') {
        $cardNumber = trim($_POST['card_number']);
        $cardPassword = trim($_POST['card_password']);
        
        if (empty($cardNumber) || empty($cardPassword)) {
            $message = '请输入卡号和密码';
            $messageType = 'danger';
        } else {
            // 查找充值卡
            $card = $db->fetch("
                SELECT * FROM {$db->getPrefix()}recharge_cards
                WHERE card_number = :card_number AND card_password = :card_password AND used_at IS NULL AND status = 1
            ", [
                'card_number' => $cardNumber,
                'card_password' => $cardPassword
            ]);
            
            if (!$card) {
                $message = '充值卡不存在或已使用';
                $messageType = 'danger';
            } else {
                // 使用充值卡
                $db->beginTransaction();
                
                try {
                    // 更新充值卡状态
                    $db->update('recharge_cards', [
                        'used_user_id' => $_SESSION['user_id'],
                        'used_at' => date('Y-m-d H:i:s')
                    ], 'id = :id', ['id' => $card['id']]);
                    
                    if ($card['type'] === 'points') {
                        // 积分卡：增加用户积分
                        $db->query("UPDATE {$db->getPrefix()}users SET points = points + :points WHERE id = :user_id", [
                            'points' => $card['points'],
                            'user_id' => $_SESSION['user_id']
                        ]);

                        // 记录积分日志（如果表存在）
                        try {
                            $db->insert('user_points_log', [
                                'user_id' => $_SESSION['user_id'],
                                'points' => $card['points'],
                                'type' => 'recharge',
                                'description' => '使用充值卡：' . $cardNumber,
                                'created_at' => date('Y-m-d H:i:s')
                            ]);
                        } catch (Exception $e) {
                            // 积分日志表可能不存在，忽略错误
                        }

                        $_SESSION['points'] = ($_SESSION['points'] ?? 0) + $card['points'];
                        $message = "充值成功！获得 {$card['points']} 积分";
                        
                    } elseif ($card['type'] === 'vip') {
                        // VIP卡：升级用户组
                        if (!$card['group_id'] || $card['group_id'] <= 0) {
                            throw new Exception('VIP卡配置错误：未指定目标用户组');
                        }

                        $targetGroup = $db->fetch("SELECT * FROM {$db->getPrefix()}user_groups WHERE id = :id", [
                            'id' => $card['group_id']
                        ]);

                        if (!$targetGroup) {
                            throw new Exception('目标用户组不存在（ID: ' . $card['group_id'] . '）');
                        }

                        if (!$card['group_days'] || $card['group_days'] <= 0) {
                            throw new Exception('VIP卡配置错误：VIP天数无效');
                        }

                        $expireDate = date('Y-m-d H:i:s', strtotime("+{$card['group_days']} days"));

                        $db->update('users', [
                            'group_id' => $card['group_id'],
                            'group_expire_time' => $expireDate
                        ], 'id = :id', ['id' => $_SESSION['user_id']]);

                        // 更新Session信息
                        $_SESSION['group_id'] = $card['group_id'];
                        $_SESSION['group_name'] = $targetGroup['group_name'];
                        $_SESSION['group_expire_time'] = $expireDate;

                        $message = "VIP升级成功！升级为：{$targetGroup['group_name']}，有效期至：" . date('Y-m-d', strtotime($expireDate));
                    }
                    
                    $db->commit();
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    $db->rollback();
                    $message = '充值失败：' . $e->getMessage();
                    $messageType = 'danger';
                }
            }
        }
    }
}

// 获取用户信息
$user = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $_SESSION['user_id']]);

// 获取用户组信息
$userGroup = null;
if (!empty($user['group_id'])) {
    $userGroup = $db->fetch("SELECT * FROM {$db->getPrefix()}user_groups WHERE id = :id", [
        'id' => $user['group_id']
    ]);
}

// 获取充值记录
$rechargeHistory = $db->fetchAll("
    SELECT rc.*, ug.group_name as target_group_name
    FROM {$db->getPrefix()}recharge_cards rc
    LEFT JOIN {$db->getPrefix()}user_groups ug ON rc.group_id = ug.id
    WHERE rc.used_user_id = :user_id
    ORDER BY rc.used_at DESC
    LIMIT 10
", ['user_id' => $_SESSION['user_id']]);

// 设置页面信息
$pageTitle = '充值中心 - ' . Config::getSiteName();
$pageDescription = '使用充值卡获得积分或升级VIP会员';

// 页面内容
ob_start();
?>

<div class="container mt-4">
    <div class="row">
        <!-- 充值表单 -->
        <div class="col-md-8">
            <div class="card bg-dark border-secondary">
                <div class="card-header">
                    <h5 class="mb-0 text-warning">
                        <i class="fas fa-credit-card me-2"></i>充值中心
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="row g-3">
                        <input type="hidden" name="action" value="use_card">
                        
                        <div class="col-md-6">
                            <label class="form-label">充值卡号</label>
                            <input type="text" class="form-control bg-dark border-secondary text-light" 
                                   name="card_number" placeholder="请输入16位卡号" maxlength="16" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label class="form-label">充值密码</label>
                            <input type="text" class="form-control bg-dark border-secondary text-light" 
                                   name="card_password" placeholder="请输入12位密码" maxlength="12" required>
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-credit-card me-2"></i>立即充值
                            </button>
                        </div>
                    </form>
                    
                    <hr class="border-secondary">
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>充值说明</h6>
                        <ul class="mb-0 small">
                            <li><strong>积分卡</strong>：充值后获得相应积分，可用于查看收费内容</li>
                            <li><strong>VIP卡</strong>：升级为VIP会员，享受更多特权</li>
                            <li><strong>有效期</strong>：请在有效期内使用充值卡</li>
                            <li><strong>客服</strong>：如有问题请联系客服</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户信息 -->
        <div class="col-md-4">
            <div class="card bg-dark border-secondary">
                <div class="card-header">
                    <h6 class="mb-0">账户信息</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-warning text-dark rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 60px; height: 60px; font-size: 1.5rem;">
                            <?php echo strtoupper(substr($user['username'], 0, 1)); ?>
                        </div>
                        <h6 class="mt-2 mb-0"><?php echo htmlspecialchars($user['username']); ?></h6>
                        <small class="text-muted"><?php echo htmlspecialchars($userGroup['group_name'] ?? '普通会员'); ?></small>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end border-secondary">
                                <h6 class="text-warning mb-0"><?php echo number_format($user['points']); ?></h6>
                                <small class="text-muted">当前积分</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-info mb-0">
                                <?php if (!empty($user['group_expire_time']) && strtotime($user['group_expire_time']) > time()): ?>
                                    <?php echo date('Y-m-d', strtotime($user['group_expire_time'])); ?>
                                <?php else: ?>
                                    未开通
                                <?php endif; ?>
                            </h6>
                            <small class="text-muted">VIP到期</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快捷操作 -->
            <div class="card bg-dark border-secondary mt-3">
                <div class="card-header">
                    <h6 class="mb-0">快捷操作</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/vip" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-crown me-2"></i>VIP升级
                        </a>
                        <a href="/profile" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-user me-2"></i>个人中心
                        </a>
                        <a href="/contact" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-headset me-2"></i>联系客服
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 充值记录 -->
    <?php if (!empty($rechargeHistory)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-dark border-secondary">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>充值记录
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-dark table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>卡号</th>
                                    <th>类型</th>
                                    <th>价值</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($rechargeHistory as $record): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d H:i', strtotime($record['used_at'])); ?></td>
                                        <td><code><?php echo htmlspecialchars($record['card_number']); ?></code></td>
                                        <td>
                                            <?php if ($record['type'] === 'points'): ?>
                                                <span class="badge bg-success">积分卡</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark">VIP卡</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($record['type'] === 'points'): ?>
                                                <?php echo number_format($record['points'] ?? 0); ?> 积分
                                            <?php else: ?>
                                                <?php echo $record['group_days'] ?? 0; ?> 天 (<?php echo htmlspecialchars($record['target_group_name'] ?? '未知'); ?>)
                                            <?php endif; ?>
                                        </td>
                                        <td><span class="badge bg-success">已使用</span></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
