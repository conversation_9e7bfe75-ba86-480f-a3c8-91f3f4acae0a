<?php
session_start();
require_once __DIR__ . "/../core/Config.php";

$siteName = Config::getSiteName();
$pageTitle = "关于我们 - {$siteName}";
$pageDescription = "了解{$siteName}的发展历程、服务理念和团队介绍";
$pageKeywords = "关于我们,{$siteName},团队介绍,发展历程";

ob_start();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header text-center">
                    <h2 class="text-warning mb-0">
                        <i class="fas fa-info-circle me-2"></i>关于我们
                    </h2>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <h3 class="text-warning"><?php echo Config::getSiteName(); ?></h3>
                        <p class="text-muted">专业的美女套图视频分享平台</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="text-warning mb-3">
                                <i class="fas fa-heart me-2"></i>我们的使命
                            </h5>
                            <p><?php echo Config::getSiteName(); ?>致力于为用户提供高质量的美女图片和视频内容，打造一个安全、专业、优质的内容分享平台。我们坚持原创优先，精品至上的理念，为广大用户带来最佳的视觉体验。</p>
                            
                            <h5 class="text-warning mb-3 mt-4">
                                <i class="fas fa-star me-2"></i>我们的特色
                            </h5>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>精选高质量内容</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>多样化会员服务</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>安全稳定的平台</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>用户友好的界面</li>
                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>持续更新的内容</li>
                            </ul>
                            
                            <h5 class="text-warning mb-3 mt-4">
                                <i class="fas fa-users me-2"></i>服务承诺
                            </h5>
                            <p>我们承诺为用户提供：</p>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-shield-alt text-primary me-2"></i>安全可靠的服务环境</li>
                                <li class="mb-2"><i class="fas fa-clock text-info me-2"></i>7×24小时技术支持</li>
                                <li class="mb-2"><i class="fas fa-lock text-warning me-2"></i>严格的隐私保护</li>
                                <li class="mb-2"><i class="fas fa-thumbs-up text-success me-2"></i>优质的用户体验</li>
                            </ul>
                            
                            <h5 class="text-warning mb-3 mt-4">
                                <i class="fas fa-envelope me-2"></i>联系我们
                            </h5>
                            <p>如果您有任何问题或建议，欢迎通过以下方式联系我们：</p>
                            <ul class="list-unstyled">
                                <li class="mb-2"><i class="fas fa-envelope text-primary me-2"></i>邮箱：<EMAIL></li>
                                <li class="mb-2"><i class="fas fa-phone text-success me-2"></i>客服QQ：在线客服</li>
                                <li class="mb-2"><i class="fas fa-clock text-info me-2"></i>服务时间：周一至周日 9:00-21:00</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="/" class="btn btn-warning">
                            <i class="fas fa-home me-2"></i>返回首页
                        </a>
                        <a href="/contact" class="btn btn-outline-warning ms-2">
                            <i class="fas fa-envelope me-2"></i>联系我们
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
