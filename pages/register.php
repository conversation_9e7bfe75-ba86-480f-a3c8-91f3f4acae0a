<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

// 如果已经登录，跳转到首页
if (isset($_SESSION['user_id'])) {
    header('Location: /');
    exit;
}

require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/InviteManager.php';

$error = '';
$success = '';

// 获取URL中的邀请码
$inviteCodeFromUrl = $_GET['invite'] ?? '';

$inviteManager = new InviteManager();

if (isset($_POST['action']) && $_POST['action'] === 'register') {
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $inviteCode = trim($_POST['invite_code'] ?? $inviteCodeFromUrl);
    $agree = $_POST['agree'] ?? '';
    
    // 基础验证
    if (empty($username) || empty($email) || empty($password)) {
        $error = '请填写完整的注册信息';
    } elseif (strlen($username) < 3 || strlen($username) > 20) {
        $error = '用户名长度应在3-20个字符之间';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = '请输入有效的邮箱地址';
    } elseif (strlen($password) < 6) {
        $error = '密码长度不能少于6位';
    } elseif ($password !== $confirmPassword) {
        $error = '两次输入的密码不一致';
    } elseif (!$agree) {
        $error = '请阅读并同意用户协议';
    } else {
        $userModel = new User();
        
        // 检查邀请码
        $inviterId = null;
        if (!empty($inviteCode)) {
            $inviter = $userModel->getUserByInviteCode($inviteCode);
            if (!$inviter) {
                $error = '邀请码无效';
            } else {
                $inviterId = $inviter['id'];
            }
        }
        
        if (!$error) {
            $result = $userModel->register($username, $email, $password, $inviterId);

            if ($result['success']) {
                $newUserId = $result['user_id'];

                // 处理邀请奖励
                if ($inviteCode) {
                    $inviteResult = $inviteManager->processRegisterReward($newUserId, $inviteCode);
                    if ($inviteResult) {
                        $success = '注册成功！邀请奖励已发放，请登录您的账号。';
                    } else {
                        $success = '注册成功！请登录您的账号。';
                    }
                } else {
                    $success = '注册成功！请登录您的账号。';
                }

                // 清空表单
                $_POST = [];

                // JavaScript跳转到登录页
                echo '<script>setTimeout(function(){ window.location.href = "/login"; }, 2000);</script>';
            } else {
                $error = $result['message'];
            }
        }
    }
}

// 设置页面信息
$siteName = Config::getSiteName();
$pageTitle = "用户注册 - {$siteName}";
$pageDescription = "注册{$siteName}账号，享受免费图片浏览";

// 页面内容
ob_start();
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card mt-4">
                <div class="card-header text-center">
                    <h4 class="text-warning mb-0">
                        <i class="fas fa-user-plus me-2"></i>用户注册
                    </h4>
                    <p class="text-muted mt-2 mb-0">注册即送100积分，可浏览10张免费图片</p>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                    <?php endif; ?>
                    
                    <form method="post" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="register">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-dark border-secondary text-warning">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                               placeholder="3-20个字符" required pattern="[a-zA-Z0-9_]{3,20}">
                                        <div class="invalid-feedback">
                                            请输入3-20位用户名（字母、数字、下划线）
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱地址 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-dark border-secondary text-warning">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                               placeholder="请输入邮箱地址" required>
                                        <div class="invalid-feedback">
                                            请输入有效的邮箱地址
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-dark border-secondary text-warning">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="至少6位密码" required minlength="6">
                                        <div class="invalid-feedback">
                                            密码长度不能少于6位
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">确认密码 *</label>
                                    <div class="input-group">
                                        <span class="input-group-text bg-dark border-secondary text-warning">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                               placeholder="再次输入密码" required>
                                        <div class="invalid-feedback">
                                            两次输入的密码不一致
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="invite_code" class="form-label">邀请码 (可选)</label>
                            <div class="input-group">
                                <span class="input-group-text bg-dark border-secondary text-warning">
                                    <i class="fas fa-gift"></i>
                                </span>
                                <input type="text" class="form-control" id="invite_code" name="invite_code"
                                       value="<?php echo htmlspecialchars($_POST['invite_code'] ?? $inviteCodeFromUrl); ?>"
                                       placeholder="填写邀请码可获得额外积分">
                            </div>
                            <small class="text-muted">有邀请码？填写后您和邀请人都将获得额外积分奖励</small>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agree" name="agree" value="1" required>
                            <label class="form-check-label" for="agree">
                                我已阅读并同意 
                                <a href="/terms" class="text-warning text-decoration-none" target="_blank">《用户协议》</a> 
                                和 
                                <a href="/privacy" class="text-warning text-decoration-none" target="_blank">《隐私政策》</a>
                            </label>
                            <div class="invalid-feedback">
                                请阅读并同意用户协议
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-user-plus me-2"></i>立即注册
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="text-muted mb-2">已有账号？</p>
                        <a href="/login" class="btn btn-outline-warning">
                            <i class="fas fa-sign-in-alt me-2"></i>立即登录
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 注册福利说明 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="text-warning mb-3">
                        <i class="fas fa-gift me-2"></i>注册福利
                    </h6>
                    <div class="row">
                        <div class="col-md-4 text-center mb-3">
                            <div class="text-warning fw-bold">100积分</div>
                            <small class="text-muted">注册即送</small>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="text-warning fw-bold">10张图片</div>
                            <small class="text-muted">免费浏览</small>
                        </div>
                        <div class="col-md-4 text-center mb-3">
                            <div class="text-warning fw-bold">收藏功能</div>
                            <small class="text-muted">个人收藏</small>
                        </div>
                    </div>
                    
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-users me-1"></i>邀请好友注册，您和好友都将获得额外积分奖励
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
// 密码确认验证
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('两次输入的密码不一致');
    } else {
        this.setCustomValidity('');
    }
});

// 用户名可用性检查
let usernameTimer;
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    if (username.length >= 3) {
        clearTimeout(usernameTimer);
        usernameTimer = setTimeout(() => {
            checkUsernameAvailability(username);
        }, 500);
    }
});

function checkUsernameAvailability(username) {
    fetch('/api/check_username', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({username: username})
    })
    .then(response => response.json())
    .then(data => {
        const input = document.getElementById('username');
        if (data.available) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            input.setCustomValidity('用户名已被使用');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// 表单验证
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
