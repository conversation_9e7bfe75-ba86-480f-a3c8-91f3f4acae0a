<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/Video.php';
require_once __DIR__ . '/../includes/functions.php';

// 获取参数
$page = max(1, intval($_GET['page'] ?? 1));
$type = $_GET['type'] ?? 'all'; // all, album, video



// 初始化模型
$albumModel = new Album();
$videoModel = new Video();

// 获取最新内容
$allItems = [];

try {
    if ($type === 'all') {
        // 获取套图
        $albums = $albumModel->getList([
            'page' => 1,
            'page_size' => 25,
            'order' => 'latest'
        ]);

        foreach ($albums['list'] as $album) {
            $album['content_type'] = 'album';
            $album['cover'] = $album['cover'] ?: '/assets/images/placeholder.svg';
            $album['slug'] = $album['slug'] ?: 'album-' . $album['id'];
            $allItems[] = $album;
        }

        // 获取视频
        $videos = $videoModel->getList([
            'page' => 1,
            'page_size' => 25,
            'order' => 'latest'
        ]);

        foreach ($videos['list'] as $video) {
            $video['content_type'] = 'video';
            $video['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
            $video['slug'] = $video['slug'] ?: 'video-' . $video['id'];
            $video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
            $allItems[] = $video;
        }
    } elseif ($type === 'album') {
        // 只获取套图
        $albums = $albumModel->getList([
            'page' => 1,
            'page_size' => 50,
            'order' => 'latest'
        ]);

        foreach ($albums['list'] as $album) {
            $album['content_type'] = 'album';
            $album['cover'] = $album['cover'] ?: '/assets/images/placeholder.svg';
            $album['slug'] = $album['slug'] ?: 'album-' . $album['id'];
            $allItems[] = $album;
        }
    } elseif ($type === 'video') {
        // 只获取视频
        $videos = $videoModel->getList([
            'page' => 1,
            'page_size' => 50,
            'order' => 'latest'
        ]);

        foreach ($videos['list'] as $video) {
            $video['content_type'] = 'video';
            $video['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
            $video['slug'] = $video['slug'] ?: 'video-' . $video['id'];
            $video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
            $allItems[] = $video;
        }
    }
} catch (Exception $e) {
    error_log("最新内容加载失败: " . $e->getMessage());
}

// 按时间排序
usort($allItems, function($a, $b) {
    return strtotime($b['created_at']) - strtotime($a['created_at']);
});

// 分页处理
$pageSize = 20;
$total = count($allItems);
$totalPages = ceil($total / $pageSize);
$offset = ($page - 1) * $pageSize;
$items = array_slice($allItems, $offset, $pageSize);

// 设置页面信息
$typeNames = [
    'all' => '全部内容',
    'album' => '套图',
    'video' => '视频'
];

$pageTitle = '最近更新 - ' . ($typeNames[$type] ?? '全部内容') . ' - ' . getConfig('site_name', Config::getSiteName());
$pageDescription = '最新发布的精彩内容，第一时间为您呈现';
$pageKeywords = '最新,更新,最近发布';

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item active">最近更新</li>
        </ol>
    </nav>

    <!-- 页面标题和筛选 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-warning mb-3">
                <i class="fas fa-clock me-2"></i>最近更新
            </h2>
            <p class="text-muted">最新发布的<?php echo $typeNames[$type]; ?>，共 <?php echo number_format($total); ?> 项内容</p>
        </div>
        <div class="col-md-4">
            <!-- 内容类型筛选 -->
            <div class="d-flex justify-content-end">
                <div class="btn-group" role="group">
                    <a href="?type=all<?php echo $page > 1 ? '&page=' . $page : ''; ?>"
                       class="btn <?php echo $type === 'all' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?> btn-sm">
                        <i class="fas fa-th me-1"></i>全部
                    </a>
                    <a href="?type=album<?php echo $page > 1 ? '&page=' . $page : ''; ?>"
                       class="btn <?php echo $type === 'album' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?> btn-sm">
                        <i class="fas fa-images me-1"></i>套图
                    </a>
                    <a href="?type=video<?php echo $page > 1 ? '&page=' . $page : ''; ?>"
                       class="btn <?php echo $type === 'video' ? 'btn-warning text-dark' : 'btn-outline-warning'; ?> btn-sm">
                        <i class="fas fa-video me-1"></i>视频
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容列表 -->
    <?php if (!empty($items)): ?>
    <div class="row">
        <?php foreach ($items as $item): ?>
        <div class="col-12 mb-3">
            <div class="card latest-item">
                <div class="card-body">
                    <div class="row align-items-center">
                        <!-- 封面 -->
                        <div class="col-auto">
                            <div class="position-relative">
                                <img src="<?php echo htmlspecialchars($item['cover'] ?? ''); ?>"
                                     alt="<?php echo htmlspecialchars($item['title'] ?? ''); ?>"
                                     class="latest-thumb"
                                     style="width: 120px; height: 80px; object-fit: cover;"
                                     onerror="this.src='/assets/images/placeholder.svg'">
                                
                                <!-- 内容类型标识 -->
                                <div class="position-absolute top-0 start-0 m-1">
                                    <?php if ($item['content_type'] === 'video'): ?>
                                    <span class="badge bg-danger">
                                        <i class="fas fa-video me-1"></i>视频
                                    </span>
                                    <?php else: ?>
                                    <span class="badge bg-primary">
                                        <i class="fas fa-images me-1"></i>套图
                                    </span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- VIP标识 -->
                                <?php if (!$item['is_free']): ?>
                                <div class="position-absolute top-0 end-0 m-1">
                                    <span class="badge bg-warning text-dark">VIP</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- 内容信息 -->
                        <div class="col">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h5 class="mb-1">
                                        <a href="/<?php echo $item['content_type']; ?>/<?php echo $item['id']; ?>"
                                           class="text-warning text-decoration-none">
                                            <?php echo htmlspecialchars($item['title'] ?? ''); ?>
                                        </a>
                                    </h5>
                                    <p class="text-muted mb-2">
                                        <?php echo htmlspecialchars(mb_substr($item['description'] ?? '', 0, 120) . '...'); ?>
                                    </p>
                                    <div class="latest-meta">
                                        <span class="me-3">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($item['view_count']); ?>
                                        </span>
                                        <span class="me-3">
                                            <i class="fas fa-heart me-1"></i><?php echo number_format($item['favorite_count']); ?>
                                        </span>
                                        <?php if ($item['content_type'] === 'video'): ?>
                                        <span class="me-3">
                                            <i class="fas fa-clock me-1"></i><?php echo $item['duration_formatted']; ?>
                                        </span>
                                        <?php else: ?>
                                        <span class="me-3">
                                            <i class="fas fa-images me-1"></i><?php echo $item['image_count']; ?>张
                                        </span>
                                        <?php endif; ?>
                                        <span class="text-muted">
                                            <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($item['category_name'] ?? '未分类'); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- 发布时间 -->
                                <div class="text-end">
                                    <div class="latest-time">
                                        <small class="text-muted">
                                            <?php
                                            $time = strtotime($item['created_at']);
                                            $now = time();
                                            $diff = $now - $time;
                                            
                                            if ($diff < 3600) {
                                                echo ceil($diff / 60) . '分钟前';
                                            } elseif ($diff < 86400) {
                                                echo ceil($diff / 3600) . '小时前';
                                            } elseif ($diff < 2592000) {
                                                echo ceil($diff / 86400) . '天前';
                                            } else {
                                                echo date('Y-m-d', $time);
                                            }
                                            ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('H:i', $time); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <nav class="mt-5">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&type=<?php echo $type; ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&type=<?php echo $type; ?>">
                    <?php echo $i; ?>
                </a>
            </li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&type=<?php echo $type; ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-clock fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">暂无最新内容</h4>
        <p class="text-muted">请稍后再来查看最新更新</p>
        <a href="/" class="btn btn-warning">
            <i class="fas fa-home me-1"></i>返回首页
        </a>
    </div>
    <?php endif; ?>
</div>

<style>
.latest-item {
    transition: transform 0.2s ease;
}

.latest-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.latest-thumb {
    border-radius: 8px;
    border: 2px solid #495057;
}

.latest-time {
    text-align: center;
    min-width: 80px;
}

.latest-meta {
    font-size: 0.9em;
    color: #adb5bd;
}
</style>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 最新内容项目点击事件
    document.querySelectorAll('.latest-item').forEach(item => {
        item.addEventListener('click', function(e) {
            if (!e.target.closest('a')) {
                const link = this.querySelector('a');
                if (link) {
                    window.location.href = link.href;
                }
            }
        });
    });
});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
