<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../includes/functions.php';

$albumModel = new Album();
$userModel = new User();
$currentUser = getCurrentUser();

$albumId = intval($_GET['id'] ?? 0);
if (!$albumId) {
    header('Location: /albums');
    exit;
}

// 获取套图详情
$album = $albumModel->getById($albumId);
if (!$album) {
    header('HTTP/1.0 404 Not Found');
    include __DIR__ . '/404.php';
    exit;
}

// 检查查看权限
$canView = $albumModel->canView($album['id'], $currentUser['id'] ?? 0);
if (!$canView) {
    // 如果未登录，跳转到登录页
    if (!$currentUser) {
        $_SESSION['redirect_after_login'] = "/album/{$albumId}";
        header('Location: /login');
        exit;
    } else {
        // 已登录但权限不足，显示升级提示
        $pageTitle = '权限不足 - ' . Config::getSiteName();
        $errorMessage = '此套图需要VIP权限才能查看';
        include __DIR__ . '/permission_denied.php';
        exit;
    }
}

// 增加浏览量
$albumModel->increaseViewCount($album['id']);

// 获取套图图片（获取所有图片，不分页）
$images = $albumModel->getImages($album['id'], 1, 1000);

// 处理评论操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_comment') {
    if (!$currentUser) {
        header('Location: /login');
        exit;
    }

    $comment = trim($_POST['comment'] ?? '');
    if (!empty($comment)) {
        $db = Database::getInstance();
        $result = $db->insert('comments', [
            'user_id' => $currentUser['id'],
            'content_type' => 'album',
            'content_id' => $album['id'],
            'content' => $comment,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            $commentMessage = '评论发表成功';
        } else {
            $commentMessage = '评论发表失败';
        }
    }

    // 重定向回当前页面，避免重复提交
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// 获取评论列表
$db = Database::getInstance();
$comments = $db->fetchAll(
    "SELECT c.*, u.username, u.nickname, u.avatar
     FROM {$db->getPrefix()}comments c
     LEFT JOIN {$db->getPrefix()}users u ON c.user_id = u.id
     WHERE c.content_type = 'album' AND c.content_id = :content_id AND c.status = 1
     ORDER BY c.created_at DESC",
    ['content_id' => $album['id']]
);

// 获取用户互动状态
$userInteraction = [
    'is_liked' => false,
    'is_favorited' => false,
    'is_vip' => false,
];

if ($currentUser) {
    $userInteraction = $albumModel->getUserInteraction($album['id'], $currentUser['id']);
    // 检查用户VIP状态
    $userInteraction['is_vip'] = !empty($currentUser['vip_expires']) && strtotime($currentUser['vip_expires']) > time();
}

// 获取相关套图推荐
$relatedAlbums = $albumModel->getRelated($album['id'], 8);

// 设置页面信息
$pageTitle = $album['title'] . ' - ' . Config::getSiteName();
$pageDescription = mb_substr($album['description'], 0, 150);
$pageKeywords = $album['tags'] ?: '';
$pageImage = $album['cover'];
$pageCSS = ['/assets/css/album-detail.css?v=' . time()];

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item"><a href="/albums" class="text-warning">套图</a></li>
            <?php if ($album['category_name']): ?>
            <li class="breadcrumb-item"><a href="/albums/cat/<?php echo $album['category_slug'] ?? ''; ?>" class="text-warning"><?php echo htmlspecialchars($album['category_name']); ?></a></li>
            <?php endif; ?>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($album['title']); ?></li>
        </ol>
    </nav>

    <div class="row">
        <!-- 主要内容 -->
        <div class="col-lg-8">
            <!-- 套图信息 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h1 class="h4 mb-2"><?php echo htmlspecialchars($album['title'] ?? ''); ?></h1>
                            <div class="text-muted">
                                <span class="me-3">
                                    <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($album['category_name'] ?? '未分类'); ?>
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-calendar me-1"></i><?php
                                    $dateTime = $album['published_at'] ?? $album['created_at'] ?? date('Y-m-d H:i:s');
                                    echo date('Y-m-d', strtotime($dateTime));
                                    ?>
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-images me-1"></i><?php echo count($images); ?>张
                                </span>
                                <?php if (!$album['is_free']): ?>
                                <span class="badge bg-warning text-dark">VIP</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="btn-group">
                            <button class="btn btn-outline-warning btn-sm btn-like <?php echo $userInteraction['is_liked'] ? 'active' : ''; ?>" 
                                    data-type="album" data-id="<?php echo $album['id']; ?>">
                                <i class="fas fa-thumbs-up me-1"></i>
                                <span class="count"><?php echo number_format($album['like_count']); ?></span>
                            </button>
                            <button class="btn btn-outline-warning btn-sm btn-favorite <?php echo $userInteraction['is_favorited'] ? 'active' : ''; ?>" 
                                    data-type="album" data-id="<?php echo $album['id']; ?>">
                                <i class="fas fa-heart me-1"></i>
                                <span class="count"><?php echo number_format($album['favorite_count']); ?></span>
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="shareAlbum()">
                                <i class="fas fa-share-alt me-1"></i>分享
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="downloadAlbum()">
                                <i class="fas fa-download me-1"></i>下载
                            </button>
                        </div>
                    </div>
                    
                    <div class="stats d-flex">
                        <span class="me-4 text-muted">
                            <i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?> 浏览
                        </span>
                        <span class="me-4 text-muted">
                            <i class="fas fa-download me-1"></i><?php echo number_format($album['download_count']); ?> 下载
                        </span>
                    </div>
                </div>
            </div>

            <!-- 图片展示 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">图片浏览</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" onclick="changeViewMode(1)">
                                <i class="fas fa-image"></i> 单张
                            </button>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode(6)">
                                <i class="fas fa-th"></i> 6张/页
                            </button>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode(12)">
                                <i class="fas fa-th-large"></i> 12张/页
                            </button>
                            <?php if ($userInteraction['is_vip']): ?>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode('custom')">
                                <i class="fas fa-cog"></i> 自定义
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- 图片显示区域 -->
                    <div id="imageGallery" class="image-gallery">
                        <!-- 图片将通过JavaScript动态加载 -->
                    </div>

                    <!-- 分页控制 -->
                    <div id="paginationControls" class="d-flex justify-content-between align-items-center mt-4">
                        <div class="pagination-info">
                            <span class="text-muted">
                                第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                                (共 <?php echo count($images); ?> 张图片)
                            </span>
                        </div>
                        <div class="pagination-buttons">
                            <button id="prevBtn" class="btn btn-outline-secondary btn-sm me-2" onclick="previousPage()" disabled>
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <button id="nextBtn" class="btn btn-outline-secondary btn-sm" onclick="nextPage()">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- VIP自定义设置 -->
                    <?php if ($userInteraction['is_vip']): ?>
                    <div id="customSettings" class="mt-3" style="display: none;">
                        <div class="card bg-dark border-secondary">
                            <div class="card-body">
                                <h6 class="text-warning">VIP自定义设置</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">每页显示图片数：</label>
                                        <input type="number" id="customPerPage" class="form-control" min="1" max="50" value="6">
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button class="btn btn-warning btn-sm" onclick="applyCustomSettings()">
                                            <i class="fas fa-check"></i> 应用设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 套图描述 -->
            <?php if ($album['description']): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="text-light">套图简介</h5>
                    <div class="album-description">
                        <?php echo nl2br(htmlspecialchars($album['description'] ?? '')); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 标签 -->
            <?php if ($album['tags']): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <h5>相关标签</h5>
                    <div class="tags">
                        <?php 
                        $tags = json_decode($album['tags'], true) ?: [];
                        foreach ($tags as $tag): ?>
                        <a href="/albums?keyword=<?php echo urlencode($tag); ?>" 
                           class="badge bg-secondary text-decoration-none me-2 mb-2">
                            #<?php echo htmlspecialchars($tag ?? ''); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 评论区 -->
            <div class="card">
                <div class="card-body">
                    <h5 class="text-light">评论 <span class="text-muted">(<?php echo count($comments); ?>)</span></h5>

                    <?php if ($currentUser): ?>
                    <form class="comment-form mb-4" method="POST">
                        <input type="hidden" name="action" value="add_comment">
                        <div class="mb-3">
                            <textarea class="form-control" name="comment" rows="3" placeholder="写下你的评论..." required></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">支持 Markdown 语法</small>
                            <button type="submit" class="btn btn-warning" style="color: #333;">发表评论</button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">登录后可以发表评论</p>
                        <a href="/login" class="btn btn-warning" style="color: #333;">立即登录</a>
                    </div>
                    <?php endif; ?>

                    <div class="comments-list">
                        <?php if (!empty($comments)): ?>
                            <?php foreach ($comments as $comment): ?>
                            <div class="comment-item border-bottom border-secondary pb-3 mb-3">
                                <div class="d-flex">
                                    <img src="<?php echo htmlspecialchars($comment['avatar'] ?: '/assets/images/default-avatar.png'); ?>"
                                         alt="<?php echo htmlspecialchars($comment['nickname'] ?: $comment['username']); ?>"
                                         class="rounded-circle me-3" style="width: 40px; height: 40px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="text-warning mb-0"><?php echo htmlspecialchars($comment['nickname'] ?: $comment['username']); ?></h6>
                                            <small class="text-muted"><?php echo date('Y-m-d H:i', strtotime($comment['created_at'])); ?></small>
                                        </div>
                                        <p class="text-light mb-0"><?php echo nl2br(htmlspecialchars($comment['content'])); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>暂无评论，来抢沙发吧~</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 相关推荐 -->
            <?php if (!empty($relatedAlbums)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-warning">
                        <i class="fas fa-thumbs-up me-2"></i>推荐套图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php foreach ($relatedAlbums as $related): ?>
                        <div class="col-6">
                            <div class="related-album">
                                <a href="/album/<?php echo $related['id']; ?>">
                                    <img src="<?php echo htmlspecialchars($related['cover'] ?? ''); ?>"
                                         alt="<?php echo htmlspecialchars($related['title'] ?? ''); ?>"
                                         class="img-fluid rounded">
                                    <div class="related-overlay">
                                        <div class="related-title">
                                            <?php echo htmlspecialchars(mb_substr($related['title'] ?? '', 0, 20)); ?>
                                        </div>
                                        <div class="related-stats">
                                            <small>
                                                <i class="fas fa-eye me-1"></i><?php echo number_format($related['view_count']); ?>
                                                <i class="fas fa-images ms-2 me-1"></i><?php echo $related['image_count']; ?>
                                            </small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 下载提示 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0 text-warning">
                        <i class="fas fa-info-circle me-2"></i>下载说明
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>支持单张下载和批量下载
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>原图高清无水印
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>WebP格式节省流量
                        </li>
                        <?php if (!$album['is_free']): ?>
                        <li class="mb-0">
                            <i class="fas fa-crown text-warning me-2"></i>VIP专享高速下载
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- 分享套图 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0 text-warning">
                        <i class="fas fa-share-alt me-2"></i>分享套图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="shareToWeibo()">
                            <i class="fab fa-weibo me-2"></i>新浪微博
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="shareToWechat()">
                            <i class="fab fa-weixin me-2"></i>微信朋友圈
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="shareToQQ()">
                            <i class="fab fa-qq me-2"></i>QQ空间
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyLink()">
                            <i class="fas fa-link me-2"></i>复制链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图片灯箱 -->
<div id="lightbox" class="lightbox" onclick="closeLightbox()">
    <div class="lightbox-content">
        <div class="lightbox-header">
            <div class="lightbox-title"></div>
            <div class="lightbox-actions">
                <button class="btn btn-dark btn-sm" onclick="downloadCurrentImage()">
                    <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-dark btn-sm" onclick="closeLightbox()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="lightbox-body">
            <img id="lightboxImage" src="" alt="" onclick="event.stopPropagation()">
            <div class="lightbox-nav">
                <button class="lightbox-prev" onclick="prevImage(); event.stopPropagation()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="lightbox-next" onclick="nextImage(); event.stopPropagation()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="lightbox-hint" id="lightboxHint" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; opacity: 0.8; pointer-events: none; z-index: 1000; transition: opacity 0.5s ease-in-out;">
                拖拽图片查看完整内容 | 点击切换显示模式 | 左右箭头切换图片 | 按住Ctrl+鼠标悬停启用放大镜
            </div>
            <!-- 放大镜容器 -->
            <div id="magnifier" style="position: absolute; width: 200px; height: 200px; border: 3px solid #fff; border-radius: 50%; background: #000; box-shadow: 0 0 20px rgba(0,0,0,0.8); display: none; pointer-events: none; z-index: 2000; overflow: hidden;">
                <img id="magnifierImage" src="" alt="" style="position: absolute; pointer-events: none;">
            </div>
        </div>
        <div class="lightbox-footer">
            <div class="lightbox-counter"></div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$albumTitle = json_encode($album['title']);
$albumDescription = json_encode(mb_substr($album['description'], 0, 100));

$pageScript = "
// 拖拽功能完全重写版本 v4.2 - 彻底修复模式切换闪烁 - " . date('Y-m-d H:i:s') . "
console.log('Loading STABLE CLICK-SWITCH version 4.2 at " . date('H:i:s') . "');

// 使用var声明避免重复声明错误
var images = " . json_encode($images) . ";
var albumTitle = " . json_encode($album['title']) . ";
var albumDescription = " . json_encode($album['description']) . ";
var currentImageIndex = 0;

// 图片灯箱
var isFullscreen = false;

// 确保DOM加载完成
console.log('Page script loaded');
console.log('Images:', images);
console.log('Album title:', albumTitle);

// 全局拖拽状态 - 完全重新设计
window.dragState = {
    isFullscreenMode: true,
    isDragging: false,
    dragOffsetY: 0,
    initialOffsetY: 0,  // 新增：记录拖拽开始时的初始偏移量
    startY: 0,
    startTime: 0,
    currentImage: null,
    
    // 放大镜相关状态
    magnifierEnabled: false,
    magnifierVisible: false,
    magnifierZoom: 3, // 放大倍数
    
    // 新增：鼠标移动跟踪
    hasMovedMouse: false,
    clickHandled: false, // 新增：防止重复处理点击
    switchingMode: false, // 新增：防止频繁切换模式
    
    // 重置状态（仅在切换图片或关闭灯箱时使用）
    reset: function() {
        this.isDragging = false;
        this.dragOffsetY = 0;
        this.initialOffsetY = 0;
        this.startY = 0;
        this.startTime = 0;
        this.magnifierEnabled = false;
        this.magnifierVisible = false;
        document.body.classList.remove('dragging');
        hideMagnifier();
        console.log('Drag state reset');
    },
    
    // 软重置：只重置拖拽状态，保持位置
    softReset: function() {
        this.isDragging = false;
        this.startY = 0;
        this.startTime = 0;
        document.body.classList.remove('dragging');
        console.log('Drag state soft reset');
    },
    
    // 应用拖拽位置
    applyOffset: function() {
        if (this.currentImage) {
            this.currentImage.style.transform = 'translateY(' + this.dragOffsetY + 'px)';
            console.log('Applied drag offset:', this.dragOffsetY);
        }
    }
};

// 简化的灯箱显示函数
function showLightbox(index) {
    console.log('=== showLightbox v4.0 ===', index);

    if (!images || !images[index]) {
        console.error('Invalid image index:', index);
        return;
    }

    currentImageIndex = index;
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    
    if (!lightbox || !lightboxImage) {
        console.error('Lightbox elements not found');
        return;
    }

    // 设置图片信息
    lightboxImage.src = images[index].url;
    
    // 更新计数器
    const lightboxTitle = document.querySelector('.lightbox-title');
    const lightboxCounter = document.querySelector('.lightbox-counter');
    if (lightboxTitle) lightboxTitle.textContent = albumTitle + ' - ' + (index + 1);
    if (lightboxCounter) lightboxCounter.textContent = (index + 1) + ' / ' + images.length;

    // 重置拖拽状态
    window.dragState.reset();
    window.dragState.currentImage = lightboxImage;
    window.dragState.isFullscreenMode = true;

    // 应用全屏样式
    applyFullscreenStyle();
    
    // 绑定新的事件处理器
    bindImageEvents();

    // 显示灯箱
    lightbox.style.display = 'flex';
    lightbox.style.visibility = 'visible';
    document.body.style.overflow = 'hidden';

    // 显示提示语并设置自动隐藏
    showHintWithAutoHide();

    console.log('Lightbox displayed with new v4.0 logic');
}

// 应用全屏样式
function applyFullscreenStyle() {
    const img = window.dragState.currentImage;
    if (!img) return;
    
    console.log('Applying fullscreen style, mode:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
    
    if (window.dragState.isFullscreenMode) {
        // 全屏模式
        img.style.cssText = `
            width: 100vw !important;
            height: auto !important;
            min-height: 100vh !important;
            object-fit: cover !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            cursor: grab !important;
            transform: translateY(` + window.dragState.dragOffsetY + `px) !important;
        `;
    } else {
        // 适应模式
        img.style.cssText = `
            width: auto !important;
            height: auto !important;
            max-width: 100% !important;
            max-height: 100% !important;
            object-fit: contain !important;
            position: relative !important;
            top: auto !important;
            left: auto !important;
            cursor: pointer !important;
            transform: none !important;
        `;
    }
}

// 切换显示模式
function toggleImageMode() {
    console.log('=== toggleImageMode v4.1 ===');
    console.log('Before toggle:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
    
    // 防止频繁切换
    if (window.dragState.switchingMode) {
        console.log('Mode switch already in progress, ignoring');
        return;
    }
    
    window.dragState.switchingMode = true;
    window.dragState.isFullscreenMode = !window.dragState.isFullscreenMode;
    
    console.log('After toggle:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
    
    // 清理所有事件
    cleanupAllEvents();
    
    // 应用新样式
    applyFullscreenStyle();
    
    // 重新绑定事件
    bindImageEvents();
    
    // 释放切换锁
    setTimeout(() => {
        window.dragState.switchingMode = false;
    }, 200);
}

// 清理所有事件监听器
function cleanupAllEvents() {
    const img = window.dragState.currentImage;
    if (!img) return;
    
    console.log('Cleaning up all events v3.0');
    
    // 移除所有可能的事件监听器
    const events = ['mousedown', 'mousemove', 'mouseup', 'touchstart', 'touchmove', 'touchend', 'click', 'mouseenter', 'mouseleave'];
    
    events.forEach(eventType => {
        // 尝试移除，即使没有绑定也不会出错
        img.removeEventListener(eventType, handleMouseDown);
        img.removeEventListener(eventType, handleTouchStart);
        img.removeEventListener(eventType, handleTouchMove);
        img.removeEventListener(eventType, handleTouchEnd);
        img.removeEventListener(eventType, handleClick);
        img.removeEventListener(eventType, handleMouseMove);
        img.removeEventListener(eventType, handleMouseUp);
        img.removeEventListener(eventType, handleMagnifierMove);
        img.removeEventListener(eventType, handleMagnifierEnter);
        img.removeEventListener(eventType, handleMagnifierLeave);
    });
    
    // 清理document级别的事件
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    
    // 隐藏放大镜
    hideMagnifier();
    
    // 使用软重置，只重置拖拽状态，保持位置
    window.dragState.softReset();
    
    console.log('All events cleaned up');
}

// 绑定图片事件
function bindImageEvents() {
    const img = window.dragState.currentImage;
    if (!img) return;
    
    console.log('Binding events v3.0, mode:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
    
    if (window.dragState.isFullscreenMode) {
        // 全屏模式：绑定拖拽事件和放大镜事件
        img.addEventListener('mousedown', handleMouseDown, { passive: false });
        img.addEventListener('touchstart', handleTouchStart, { passive: false });
        
        // 放大镜事件
        img.addEventListener('mousemove', handleMagnifierMove, { passive: false });
        img.addEventListener('mouseenter', handleMagnifierEnter, { passive: false });
        img.addEventListener('mouseleave', handleMagnifierLeave, { passive: false });
        
        console.log('Drag and magnifier events bound');
    } else {
        // 适应模式：绑定点击事件
        img.addEventListener('click', handleClick, { passive: false });
        img.addEventListener('touchend', handleTouchEndForClick, { passive: false });
        console.log('Click events bound');
    }
}

// 鼠标按下处理（拖拽开始）
function handleMouseDown(e) {
    console.log('Mouse down v4.1');
    
    if (!window.dragState.isFullscreenMode) {
        e.preventDefault();
        e.stopPropagation();
        toggleImageMode();
        return;
    }
    
    e.preventDefault();
    e.stopPropagation();
    
    // 重置所有状态
    window.dragState.isDragging = false;
    window.dragState.startY = e.clientY;
    window.dragState.startTime = Date.now();
    window.dragState.hasMovedMouse = false;
    window.dragState.clickHandled = false; // 新增：防止重复处理
    
    // 关键修复：记录当前的偏移量作为初始偏移量
    window.dragState.initialOffsetY = window.dragState.dragOffsetY;
    
    document.body.classList.add('dragging');
    window.dragState.currentImage.style.cursor = 'grabbing';
    
    // 绑定移动和释放事件到document
    document.addEventListener('mousemove', handleMouseMove, { passive: false });
    document.addEventListener('mouseup', handleMouseUp, { passive: false });
}

// 鼠标移动处理
function handleMouseMove(e) {
    if (!window.dragState.isFullscreenMode) return;
    
    const deltaY = e.clientY - window.dragState.startY;
    
    // 标记鼠标已移动
    if (Math.abs(deltaY) > 3) {
        window.dragState.hasMovedMouse = true;
    }
    
    // 开始拖拽检测
    if (!window.dragState.isDragging && Math.abs(deltaY) > 8) {
        window.dragState.isDragging = true;
        console.log('Drag started v4.0');
    }
    
    if (window.dragState.isDragging) {
        const img = window.dragState.currentImage;
        const imgHeight = img.offsetHeight;
        const containerHeight = window.innerHeight;
        
        // 关键修复：基于当前位置计算新的偏移量，而不是从0开始
        let newOffset = window.dragState.initialOffsetY + deltaY;
        
        // 限制拖拽范围
        if (imgHeight > containerHeight) {
            const minOffset = containerHeight - imgHeight;
            const maxOffset = 0;
            newOffset = Math.max(minOffset, Math.min(maxOffset, newOffset));
        } else {
            newOffset = 0;
        }
        
        window.dragState.dragOffsetY = newOffset;
        window.dragState.applyOffset();
    }
}

// 鼠标释放处理
function handleMouseUp(e) {
    console.log('Mouse up v4.1');
    
    // 清理document级别的事件
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    
    document.body.classList.remove('dragging');
    if (window.dragState.currentImage) {
        window.dragState.currentImage.style.cursor = 'grab';
    }
    
    // 防止重复处理点击事件
    if (window.dragState.clickHandled) {
        window.dragState.softReset();
        window.dragState.hasMovedMouse = false;
        window.dragState.clickHandled = false;
        return;
    }
    
    // 检查是否是点击（而不是拖拽）
    const duration = Date.now() - window.dragState.startTime;
    const distance = Math.abs(e.clientY - window.dragState.startY);
    
    // 更严格的点击检测
    const isValidClick = !window.dragState.isDragging && 
                        !window.dragState.hasMovedMouse && 
                        duration < 200 && 
                        distance < 3;
    
    if (isValidClick) {
        console.log('Valid click detected, switching mode');
        window.dragState.clickHandled = true;
        
        // 立即切换模式，不延迟
        e.preventDefault();
        e.stopPropagation();
        toggleImageMode();
    }
    
    // 使用软重置，保持当前拖拽位置
    window.dragState.softReset();
    window.dragState.hasMovedMouse = false;
    window.dragState.clickHandled = false;
}

// 触摸开始处理
function handleTouchStart(e) {
    console.log('Touch start v3.0');
    
    if (!window.dragState.isFullscreenMode) {
        setTimeout(() => toggleImageMode(), 50);
        return;
    }
    
    e.preventDefault();
    e.stopPropagation();
    
    const touch = e.touches[0];
    window.dragState.isDragging = false;
    window.dragState.startY = touch.clientY;
    window.dragState.startTime = Date.now();
    
    // 关键修复：记录当前的偏移量作为初始偏移量
    window.dragState.initialOffsetY = window.dragState.dragOffsetY;
    
    // 绑定触摸移动和结束事件
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: false });
}

// 触摸移动处理
function handleTouchMove(e) {
    if (!window.dragState.isFullscreenMode) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const touch = e.touches[0];
    const deltaY = touch.clientY - window.dragState.startY;
    
    if (!window.dragState.isDragging && Math.abs(deltaY) > 10) {
        window.dragState.isDragging = true;
        console.log('Touch drag started v3.0');
    }
    
    if (window.dragState.isDragging) {
        const img = window.dragState.currentImage;
        const imgHeight = img.offsetHeight;
        const containerHeight = window.innerHeight;
        
        // 关键修复：基于当前位置计算新的偏移量
        let newOffset = window.dragState.initialOffsetY + deltaY;
        
        if (imgHeight > containerHeight) {
            const minOffset = containerHeight - imgHeight;
            const maxOffset = 0;
            newOffset = Math.max(minOffset, Math.min(maxOffset, newOffset));
        } else {
            newOffset = 0;
        }
        
        window.dragState.dragOffsetY = newOffset;
        window.dragState.applyOffset();
    }
}

// 触摸结束处理
function handleTouchEnd(e) {
    console.log('Touch end v4.1');
    
    // 清理事件
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    
    const duration = Date.now() - window.dragState.startTime;
    const touch = e.changedTouches[0];
    const distance = Math.abs(touch.clientY - window.dragState.startY);
    
    // 更严格的触摸点击检测
    const isTouchClick = !window.dragState.isDragging && 
                        duration < 300 && 
                        distance < 20;
    
    if (isTouchClick && !window.dragState.switchingMode) {
        console.log('Touch click detected, switching mode');
        e.preventDefault();
        e.stopPropagation();
        setTimeout(() => toggleImageMode(), 50);
    }
    
    // 使用软重置，保持当前拖拽位置
    window.dragState.softReset();
}

// 点击处理（适应模式）
function handleClick(e) {
    console.log('Click in fit mode v4.1');
    
    // 防止重复切换
    if (window.dragState.switchingMode) {
        console.log('Mode switch in progress, ignoring click');
        return;
    }
    
    e.preventDefault();
    e.stopPropagation();
    toggleImageMode();
}

// 触摸结束处理（适应模式）
function handleTouchEndForClick(e) {
    console.log('Touch end for click v4.1');
    
    // 防止重复切换
    if (window.dragState.switchingMode) {
        console.log('Mode switch in progress, ignoring touch');
        return;
    }
    
    e.preventDefault();
    e.stopPropagation();
    setTimeout(() => toggleImageMode(), 50);
}

// ============ 放大镜功能 ============

// 显示放大镜
function showMagnifier(x, y) {
    const magnifier = document.getElementById('magnifier');
    const magnifierImage = document.getElementById('magnifierImage');
    const img = window.dragState.currentImage;
    
    if (!magnifier || !magnifierImage || !img) return;
    
    // 设置放大镜图片源
    magnifierImage.src = img.src;
    
    // 计算放大镜位置（避免超出屏幕边界）
    const magnifierSize = 200;
    const offsetX = 20; // 鼠标偏移量
    const offsetY = 20;
    
    let magnifierX = x + offsetX;
    let magnifierY = y + offsetY;
    
    // 边界检测
    if (magnifierX + magnifierSize > window.innerWidth) {
        magnifierX = x - magnifierSize - offsetX;
    }
    if (magnifierY + magnifierSize > window.innerHeight) {
        magnifierY = y - magnifierSize - offsetY;
    }
    
    magnifier.style.left = magnifierX + 'px';
    magnifier.style.top = magnifierY + 'px';
    magnifier.style.display = 'block';
    
    // 计算图片在放大镜中的位置
    updateMagnifierContent(x, y);
    
    window.dragState.magnifierVisible = true;
}

// 隐藏放大镜
function hideMagnifier() {
    const magnifier = document.getElementById('magnifier');
    if (magnifier) {
        magnifier.style.display = 'none';
    }
    window.dragState.magnifierVisible = false;
}

// 更新放大镜内容
function updateMagnifierContent(mouseX, mouseY) {
    const magnifier = document.getElementById('magnifier');
    const magnifierImage = document.getElementById('magnifierImage');
    const img = window.dragState.currentImage;
    
    if (!magnifier || !magnifierImage || !img) return;
    
    // 获取图片的边界框
    const imgRect = img.getBoundingClientRect();
    
    // 计算鼠标在图片上的相对位置
    const relativeX = mouseX - imgRect.left;
    const relativeY = mouseY - imgRect.top;
    
    // 计算放大镜应该显示的图片区域
    const zoom = window.dragState.magnifierZoom;
    const magnifierSize = 200;
    
    // 放大镜中图片的尺寸
    const magnifiedWidth = img.offsetWidth * zoom;
    const magnifiedHeight = img.offsetHeight * zoom;
    
    // 计算放大镜中图片的位置，使鼠标位置居中
    const magnifierCenterX = magnifierSize / 2;
    const magnifierCenterY = magnifierSize / 2;
    
    const imgX = magnifierCenterX - (relativeX * zoom);
    const imgY = magnifierCenterY - (relativeY * zoom);
    
    // 应用样式
    magnifierImage.style.width = magnifiedWidth + 'px';
    magnifierImage.style.height = magnifiedHeight + 'px';
    magnifierImage.style.left = imgX + 'px';
    magnifierImage.style.top = imgY + 'px';
}

// 放大镜鼠标移动处理
function handleMagnifierMove(e) {
    // 只有在按住 Ctrl 键且不在拖拽状态时才显示放大镜
    if (!e.ctrlKey || window.dragState.isDragging) {
        if (window.dragState.magnifierVisible) {
            hideMagnifier();
        }
        return;
    }
    
    e.preventDefault();
    
    // 显示并更新放大镜
    showMagnifier(e.clientX, e.clientY);
}

// 放大镜鼠标进入处理
function handleMagnifierEnter(e) {
    window.dragState.magnifierEnabled = true;
}

// 放大镜鼠标离开处理
function handleMagnifierLeave(e) {
    window.dragState.magnifierEnabled = false;
    hideMagnifier();
}

// 全局键盘事件处理（用于检测 Ctrl 键释放）
document.addEventListener('keyup', function(e) {
    if (e.key === 'Control' && window.dragState.magnifierVisible) {
        hideMagnifier();
    }
});

// ============ 提示语控制功能 ============

// 全局提示语隐藏定时器
let hintHideTimer = null;

// 显示提示语并设置自动隐藏
function showHintWithAutoHide() {
    const hint = document.getElementById('lightboxHint');
    if (!hint) return;
    
    // 清除之前的定时器
    if (hintHideTimer) {
        clearTimeout(hintHideTimer);
    }
    
    // 显示提示语
    hint.style.opacity = '0.8';
    hint.style.visibility = 'visible';
    
    // 设置3秒后自动隐藏
    hintHideTimer = setTimeout(() => {
        hint.style.opacity = '0';
        setTimeout(() => {
            hint.style.visibility = 'hidden';
        }, 500); // 等待渐隐动画完成
    }, 3000);
    
    console.log('Hint displayed, will auto-hide in 3 seconds');
}

// 手动显示提示语（用于用户主动查看）
function showHint() {
    const hint = document.getElementById('lightboxHint');
    if (!hint) return;
    
    // 清除自动隐藏定时器
    if (hintHideTimer) {
        clearTimeout(hintHideTimer);
        hintHideTimer = null;
    }
    
    hint.style.opacity = '0.8';
    hint.style.visibility = 'visible';
    
    console.log('Hint displayed manually');
}

// 隐藏提示语
function hideHint() {
    const hint = document.getElementById('lightboxHint');
    if (!hint) return;
    
    // 清除定时器
    if (hintHideTimer) {
        clearTimeout(hintHideTimer);
        hintHideTimer = null;
    }
    
    hint.style.opacity = '0';
    setTimeout(() => {
        hint.style.visibility = 'hidden';
    }, 500);
    
    console.log('Hint hidden manually');
}

// 切换提示语显示状态
function toggleHint() {
    const hint = document.getElementById('lightboxHint');
    if (!hint) return;
    
    const isVisible = hint.style.visibility !== 'hidden' && hint.style.opacity !== '0';
    
    if (isVisible) {
        hideHint();
    } else {
        showHint();
    }
}

// 放大倍数调整函数
window.adjustMagnifierZoom = function(delta) {
    window.dragState.magnifierZoom += delta;
    window.dragState.magnifierZoom = Math.max(1.5, Math.min(5, window.dragState.magnifierZoom));
    console.log('Magnifier zoom adjusted to:', window.dragState.magnifierZoom);
    
    // 如果放大镜正在显示，更新内容
    if (window.dragState.magnifierVisible) {
        // 可以在这里添加当前鼠标位置的跟踪
    }
};

// 测试函数
window.testDragFunction = function() {
    console.log('=== TESTING DRAG FUNCTION v3.0 ===');
    
    if (!images || images.length === 0) {
        console.error('No images available for testing');
        return;
    }
    
    showLightbox(0);
    
    setTimeout(() => {
        console.log('Current drag state:', window.dragState);
        console.log('Current mode:', window.dragState.isFullscreenMode ? 'FULLSCREEN (draggable)' : 'FIT (clickable)');
        console.log('Current image:', window.dragState.currentImage);
    }, 1000);
};

window.quickToggleTest = function() {
    console.log('=== QUICK TOGGLE TEST v3.0 ===');
    console.log('Before:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
    
    toggleImageMode();
    
    setTimeout(() => {
        console.log('After first toggle:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
        toggleImageMode();
        
        setTimeout(() => {
            console.log('After second toggle:', window.dragState.isFullscreenMode ? 'fullscreen' : 'fit');
        }, 500);
    }, 500);
};

window.debugCurrentState = function() {
    console.log('=== DEBUG CURRENT STATE v3.0 ===');
    console.log('Drag state:', window.dragState);
    console.log('Current image element:', window.dragState.currentImage);
    console.log('Lightbox visible:', document.getElementById('lightbox').style.display === 'flex');
};

// 新增：测试连续拖拽功能
window.testContinuousDrag = function() {
    console.log('=== TESTING CONTINUOUS DRAG v3.1 ===');
    
    if (!images || images.length === 0) {
        console.error('No images available for testing');
        return;
    }
    
    showLightbox(0);
    
    setTimeout(() => {
        console.log('Initial state:');
        console.log('- dragOffsetY:', window.dragState.dragOffsetY);
        console.log('- initialOffsetY:', window.dragState.initialOffsetY);
        console.log('现在请在图片上拖拽一段距离，然后松开鼠标，再重新拖拽试试。');
        console.log('图片应该从您松开鼠标的位置继续拖拽，而不是跳回原位。');
        console.log('');
        console.log('=== 放大镜功能测试 ===');
        console.log('按住 Ctrl 键并移动鼠标到图片上即可启用放大镜功能');
        console.log('当前放大倍数:', window.dragState.magnifierZoom);
        console.log('可以使用 adjustMagnifierZoom(0.5) 或 adjustMagnifierZoom(-0.5) 调整放大倍数');
    }, 1000);
};

// 新增：专门测试放大镜功能
window.testMagnifier = function() {
    console.log('=== TESTING MAGNIFIER FUNCTION v4.0 ===');
    
    if (!images || images.length === 0) {
        console.error('No images available for testing');
        return;
    }
    
    showLightbox(0);
    
    setTimeout(() => {
        console.log('放大镜功能已启用！');
        console.log('使用方法：');
        console.log('1. 按住 Ctrl 键');
        console.log('2. 将鼠标移动到图片上');
        console.log('3. 放大镜将显示在鼠标附近');
        console.log('4. 松开 Ctrl 键或移开鼠标即可隐藏放大镜');
        console.log('');
        console.log('当前设置：');
        console.log('- 放大倍数:', window.dragState.magnifierZoom);
        console.log('- 放大镜尺寸: 200x200 像素');
        console.log('');
        console.log('调整放大倍数：');
        console.log('- adjustMagnifierZoom(0.5)  // 增加放大倍数');
        console.log('- adjustMagnifierZoom(-0.5) // 减少放大倍数');
        console.log('');
        console.log('提示语控制：');
        console.log('- 按 H 键切换提示语显示/隐藏');
        console.log('- 按 ? 键显示帮助');
        console.log('- 提示语会在3秒后自动隐藏');
    }, 1000);
};

// 保持向后兼容的函数
function openLightbox(index) {
    showLightbox(index);
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.style.display = 'none';
    lightbox.style.visibility = 'hidden';
    document.body.style.overflow = 'auto';
    
    // 清理所有事件和状态
    cleanupAllEvents();
    window.dragState.reset();
    
    console.log('Lightbox closed v3.0');
}

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % images.length;
    showLightbox(currentImageIndex);
}

function prevImage() {
    currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
    showLightbox(currentImageIndex);
}

// 确保函数全局可用
window.showLightbox = showLightbox;
window.openLightbox = openLightbox;
window.closeLightbox = closeLightbox;
window.nextImage = nextImage;
window.prevImage = prevImage;
window.toggleImageMode = toggleImageMode;

// 键盘控制（灯箱专用）
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.style.display === 'flex') {
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'ArrowLeft') prevImage();
        if (e.key === 'Escape') closeLightbox();
        if (e.key === 'h' || e.key === 'H') toggleHint(); // 按 H 键切换提示语显示
        if (e.key === '?') showHint(); // 按 ? 键显示帮助
    }
});

// 图片分页查看功能
let currentPage = 1;
let imagesPerPage = 1; // 默认单张模式
let totalImages = images.length;
let totalPages = Math.ceil(totalImages / imagesPerPage);

// 查看模式切换
function changeViewMode(mode) {
    const buttons = document.querySelectorAll('.btn-group button');
    const customSettings = document.getElementById('customSettings');

    // 更新按钮状态
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (mode === 'custom') {
        // 显示自定义设置
        customSettings.style.display = 'block';
        return;
    } else {
        // 隐藏自定义设置
        if (customSettings) {
            customSettings.style.display = 'none';
        }

        // 设置每页图片数
        imagesPerPage = parseInt(mode);
    }

    // 重新计算分页
    totalPages = Math.ceil(totalImages / imagesPerPage);
    currentPage = 1;

    // 更新显示
    updateImageDisplay();
    updatePaginationControls();

    // 保存用户偏好
    localStorage.setItem('viewMode', mode);
    localStorage.setItem('imagesPerPage', imagesPerPage);
}

// 应用自定义设置
function applyCustomSettings() {
    const customPerPage = document.getElementById('customPerPage');
    const newPerPage = parseInt(customPerPage.value);

    if (newPerPage >= 1 && newPerPage <= 50) {
        imagesPerPage = newPerPage;
        totalPages = Math.ceil(totalImages / imagesPerPage);
        currentPage = 1;

        updateImageDisplay();
        updatePaginationControls();

        // 保存设置
        localStorage.setItem('imagesPerPage', imagesPerPage);
        localStorage.setItem('viewMode', 'custom');

        showToast('设置已保存');
    } else {
        showToast('请输入1-50之间的数字', 'error');
    }
}

// 更新图片显示
function updateImageDisplay() {
    const gallery = document.getElementById('imageGallery');
    const startIndex = (currentPage - 1) * imagesPerPage;
    const endIndex = Math.min(startIndex + imagesPerPage, totalImages);

    // 清空画廊
    gallery.innerHTML = '';

    // 根据每页图片数设置布局
    if (imagesPerPage === 1) {
        gallery.className = 'image-gallery single-mode';
    } else if (imagesPerPage <= 6) {
        gallery.className = 'image-gallery grid-mode grid-small';
    } else {
        gallery.className = 'image-gallery grid-mode grid-large';
    }

    // 添加当前页的图片
    for (let i = startIndex; i < endIndex; i++) {
        const image = images[i];
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';
        imageItem.setAttribute('data-index', i);

        // 使用最简单的innerHTML方式，避免复杂的事件绑定
        imageItem.innerHTML =
            '<img src=\"' + image.url + '\" ' +
                 'alt=\"' + (image.original_name || '图片' + (i + 1)) + '\" ' +
                 'loading=\"lazy\" ' +
                 'style=\"cursor: pointer; width: 100%; height: auto;\" ' +
                 'data-index=\"' + i + '\">' +
            '<div class=\"image-overlay\">' +
                '<div class=\"image-number\">' + (i + 1) + '</div>' +
                '<div class=\"image-actions\">' +
                    '<button class=\"btn btn-dark btn-sm\" onclick=\"downloadImage(' + JSON.stringify(image.url) + ', ' + (i + 1) + ')\">' +
                        '<i class=\"fas fa-download\"></i>' +
                    '</button>' +
                '</div>' +
            '</div>';

        gallery.appendChild(imageItem);
    }

    // 在所有图片添加完成后，统一绑定点击事件
    console.log('About to bind click events...');

    // 立即绑定事件，不使用setTimeout
    const allImages = gallery.querySelectorAll('img[data-index]');
    console.log('Found images for binding:', allImages.length);

    allImages.forEach(function(img, imgIndex) {
        const index = parseInt(img.getAttribute('data-index'));
        console.log('Binding event to image', imgIndex, 'with data-index', index);

        // 使用addEventListener而不是onclick
        img.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Image clicked via addEventListener, index:', index);
            showLightbox(index);
            return false;
        });

        // 同时也设置onclick作为备用
        img.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Image clicked via onclick, index:', index);
            showLightbox(index);
            return false;
        };

        // 添加视觉反馈和确保可点击
        img.style.cursor = 'pointer';
        img.style.position = 'relative';
        img.style.zIndex = '10';
        img.style.pointerEvents = 'auto';
        img.title = '点击查看大图';

        // 确保父容器不阻挡点击，并且也绑定点击事件
        const imageItem = img.parentElement;
        if (imageItem && imageItem.classList.contains('image-item')) {
            imageItem.style.pointerEvents = 'auto';
            imageItem.style.cursor = 'pointer';

            // 为整个容器也绑定点击事件
            imageItem.onclick = function(e) {
                // 如果点击的是下载按钮，不触发灯箱
                if (e.target.closest('.image-actions') || e.target.closest('button')) {
                    return;
                }
                e.preventDefault();
                e.stopPropagation();
                console.log('Image container clicked, index:', index);
                showLightbox(index);
                return false;
            };
        }
    });

    console.log('Click events bound to', allImages.length, 'images');
}

// 更新分页控制
function updatePaginationControls() {
    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('totalPages').textContent = totalPages;

    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        updateImageDisplay();
        updatePaginationControls();
    }
}

// 下一页
function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        updateImageDisplay();
        updatePaginationControls();
    }
}

// 下载功能
function downloadImage(url, index) {
    try {
        const link = document.createElement('a');
        link.href = url;
        link.download = albumTitle + '_' + index + '.webp';
        link.target = '_blank';
        link.rel = 'noopener noreferrer';

        // 添加到页面并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('开始下载图片:', index);
    } catch (error) {
        console.error('下载失败:', error);
        alert('下载失败，请尝试右键保存图片');
    }
}

window.downloadCurrentImage = function() {
    downloadImage(images[currentImageIndex].url, currentImageIndex + 1);
};

function downloadAlbum() {
    if (confirm('确定要下载整个套图吗？\\n\\n将会打包成ZIP文件下载，共 ' + images.length + ' 张图片')) {
        // 显示下载进度
        const progressDiv = document.createElement('div');
        progressDiv.id = 'downloadProgress';
        progressDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.9); color: white; padding: 20px; border-radius: 8px; z-index: 10000; font-family: Arial, sans-serif; min-width: 300px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);';
        progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"animation: fa-spin 2s infinite linear;\"></i> 正在打包下载...</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">请稍候，正在处理 ' + images.length + ' 张图片</div><style>@keyframes fa-spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>';
        document.body.appendChild(progressDiv);

        // 发送获取图片列表请求
        const formData = new FormData();
        formData.append('album_id', window.location.pathname.split('/').pop());

        fetch('/api/download-album-simple.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-check-circle\" style=\"color: #28a745;\"></i> 准备下载！</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">找到 ' + data.total_images + ' 张图片</div><div style=\"margin-top: 10px;\"><button onclick=\"startBatchDownload()\" data-images=\"' + encodeURIComponent(JSON.stringify(data.images)) + '\" data-title=\"' + data.album_title + '\" style=\"background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">开始下载</button></div>';
            } else {
                throw new Error(data.error || '获取图片列表失败');
            }
        })
        .catch(error => {
            console.error('下载失败:', error);
            progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-exclamation-triangle\" style=\"color: #dc3545;\"></i> 下载失败</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">' + error.message + '</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';
        });
    }
}

function startBatchDownload() {
    const button = event.target;
    const images = JSON.parse(decodeURIComponent(button.dataset.images));
    const title = button.dataset.title;

    const progressDiv = document.getElementById('downloadProgress');
    let downloaded = 0;
    const total = images.length;

    progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-download\" style=\"color: #007bff;\"></i> 正在下载...</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">进度: <span id=\"downloadCount\">0</span>/' + total + '</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';

    // 逐个下载图片
    images.forEach((image, index) => {
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = image.url;
            link.download = image.filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            downloaded++;
            document.getElementById('downloadCount').textContent = downloaded;

            if (downloaded === total) {
                setTimeout(() => {
                    progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-check-circle\" style=\"color: #28a745;\"></i> 下载完成！</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">已下载 ' + total + ' 张图片</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';
                }, 1000);
            }
        }, index * 500); // 每500ms下载一张，避免浏览器限制
    });
}

function startDownload(downloadUrl, filename) {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 延迟关闭进度窗口
    setTimeout(() => {
        closeDownloadProgress();
    }, 2000);
}

function closeDownloadProgress() {
    const progressDiv = document.getElementById('downloadProgress');
    if (progressDiv && progressDiv.parentNode) {
        progressDiv.parentNode.removeChild(progressDiv);
    }
}

// 分享功能
function shareAlbum() {
    if (navigator.share) {
        navigator.share({
            title: albumTitle,
            text: albumDescription,
            url: window.location.href
        });
    } else {
        copyLink();
    }
}

function shareToWeibo() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(albumTitle);
    window.open('https://service.weibo.com/share/share.php?url=' + url + '&title=' + title);
}

function shareToWechat() {
    alert('请复制链接后在微信中分享');
    copyLink();
}

function shareToQQ() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(albumTitle);
    window.open('https://connect.qq.com/widget/shareqq/index.html?url=' + url + '&title=' + title);
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        showToast('链接已复制到剪贴板');
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 恢复用户偏好设置
    const savedMode = localStorage.getItem('viewMode') || '1';
    const savedPerPage = localStorage.getItem('imagesPerPage') || '1';

    imagesPerPage = parseInt(savedPerPage);
    totalPages = Math.ceil(totalImages / imagesPerPage);

    // 更新按钮状态
    const buttons = document.querySelectorAll('.btn-group button');
    buttons.forEach((btn, index) => {
        btn.classList.remove('active');
        const modes = ['1', '6', '12', 'custom'];
        if (modes[index] === savedMode) {
            btn.classList.add('active');
        }
    });

    // 如果是自定义模式，显示设置面板
    if (savedMode === 'custom') {
        const customSettings = document.getElementById('customSettings');
        if (customSettings) {
            customSettings.style.display = 'block';
            document.getElementById('customPerPage').value = imagesPerPage;
        }
    }

    // 初始化显示
    updateImageDisplay();
    updatePaginationControls();

    // 调试：检查图片是否正确创建
    setTimeout(function() {
        console.log('=== Post-load debug ===');
        const gallery = document.getElementById('imageGallery');
        const images = gallery ? gallery.querySelectorAll('img') : [];
        console.log('Gallery found:', !!gallery);
        console.log('Images found:', images.length);
        if (images.length > 0) {
            console.log('First image onclick:', images[0].onclick);
            console.log('First image src:', images[0].src);
        }
    }, 1000);

    // 页面卸载时清理事件监听器
    window.addEventListener('beforeunload', function() {
        document.onmousemove = null;
        document.onmouseup = null;
        document.body.classList.remove('dragging');
    });

    // 键盘快捷键（分页专用）
    document.addEventListener('keydown', function(e) {
        const lightbox = document.getElementById('lightbox');
        // 只有在灯箱未显示时才处理分页快捷键
        if (!lightbox || lightbox.style.display !== 'flex') {
            if (e.key === 'ArrowLeft') {
                previousPage();
            } else if (e.key === 'ArrowRight') {
                nextPage();
            }
        }
    });

// 收藏功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-favorite')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-favorite');
        const type = btn.dataset.type;
        const id = btn.dataset.id;

        if (!type || !id) return;

        btn.disabled = true;

        fetch('/api/favorites.php?action=toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: type,
                id: id
            })
        })
        .then(response => {
            console.log('收藏API响应状态:', response.status);
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            console.log('收藏API响应数据:', data);
            if (data.success) {
                const icon = btn.querySelector('i');
                const countEl = btn.querySelector('.count');

                if (data.action === 'added') {
                    btn.classList.add('active');
                    icon.className = 'fas fa-heart me-1';
                    if (countEl) {
                        let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                        countEl.textContent = (count + 1).toLocaleString();
                    }
                } else if (data.action === 'removed') {
                    btn.classList.remove('active');
                    icon.className = 'far fa-heart me-1';
                    if (countEl) {
                        let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                        countEl.textContent = Math.max(0, count - 1).toLocaleString();
                    }
                }

                alert(data.message);
            } else {
                alert(data.message || data.error || '操作失败');
            }
        })
        .catch(error => {
            console.error('收藏操作失败:', error);
            alert('网络错误，请重试');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }
});

// 点赞功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-like')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-like');
        const type = btn.dataset.type;
        const id = btn.dataset.id;

        if (!type || !id) return;

        btn.disabled = true;

        fetch('/api/like.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: type,
                id: id
            })
        })
        .then(response => {
            console.log('点赞API响应状态:', response.status);
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            console.log('点赞API响应数据:', data);
            if (data.success) {
                const icon = btn.querySelector('i');
                const countEl = btn.querySelector('.count');

                if (data.action === 'liked') {
                    btn.classList.add('active');
                    icon.className = 'fas fa-thumbs-up me-1';
                } else {
                    btn.classList.remove('active');
                    icon.className = 'far fa-thumbs-up me-1';
                }

                if (countEl) {
                    countEl.textContent = data.count.toLocaleString();
                }

                alert(data.message);
            } else {
                alert(data.message || '操作失败');
            }
        })
        .catch(error => {
            console.error('点赞操作失败:', error);
            alert('网络错误，请重试');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }
});

});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
