<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../includes/functions.php';

$albumModel = new Album();
$userModel = new User();
$currentUser = getCurrentUser();

$albumId = intval($_GET['id'] ?? 0);
if (!$albumId) {
    header('Location: /albums');
    exit;
}

// 获取套图详情
$album = $albumModel->getById($albumId);
if (!$album) {
    header('HTTP/1.0 404 Not Found');
    include __DIR__ . '/404.php';
    exit;
}

// 检查查看权限
$canView = $albumModel->canView($album['id'], $currentUser['id'] ?? 0);
if (!$canView) {
    // 如果未登录，跳转到登录页
    if (!$currentUser) {
        $_SESSION['redirect_after_login'] = "/album/{$albumId}";
        header('Location: /login');
        exit;
    } else {
        // 已登录但权限不足，显示升级提示
        $pageTitle = '权限不足 - ' . Config::getSiteName();
        $errorMessage = '此套图需要VIP权限才能查看';
        include __DIR__ . '/permission_denied.php';
        exit;
    }
}

// 增加浏览量
$albumModel->increaseViewCount($album['id']);

// 获取套图图片（获取所有图片，不分页）
$images = $albumModel->getImages($album['id'], 1, 1000);

// 获取用户互动状态
$userInteraction = [
    'is_liked' => false,
    'is_favorited' => false,
    'is_vip' => false,
];

if ($currentUser) {
    $userInteraction = $albumModel->getUserInteraction($album['id'], $currentUser['id']);
    // 检查用户VIP状态
    $userInteraction['is_vip'] = !empty($currentUser['vip_expires']) && strtotime($currentUser['vip_expires']) > time();
}

// 获取相关套图推荐
$relatedAlbums = $albumModel->getRelated($album['id'], 8);

// 设置页面信息
$pageTitle = $album['title'] . ' - ' . Config::getSiteName();
$pageDescription = mb_substr($album['description'], 0, 150);
$pageKeywords = $album['tags'] ?: '';
$pageImage = $album['cover'];
$pageCSS = ['/assets/css/album-detail.css?v=' . time()];

// 页面内容
ob_start();
?>

<div class="container">
    <div class="row">
        <!-- 主要内容 -->
        <div class="col-lg-8">
            <!-- 套图信息 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h1 class="h4 mb-2"><?php echo htmlspecialchars($album['title'] ?? ''); ?></h1>
                            <div class="text-muted">
                                <span class="me-3">
                                    <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($album['category_name'] ?? '未分类'); ?>
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-calendar me-1"></i><?php
                                    $dateTime = $album['published_at'] ?? $album['created_at'] ?? date('Y-m-d H:i:s');
                                    echo date('Y-m-d', strtotime($dateTime));
                                    ?>
                                </span>
                                <span class="me-3">
                                    <i class="fas fa-images me-1"></i><?php echo count($images); ?>张
                                </span>
                                <?php if (!$album['is_free']): ?>
                                <span class="badge bg-warning text-dark">VIP</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="btn-group">
                            <button class="btn btn-outline-warning btn-sm btn-like <?php echo $userInteraction['is_liked'] ? 'active' : ''; ?>" 
                                    data-type="album" data-id="<?php echo $album['id']; ?>">
                                <i class="fas fa-thumbs-up me-1"></i>
                                <span class="count"><?php echo number_format($album['like_count']); ?></span>
                            </button>
                            <button class="btn btn-outline-warning btn-sm btn-favorite <?php echo $userInteraction['is_favorited'] ? 'active' : ''; ?>" 
                                    data-type="album" data-id="<?php echo $album['id']; ?>">
                                <i class="fas fa-heart me-1"></i>
                                <span class="count"><?php echo number_format($album['favorite_count']); ?></span>
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="shareAlbum()">
                                <i class="fas fa-share-alt me-1"></i>分享
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="downloadAlbum()">
                                <i class="fas fa-download me-1"></i>下载
                            </button>
                        </div>
                    </div>
                    
                    <div class="stats d-flex">
                        <span class="me-4 text-muted">
                            <i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?> 浏览
                        </span>
                        <span class="me-4 text-muted">
                            <i class="fas fa-download me-1"></i><?php echo number_format($album['download_count']); ?> 下载
                        </span>
                    </div>
                </div>
            </div>

            <!-- 图片展示 -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">图片浏览</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" onclick="changeViewMode(1)">
                                <i class="fas fa-image"></i> 单张
                            </button>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode(6)">
                                <i class="fas fa-th"></i> 6张/页
                            </button>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode(12)">
                                <i class="fas fa-th-large"></i> 12张/页
                            </button>
                            <?php if ($userInteraction['is_vip']): ?>
                            <button class="btn btn-outline-secondary" onclick="changeViewMode('custom')">
                                <i class="fas fa-cog"></i> 自定义
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- 图片显示区域 -->
                    <div id="imageGallery" class="image-gallery">
                        <!-- 图片将通过JavaScript动态加载 -->
                    </div>

                    <!-- 分页控制 -->
                    <div id="paginationControls" class="d-flex justify-content-between align-items-center mt-4">
                        <div class="pagination-info">
                            <span class="text-muted">
                                第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                                (共 <?php echo count($images); ?> 张图片)
                            </span>
                        </div>
                        <div class="pagination-buttons">
                            <button id="prevBtn" class="btn btn-outline-secondary btn-sm me-2" onclick="previousPage()" disabled>
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <button id="nextBtn" class="btn btn-outline-secondary btn-sm" onclick="nextPage()">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- VIP自定义设置 -->
                    <?php if ($userInteraction['is_vip']): ?>
                    <div id="customSettings" class="mt-3" style="display: none;">
                        <div class="card bg-dark border-secondary">
                            <div class="card-body">
                                <h6 class="text-warning">VIP自定义设置</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">每页显示图片数：</label>
                                        <input type="number" id="customPerPage" class="form-control" min="1" max="50" value="6">
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button class="btn btn-warning btn-sm" onclick="applyCustomSettings()">
                                            <i class="fas fa-check"></i> 应用设置
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 套图描述 -->
            <?php if ($album['description']): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <h5>套图简介</h5>
                    <div class="album-description">
                        <?php echo nl2br(htmlspecialchars($album['description'] ?? '')); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 标签 -->
            <?php if ($album['tags']): ?>
            <div class="card mb-4">
                <div class="card-body">
                    <h5>相关标签</h5>
                    <div class="tags">
                        <?php 
                        $tags = json_decode($album['tags'], true) ?: [];
                        foreach ($tags as $tag): ?>
                        <a href="/albums?keyword=<?php echo urlencode($tag); ?>" 
                           class="badge bg-secondary text-decoration-none me-2 mb-2">
                            #<?php echo htmlspecialchars($tag ?? ''); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 评论区 -->
            <div class="card">
                <div class="card-body">
                    <h5>评论 <span class="text-muted">(0)</span></h5>
                    
                    <?php if ($currentUser): ?>
                    <form class="comment-form mb-4">
                        <div class="mb-3">
                            <textarea class="form-control" rows="3" placeholder="写下你的评论..."></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">支持 Markdown 语法</small>
                            <button type="submit" class="btn btn-warning">发表评论</button>
                        </div>
                    </form>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <p class="text-muted">登录后可以发表评论</p>
                        <a href="/login" class="btn btn-warning">立即登录</a>
                    </div>
                    <?php endif; ?>

                    <div class="comments-list">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>暂无评论，来抢沙发吧~</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 相关推荐 -->
            <?php if (!empty($relatedAlbums)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-thumbs-up me-2"></i>推荐套图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <?php foreach ($relatedAlbums as $related): ?>
                        <div class="col-6">
                            <div class="related-album">
                                <a href="/album/<?php echo $related['id']; ?>">
                                    <img src="<?php echo htmlspecialchars($related['cover'] ?? ''); ?>"
                                         alt="<?php echo htmlspecialchars($related['title'] ?? ''); ?>"
                                         class="img-fluid rounded">
                                    <div class="related-overlay">
                                        <div class="related-title">
                                            <?php echo htmlspecialchars(mb_substr($related['title'] ?? '', 0, 20)); ?>
                                        </div>
                                        <div class="related-stats">
                                            <small>
                                                <i class="fas fa-eye me-1"></i><?php echo number_format($related['view_count']); ?>
                                                <i class="fas fa-images ms-2 me-1"></i><?php echo $related['image_count']; ?>
                                            </small>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- 下载提示 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>下载说明
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>支持单张下载和批量下载
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>原图高清无水印
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>WebP格式节省流量
                        </li>
                        <?php if (!$album['is_free']): ?>
                        <li class="mb-0">
                            <i class="fas fa-crown text-warning me-2"></i>VIP专享高速下载
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- 分享套图 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-share-alt me-2"></i>分享套图
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="shareToWeibo()">
                            <i class="fab fa-weibo me-2"></i>新浪微博
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="shareToWechat()">
                            <i class="fab fa-weixin me-2"></i>微信朋友圈
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="shareToQQ()">
                            <i class="fab fa-qq me-2"></i>QQ空间
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyLink()">
                            <i class="fas fa-link me-2"></i>复制链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图片灯箱 -->
<div id="lightbox" class="lightbox" onclick="closeLightbox()">
    <div class="lightbox-content">
        <div class="lightbox-header">
            <div class="lightbox-title"></div>
            <div class="lightbox-actions">
                <button class="btn btn-dark btn-sm" onclick="downloadCurrentImage()">
                    <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-dark btn-sm" onclick="closeLightbox()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="lightbox-body">
            <img id="lightboxImage" src="" alt="" onclick="event.stopPropagation()">
            <div class="lightbox-nav">
                <button class="lightbox-prev" onclick="prevImage(); event.stopPropagation()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="lightbox-next" onclick="nextImage(); event.stopPropagation()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="lightbox-hint" id="lightboxHint" style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; opacity: 0.8; pointer-events: none; z-index: 1000;">
                拖拽图片查看完整内容 | 点击切换显示模式 | 左右箭头切换图片
            </div>
        </div>
        <div class="lightbox-footer">
            <div class="lightbox-counter"></div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$albumTitle = json_encode($album['title']);
$albumDescription = json_encode(mb_substr($album['description'], 0, 100));

$pageScript = "
// 拖拽功能修复版本 v2.0 - " . date('Y-m-d H:i:s') . "
console.log('Loading NEW drag fix version 2.0 at " . date('H:i:s') . "');

// 使用var声明避免重复声明错误
var images = " . json_encode($images) . ";
var albumTitle = " . $albumTitle . ";
var albumDescription = " . $albumDescription . ";
var currentImageIndex = 0;

// 图片灯箱
var isFullscreen = false;

// 确保DOM加载完成
console.log('Page script loaded');
console.log('Images:', images);
console.log('Album title:', albumTitle);

// 测试函数
window.testLightbox = function() {
    console.log('Testing lightbox...');
    if (images && images.length > 0) {
        openLightbox(0);
    } else {
        console.error('No images available');
    }
};

// 强制触发第一张图片点击
window.forceClickFirstImage = function() {
    console.log('Forcing click on first image...');
    showLightbox(0);
};

// 简单的测试函数
window.testLightboxSimple = function() {
    console.log('Testing simple lightbox...');
    console.log('Images available:', images ? images.length : 0);
    if (images && images.length > 0) {
        showLightbox(0);
    } else {
        console.error('No images available for testing');
    }
};

// 调试函数
window.debugImages = function() {
    console.log('=== Debug Images ===');
    const gallery = document.getElementById('imageGallery');
    console.log('Gallery element:', gallery);
    console.log('Gallery class:', gallery ? gallery.className : 'not found');

    const imgElements = gallery ? gallery.querySelectorAll('img') : [];
    console.log('Image elements count:', imgElements.length);

    if (imgElements.length > 0) {
        const firstImg = imgElements[0];
        console.log('First image src:', firstImg.src);
        console.log('First image onclick:', firstImg.onclick);
        console.log('First image event listeners:', getEventListeners ? getEventListeners(firstImg) : 'getEventListeners not available');
    }

    console.log('Current page:', currentPage);
    console.log('Images per page:', imagesPerPage);
    console.log('Total images:', totalImages);
};

// 简单直接的灯箱显示函数
var isFullscreenMode = true; // 默认全屏模式

function showLightbox(index) {
    console.log('showLightbox called with index:', index);

    if (!images || !images[index]) {
        console.error('Invalid image index:', index);
        return;
    }

    currentImageIndex = index;
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxTitle = document.querySelector('.lightbox-title');
    const lightboxCounter = document.querySelector('.lightbox-counter');

    if (!lightbox || !lightboxImage) {
        console.error('Lightbox elements not found');
        return;
    }

    // 设置图片和信息
    lightboxImage.src = images[index].url;
    if (lightboxTitle) lightboxTitle.textContent = albumTitle + ' - ' + (index + 1);
    if (lightboxCounter) lightboxCounter.textContent = (index + 1) + ' / ' + images.length;

    // 设置图片事件处理
    setupImageEvents(lightboxImage);

    // 应用当前显示模式
    const updatedImage = applyImageMode();
    if (updatedImage) {
        // 确保使用更新后的图片引用
        window.lightboxImage = updatedImage;
    }

    // 显示灯箱
    lightbox.style.display = 'flex';
    lightbox.style.visibility = 'visible';
    document.body.style.overflow = 'hidden';

    console.log('Lightbox displayed');
}

// 切换图片显示模式
function toggleImageMode() {
    isFullscreenMode = !isFullscreenMode;
    const updatedImage = applyImageMode();
    if (updatedImage) {
        // 确保使用更新后的图片引用
        window.lightboxImage = updatedImage;
    }
    console.log('Image mode toggled to:', isFullscreenMode ? 'fullscreen' : 'fit');
}

// 应用图片显示模式
function applyImageMode() {
    const lightboxImage = document.getElementById('lightboxImage');
    if (!lightboxImage) return;

    console.log('Applying image mode:', isFullscreenMode ? 'fullscreen' : 'fit');

    if (isFullscreenMode) {
        // 全屏填充模式 - 以宽度为主，允许拖拽查看高度超出的部分
        lightboxImage.style.width = '100vw';
        lightboxImage.style.height = 'auto';
        lightboxImage.style.minHeight = '100vh';
        lightboxImage.style.objectFit = 'cover';
        lightboxImage.style.position = 'absolute';
        lightboxImage.style.top = '0';
        lightboxImage.style.left = '0';
        lightboxImage.style.cursor = 'grab';

        // 保持或初始化拖拽位置
        if (typeof lightboxImage.dragOffsetY === 'undefined') {
            lightboxImage.dragOffsetY = 0;
        }
        // 应用当前的拖拽位置
        lightboxImage.style.transform = 'translateY(' + lightboxImage.dragOffsetY + 'px)';
    } else {
        // 适应模式
        lightboxImage.style.width = 'auto';
        lightboxImage.style.height = 'auto';
        lightboxImage.style.maxWidth = '100%';
        lightboxImage.style.maxHeight = '100%';
        lightboxImage.style.objectFit = 'contain';
        lightboxImage.style.position = 'relative';
        lightboxImage.style.top = 'auto';
        lightboxImage.style.left = 'auto';
        lightboxImage.style.cursor = 'pointer';
        lightboxImage.style.transform = 'none';
    }

    // 重新设置事件处理，并获取更新后的元素引用
    console.log('About to call setupImageEvents, isFullscreenMode:', isFullscreenMode);
    const updatedImage = setupImageEvents(lightboxImage);
    if (updatedImage) {
        // 更新全局引用，确保后续操作使用新的元素引用
        window.lightboxImage = updatedImage;
        console.log('Updated global lightboxImage reference');
        // 返回更新后的引用，以便调用者使用
        return updatedImage;
    }
    return lightboxImage;
}

// 全局事件处理器存储
let currentImageEventHandlers = {
    mousedown: null,
    mousemove: null,
    mouseup: null,
    touchstart: null,
    touchmove: null,
    touchend: null,
    click: null
};

// 清理图片上的所有事件监听器
function cleanupImageEvents(img) {
    if (!img) return;

    console.log('=== cleanupImageEvents START ===');
    console.log('Cleaning events for image:', img.id);

    // 清理可能存在的全局事件监听器
    document.body.classList.remove('dragging');

    // 移除所有已注册的事件监听器
    Object.keys(currentImageEventHandlers).forEach(eventType => {
        if (currentImageEventHandlers[eventType]) {
            console.log('Removing event:', eventType);
            if (eventType === 'mousemove' || eventType === 'mouseup') {
                document.removeEventListener(eventType, currentImageEventHandlers[eventType]);
            } else {
                // 对于touchend事件，需要考虑passive选项
                if (eventType === 'touchend') {
                    img.removeEventListener(eventType, currentImageEventHandlers[eventType], { passive: false });
                } else {
                    img.removeEventListener(eventType, currentImageEventHandlers[eventType]);
                }
            }
            currentImageEventHandlers[eventType] = null;
        }
    });

    console.log('=== cleanupImageEvents END ===');
}

// 设置图片事件处理（根据当前模式）- 不替换DOM元素
function setupImageEvents(img) {
    if (!img) return img;

    console.log('=== setupImageEvents START ===');
    console.log('Mode:', isFullscreenMode ? 'fullscreen' : 'fit');
    console.log('Image element:', img);
    console.log('Current dragOffset:', img.dragOffsetY || 0);
    console.log('Image ID:', img.id);

    // 清理之前的事件监听器
    cleanupImageEvents(img);

    if (isFullscreenMode) {
        console.log('Setting up DRAG events');
        // 全屏模式：设置拖拽功能
        setupDragEvents(img);
    } else {
        console.log('Setting up CLICK events');
        // 适应模式：只设置点击切换
        setupClickEvents(img);
    }

    console.log('=== setupImageEvents END ===');
    // 返回相同的图片元素引用（不替换DOM）
    return img;
}

// 设置拖拽事件（全屏模式）- 全新实现
function setupDragEvents(img) {
    console.log('=== NEW setupDragEvents START ===');

    // 初始化拖拽偏移
    if (typeof img.dragOffsetY === 'undefined') {
        img.dragOffsetY = 0;
    }

    let isDragging = false;
    let startY = 0;
    let startOffsetY = 0;
    let clickStartTime = 0;
    let clickStartY = 0;

    // 简单直接的鼠标按下处理
    function handleMouseDown(e) {
        console.log('NEW MOUSEDOWN - isFullscreenMode:', isFullscreenMode);

        if (!isFullscreenMode) {
            e.preventDefault();
            e.stopPropagation();
            toggleImageMode();
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        isDragging = false;
        startY = e.clientY;
        startOffsetY = img.dragOffsetY || 0;
        clickStartTime = Date.now();
        clickStartY = e.clientY;

        document.body.classList.add('dragging');
        img.style.cursor = 'grabbing';

        // 简单的鼠标移动处理
        function handleMouseMove(moveEvent) {
            if (!isDragging && Math.abs(moveEvent.clientY - clickStartY) > 5) {
                isDragging = true;
                console.log('NEW: Started dragging');
            }

            if (isDragging) {
                const deltaY = moveEvent.clientY - startY;
                const newOffsetY = startOffsetY + deltaY;

                // 简化的范围限制
                const imgHeight = img.offsetHeight;
                const containerHeight = window.innerHeight;

                let finalOffset = newOffsetY;
                if (imgHeight > containerHeight) {
                    // 图片比容器高，允许拖拽
                    const minOffset = containerHeight - imgHeight;
                    const maxOffset = 0;
                    finalOffset = Math.max(minOffset, Math.min(maxOffset, newOffsetY));
                } else {
                    // 图片比容器小，不允许拖拽
                    finalOffset = 0;
                }

                img.dragOffsetY = finalOffset;
                img.style.transform = 'translateY(' + finalOffset + 'px)';
                console.log('NEW: Dragging to', finalOffset);
            }
        }

        // 简单的鼠标释放处理
        function handleMouseUp(upEvent) {
            console.log('NEW MOUSEUP');

            // 立即清理事件
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);

            document.body.classList.remove('dragging');
            img.style.cursor = 'grab';

            // 检查是否是点击
            const clickDuration = Date.now() - clickStartTime;
            const clickDistance = Math.abs(upEvent.clientY - clickStartY);

            if (!isDragging && clickDuration < 300 && clickDistance < 5) {
                console.log('NEW: Click detected, switching mode');
                setTimeout(() => toggleImageMode(), 50);
            }

            isDragging = false;
        }

        // 绑定事件
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        console.log('NEW: Events bound');
    }

    // 绑定到图片
    img.addEventListener('mousedown', handleMouseDown);
    console.log('NEW: Mousedown bound to image');

    // 存储引用以便清理
    currentImageEventHandlers.mousedown = handleMouseDown;

    // 触摸事件支持 - 优化版本
    let touchStartTime = 0;
    let touchStartY = 0;
    let touchIsDragging = false;
    let touchStartOffsetY = 0;

    img.addEventListener('touchstart', function(e) {
        if (!isFullscreenMode) {
            // 非全屏模式，延迟执行切换以避免闪烁
            setTimeout(function() {
                toggleImageMode();
            }, 50);
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        const touch = e.touches[0];
        touchIsDragging = false;
        touchStartY = touch.clientY;
        touchStartOffsetY = img.dragOffsetY || 0;
        touchStartTime = Date.now();


    }, { passive: false });

    img.addEventListener('touchmove', function(e) {
        if (!isFullscreenMode) return;

        e.preventDefault();
        e.stopPropagation();

        const touch = e.touches[0];
        const deltaY = touch.clientY - touchStartY;

        // 更严格的拖拽检测
        if (!touchIsDragging && Math.abs(deltaY) > 10) {
            touchIsDragging = true;

        }

        if (touchIsDragging) {
            const newOffsetY = touchStartOffsetY + deltaY;

            const imgHeight = img.offsetHeight;
            const containerHeight = window.innerHeight;

            const maxOffset = 0;
            const minOffset = Math.min(0, containerHeight - imgHeight);

            img.dragOffsetY = Math.max(minOffset, Math.min(maxOffset, newOffsetY));
            img.style.transform = 'translateY(' + img.dragOffsetY + 'px)';
        }
    }, { passive: false });

    img.addEventListener('touchend', function(e) {
        if (!isFullscreenMode) return;

        e.preventDefault();
        e.stopPropagation();

        const touchDuration = Date.now() - touchStartTime;
        const touch = e.changedTouches[0];
        const touchDistance = Math.abs(touch.clientY - touchStartY);

        // 更严格的点击检测条件
        if (!touchIsDragging && touchDuration < 500 && touchDistance < 15) {
            // 延迟执行以确保稳定性
            setTimeout(function() {
                toggleImageMode();
            }, 100);
        }

        touchIsDragging = false;
    }, { passive: false });
}

// 设置点击事件（适应模式）
function setupClickEvents(img) {
    console.log('Setting up click events for fit mode');

    // 点击事件处理器
    const clickHandler = function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Image clicked in fit mode, switching to fullscreen');
        toggleImageMode();
    };

    // 触摸事件处理器
    const touchEndHandler = function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Image touched in fit mode, switching to fullscreen');
        setTimeout(function() {
            toggleImageMode();
        }, 100);
    };

    // 存储事件处理器并绑定
    currentImageEventHandlers.click = clickHandler;
    currentImageEventHandlers.touchend = touchEndHandler;

    img.addEventListener('click', currentImageEventHandlers.click);
    img.addEventListener('touchend', currentImageEventHandlers.touchend, { passive: false });
}

// 保持原有的openLightbox函数作为别名
function openLightbox(index) {
    showLightbox(index);
}

// 同时添加到window对象
window.openLightbox = openLightbox;

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.style.display = 'none';
    lightbox.style.visibility = 'hidden';
    document.body.style.overflow = 'auto';
    isFullscreen = false;

    // 清理可能残留的事件监听器
    document.body.classList.remove('dragging');

    // 清理全局鼠标事件监听器
    const allMouseMoveListeners = document.querySelectorAll('*');
    document.onmousemove = null;
    document.onmouseup = null;

    console.log('Lightbox closed and events cleaned up');
}
window.closeLightbox = closeLightbox;

function toggleFullscreen() {
    console.log('Toggling fullscreen, current state:', isFullscreen);
    const lightboxContent = document.querySelector('.lightbox-content');

    if (!lightboxContent) {
        console.error('Lightbox content not found');
        return;
    }

    isFullscreen = !isFullscreen;

    if (isFullscreen) {
        lightboxContent.classList.remove('windowed');
        console.log('Switched to fullscreen');
    } else {
        lightboxContent.classList.add('windowed');
        console.log('Switched to windowed');
    }
}
window.toggleFullscreen = toggleFullscreen;

function nextImage() {
    currentImageIndex = (currentImageIndex + 1) % images.length;
    showLightbox(currentImageIndex);
};

function prevImage() {
    currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
    showLightbox(currentImageIndex);
}

// 确保函数全局可用
window.showLightbox = showLightbox;
window.openLightbox = openLightbox;
window.nextImage = nextImage;
window.prevImage = prevImage;
window.toggleImageMode = toggleImageMode;
window.applyImageMode = applyImageMode;
window.setupImageEvents = setupImageEvents;
window.setupDragEvents = setupDragEvents;
window.setupClickEvents = setupClickEvents;

// 键盘控制（灯箱专用）
document.addEventListener('keydown', function(e) {
    const lightbox = document.getElementById('lightbox');
    if (lightbox && lightbox.style.display === 'flex') {
        if (e.key === 'ArrowRight') nextImage();
        if (e.key === 'ArrowLeft') prevImage();
        if (e.key === 'Escape') closeLightbox();
    }
});

// 图片分页查看功能
let currentPage = 1;
let imagesPerPage = 1; // 默认单张模式
let totalImages = images.length;
let totalPages = Math.ceil(totalImages / imagesPerPage);

// 查看模式切换
function changeViewMode(mode) {
    const buttons = document.querySelectorAll('.btn-group button');
    const customSettings = document.getElementById('customSettings');

    // 更新按钮状态
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (mode === 'custom') {
        // 显示自定义设置
        customSettings.style.display = 'block';
        return;
    } else {
        // 隐藏自定义设置
        if (customSettings) {
            customSettings.style.display = 'none';
        }

        // 设置每页图片数
        imagesPerPage = parseInt(mode);
    }

    // 重新计算分页
    totalPages = Math.ceil(totalImages / imagesPerPage);
    currentPage = 1;

    // 更新显示
    updateImageDisplay();
    updatePaginationControls();

    // 保存用户偏好
    localStorage.setItem('viewMode', mode);
    localStorage.setItem('imagesPerPage', imagesPerPage);
}

// 应用自定义设置
function applyCustomSettings() {
    const customPerPage = document.getElementById('customPerPage');
    const newPerPage = parseInt(customPerPage.value);

    if (newPerPage >= 1 && newPerPage <= 50) {
        imagesPerPage = newPerPage;
        totalPages = Math.ceil(totalImages / imagesPerPage);
        currentPage = 1;

        updateImageDisplay();
        updatePaginationControls();

        // 保存设置
        localStorage.setItem('imagesPerPage', imagesPerPage);
        localStorage.setItem('viewMode', 'custom');

        showToast('设置已保存');
    } else {
        showToast('请输入1-50之间的数字', 'error');
    }
}

// 更新图片显示
function updateImageDisplay() {
    const gallery = document.getElementById('imageGallery');
    const startIndex = (currentPage - 1) * imagesPerPage;
    const endIndex = Math.min(startIndex + imagesPerPage, totalImages);

    // 清空画廊
    gallery.innerHTML = '';

    // 根据每页图片数设置布局
    if (imagesPerPage === 1) {
        gallery.className = 'image-gallery single-mode';
    } else if (imagesPerPage <= 6) {
        gallery.className = 'image-gallery grid-mode grid-small';
    } else {
        gallery.className = 'image-gallery grid-mode grid-large';
    }

    // 添加当前页的图片
    for (let i = startIndex; i < endIndex; i++) {
        const image = images[i];
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';
        imageItem.setAttribute('data-index', i);

        // 使用最简单的innerHTML方式，避免复杂的事件绑定
        imageItem.innerHTML =
            '<img src=\"' + image.url + '\" ' +
                 'alt=\"' + (image.original_name || '图片' + (i + 1)) + '\" ' +
                 'loading=\"lazy\" ' +
                 'style=\"cursor: pointer; width: 100%; height: auto;\" ' +
                 'data-index=\"' + i + '\">' +
            '<div class=\"image-overlay\">' +
                '<div class=\"image-number\">' + (i + 1) + '</div>' +
                '<div class=\"image-actions\">' +
                    '<button class=\"btn btn-dark btn-sm\" onclick=\"downloadImage(' + JSON.stringify(image.url) + ', ' + (i + 1) + ')\">' +
                        '<i class=\"fas fa-download\"></i>' +
                    '</button>' +
                '</div>' +
            '</div>';

        gallery.appendChild(imageItem);
    }

    // 在所有图片添加完成后，统一绑定点击事件
    console.log('About to bind click events...');

    // 立即绑定事件，不使用setTimeout
    const allImages = gallery.querySelectorAll('img[data-index]');
    console.log('Found images for binding:', allImages.length);

    allImages.forEach(function(img, imgIndex) {
        const index = parseInt(img.getAttribute('data-index'));
        console.log('Binding event to image', imgIndex, 'with data-index', index);

        // 使用addEventListener而不是onclick
        img.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Image clicked via addEventListener, index:', index);
            showLightbox(index);
            return false;
        });

        // 同时也设置onclick作为备用
        img.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Image clicked via onclick, index:', index);
            showLightbox(index);
            return false;
        };

        // 添加视觉反馈和确保可点击
        img.style.cursor = 'pointer';
        img.style.position = 'relative';
        img.style.zIndex = '10';
        img.style.pointerEvents = 'auto';
        img.title = '点击查看大图';

        // 确保父容器不阻挡点击，并且也绑定点击事件
        const imageItem = img.parentElement;
        if (imageItem && imageItem.classList.contains('image-item')) {
            imageItem.style.pointerEvents = 'auto';
            imageItem.style.cursor = 'pointer';

            // 为整个容器也绑定点击事件
            imageItem.onclick = function(e) {
                // 如果点击的是下载按钮，不触发灯箱
                if (e.target.closest('.image-actions') || e.target.closest('button')) {
                    return;
                }
                e.preventDefault();
                e.stopPropagation();
                console.log('Image container clicked, index:', index);
                showLightbox(index);
                return false;
            };
        }
    });

    console.log('Click events bound to', allImages.length, 'images');
}

// 更新分页控制
function updatePaginationControls() {
    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('totalPages').textContent = totalPages;

    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        updateImageDisplay();
        updatePaginationControls();
    }
}

// 下一页
function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        updateImageDisplay();
        updatePaginationControls();
    }
}

// 下载功能
function downloadImage(url, index) {
    try {
        const link = document.createElement('a');
        link.href = url;
        link.download = albumTitle + '_' + index + '.webp';
        link.target = '_blank';
        link.rel = 'noopener noreferrer';

        // 添加到页面并触发点击
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('开始下载图片:', index);
    } catch (error) {
        console.error('下载失败:', error);
        alert('下载失败，请尝试右键保存图片');
    }
}

window.downloadCurrentImage = function() {
    downloadImage(images[currentImageIndex].url, currentImageIndex + 1);
};

function downloadAlbum() {
    if (confirm('确定要下载整个套图吗？\\n\\n将会打包成ZIP文件下载，共 ' + images.length + ' 张图片')) {
        // 显示下载进度
        const progressDiv = document.createElement('div');
        progressDiv.id = 'downloadProgress';
        progressDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; background: rgba(0,0,0,0.9); color: white; padding: 20px; border-radius: 8px; z-index: 10000; font-family: Arial, sans-serif; min-width: 300px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);';
        progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-spinner fa-spin\" style=\"animation: fa-spin 2s infinite linear;\"></i> 正在打包下载...</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">请稍候，正在处理 ' + images.length + ' 张图片</div><style>@keyframes fa-spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>';
        document.body.appendChild(progressDiv);

        // 发送获取图片列表请求
        const formData = new FormData();
        formData.append('album_id', window.location.pathname.split('/').pop());

        fetch('/api/download-album-simple.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-check-circle\" style=\"color: #28a745;\"></i> 准备下载！</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">找到 ' + data.total_images + ' 张图片</div><div style=\"margin-top: 10px;\"><button onclick=\"startBatchDownload()\" data-images=\"' + encodeURIComponent(JSON.stringify(data.images)) + '\" data-title=\"' + data.album_title + '\" style=\"background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">开始下载</button></div>';
            } else {
                throw new Error(data.error || '获取图片列表失败');
            }
        })
        .catch(error => {
            console.error('下载失败:', error);
            progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-exclamation-triangle\" style=\"color: #dc3545;\"></i> 下载失败</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">' + error.message + '</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';
        });
    }
}

function startBatchDownload() {
    const button = event.target;
    const images = JSON.parse(decodeURIComponent(button.dataset.images));
    const title = button.dataset.title;

    const progressDiv = document.getElementById('downloadProgress');
    let downloaded = 0;
    const total = images.length;

    progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-download\" style=\"color: #007bff;\"></i> 正在下载...</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">进度: <span id=\"downloadCount\">0</span>/' + total + '</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';

    // 逐个下载图片
    images.forEach((image, index) => {
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = image.url;
            link.download = image.filename;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            downloaded++;
            document.getElementById('downloadCount').textContent = downloaded;

            if (downloaded === total) {
                setTimeout(() => {
                    progressDiv.innerHTML = '<div style=\"text-align: center;\"><i class=\"fas fa-check-circle\" style=\"color: #28a745;\"></i> 下载完成！</div><div style=\"margin-top: 10px; font-size: 14px; color: #ccc;\">已下载 ' + total + ' 张图片</div><div style=\"margin-top: 10px;\"><button onclick=\"closeDownloadProgress()\" style=\"background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;\">关闭</button></div>';
                }, 1000);
            }
        }, index * 500); // 每500ms下载一张，避免浏览器限制
    });
}

function startDownload(downloadUrl, filename) {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 延迟关闭进度窗口
    setTimeout(() => {
        closeDownloadProgress();
    }, 2000);
}

function closeDownloadProgress() {
    const progressDiv = document.getElementById('downloadProgress');
    if (progressDiv && progressDiv.parentNode) {
        progressDiv.parentNode.removeChild(progressDiv);
    }
}

// 分享功能
function shareAlbum() {
    if (navigator.share) {
        navigator.share({
            title: albumTitle,
            text: albumDescription,
            url: window.location.href
        });
    } else {
        copyLink();
    }
}

function shareToWeibo() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(albumTitle);
    window.open('https://service.weibo.com/share/share.php?url=' + url + '&title=' + title);
}

function shareToWechat() {
    alert('请复制链接后在微信中分享');
    copyLink();
}

function shareToQQ() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(albumTitle);
    window.open('https://connect.qq.com/widget/shareqq/index.html?url=' + url + '&title=' + title);
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        showToast('链接已复制到剪贴板');
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 恢复用户偏好设置
    const savedMode = localStorage.getItem('viewMode') || '1';
    const savedPerPage = localStorage.getItem('imagesPerPage') || '1';

    imagesPerPage = parseInt(savedPerPage);
    totalPages = Math.ceil(totalImages / imagesPerPage);

    // 更新按钮状态
    const buttons = document.querySelectorAll('.btn-group button');
    buttons.forEach((btn, index) => {
        btn.classList.remove('active');
        const modes = ['1', '6', '12', 'custom'];
        if (modes[index] === savedMode) {
            btn.classList.add('active');
        }
    });

    // 如果是自定义模式，显示设置面板
    if (savedMode === 'custom') {
        const customSettings = document.getElementById('customSettings');
        if (customSettings) {
            customSettings.style.display = 'block';
            document.getElementById('customPerPage').value = imagesPerPage;
        }
    }

    // 初始化显示
    updateImageDisplay();
    updatePaginationControls();

    // 调试：检查图片是否正确创建
    setTimeout(function() {
        console.log('=== Post-load debug ===');
        const gallery = document.getElementById('imageGallery');
        const images = gallery ? gallery.querySelectorAll('img') : [];
        console.log('Gallery found:', !!gallery);
        console.log('Images found:', images.length);
        if (images.length > 0) {
            console.log('First image onclick:', images[0].onclick);
            console.log('First image src:', images[0].src);
        }
    }, 1000);

    // 页面卸载时清理事件监听器
    window.addEventListener('beforeunload', function() {
        document.onmousemove = null;
        document.onmouseup = null;
        document.body.classList.remove('dragging');
    });

    // 键盘快捷键（分页专用）
    document.addEventListener('keydown', function(e) {
        const lightbox = document.getElementById('lightbox');
        // 只有在灯箱未显示时才处理分页快捷键
        if (!lightbox || lightbox.style.display !== 'flex') {
            if (e.key === 'ArrowLeft') {
                previousPage();
            } else if (e.key === 'ArrowRight') {
                nextPage();
            }
        }
    });

// 收藏功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-favorite')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-favorite');
        const type = btn.dataset.type;
        const id = btn.dataset.id;

        if (!type || !id) return;

        btn.disabled = true;

        fetch('/api/user.php?action=favorite', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: type,
                id: id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const icon = btn.querySelector('i');
                const countEl = btn.querySelector('.count');

                if (data.action === 'favorited') {
                    btn.classList.add('active');
                    icon.className = 'fas fa-heart me-1';
                    if (countEl) {
                        let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                        countEl.textContent = (count + 1).toLocaleString();
                    }
                } else {
                    btn.classList.remove('active');
                    icon.className = 'far fa-heart me-1';
                    if (countEl) {
                        let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                        countEl.textContent = Math.max(0, count - 1).toLocaleString();
                    }
                }

                alert(data.message);
            } else {
                alert(data.message || '操作失败');
            }
        })
        .catch(error => {
            console.error('收藏操作失败:', error);
            alert('网络错误，请重试');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }
});

// 点赞功能
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-like')) {
        e.preventDefault();
        const btn = e.target.closest('.btn-like');
        const type = btn.dataset.type;
        const id = btn.dataset.id;

        if (!type || !id) return;

        btn.disabled = true;

        fetch('/api/like.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                type: type,
                id: id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const icon = btn.querySelector('i');
                const countEl = btn.querySelector('.count');

                if (data.action === 'liked') {
                    btn.classList.add('active');
                    icon.className = 'fas fa-thumbs-up me-1';
                } else {
                    btn.classList.remove('active');
                    icon.className = 'far fa-thumbs-up me-1';
                }

                if (countEl) {
                    countEl.textContent = data.count.toLocaleString();
                }

                alert(data.message);
            } else {
                alert(data.message || '操作失败');
            }
        })
        .catch(error => {
            console.error('点赞操作失败:', error);
            alert('网络错误，请重试');
        })
        .finally(() => {
            btn.disabled = false;
        });
    }
});

});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
