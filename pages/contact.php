<?php
session_start();
require_once __DIR__ . "/../core/Config.php";

$siteName = Config::getSiteName();
$pageTitle = "联系我们 - {$siteName}";
$pageDescription = "联系{$siteName}客服团队，获取技术支持和服务咨询";
$pageKeywords = "联系我们,客服,技术支持,意见反馈";

// 获取用户信息
$userId = $_SESSION['user_id'] ?? null;
$userInfo = null;
if ($userId) {
    require_once __DIR__ . '/../models/User.php';
    $userModel = new User();
    $userInfo = $userModel->getUserById($userId);

    // 如果用户不存在，清除session
    if (!$userInfo) {
        unset($_SESSION['user_id']);
        $userId = null;
    }
}

// 处理成功/错误消息
$successMessage = '';
$errorMessage = '';
if (isset($_GET['success'])) {
    $successMessage = '反馈提交成功，我们会尽快处理您的问题';
}
if (isset($_GET['error'])) {
    $errorMessage = htmlspecialchars($_GET['error']);
}

ob_start();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header text-center">
                    <h2 class="text-warning mb-0">
                        <i class="fas fa-envelope me-2"></i>联系我们
                    </h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-warning mb-3">
                                <i class="fas fa-headset me-2"></i>客服支持
                            </h5>
                            <div class="mb-3">
                                <strong>在线客服：</strong><br>
                                <span class="text-muted">周一至周日 9:00-21:00</span><br>
                                <a href="#" class="btn btn-sm btn-outline-warning mt-2">
                                    <i class="fab fa-qq me-1"></i>QQ在线客服
                                </a>
                            </div>
                            
                            <div class="mb-3">
                                <strong>邮箱支持：</strong><br>
                                <a href="mailto:<EMAIL>" class="text-warning"><EMAIL></a><br>
                                <span class="text-muted">24小时内回复</span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>技术支持：</strong><br>
                                <a href="mailto:<EMAIL>" class="text-warning"><EMAIL></a><br>
                                <span class="text-muted">技术问题专线</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="text-warning mb-3">
                                <i class="fas fa-paper-plane me-2"></i>意见反馈
                            </h5>

                            <?php if ($successMessage): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $successMessage; ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($errorMessage): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i><?php echo $errorMessage; ?>
                            </div>
                            <?php endif; ?>

                            <form method="post" action="/api/contact.php">
                                <?php if (!$userId): ?>
                                <!-- 未登录用户需要填写昵称和邮箱 -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">昵称 *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱 *</label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                                <?php else: ?>
                                <!-- 已登录用户显示默认信息 -->
                                <div class="mb-3">
                                    <label for="name" class="form-label">昵称</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<?php echo htmlspecialchars($userInfo['nickname'] ?: $userInfo['username'] ?: ''); ?>" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email"
                                           value="<?php echo htmlspecialchars($userInfo['email'] ?: ''); ?>" readonly>
                                </div>
                                <?php endif; ?>

                                <div class="mb-3">
                                    <label for="subject" class="form-label">反馈类型 *</label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">请选择反馈类型</option>
                                        <option value="bug">Bug反馈</option>
                                        <option value="feature">功能建议</option>
                                        <option value="content">内容问题</option>
                                        <option value="account">账户问题</option>
                                        <option value="other">其他问题</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="message" class="form-label">详细描述 *</label>
                                    <textarea class="form-control" id="message" name="message" rows="4" required
                                              placeholder="请详细描述您的问题或建议..."></textarea>
                                </div>

                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-paper-plane me-2"></i>发送反馈
                                </button>

                                <?php if (!$userId): ?>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <a href="/login" class="text-warning">登录</a> 后可以更方便地提交反馈
                                    </small>
                                </div>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-md-12">
                            <h5 class="text-warning mb-3">
                                <i class="fas fa-question-circle me-2"></i>常见问题
                            </h5>
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                            如何成为VIP会员？
                                        </button>
                                    </h2>
                                    <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            您可以通过充值积分或直接购买会员套餐来升级为VIP会员。不同等级的会员享有不同的特权，包括更多的图片浏览权限、视频观看权限和下载权限。
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                            忘记密码怎么办？
                                        </button>
                                    </h2>
                                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            您可以在登录页面点击"忘记密码"链接，输入注册邮箱，系统会发送重置密码的邮件到您的邮箱。请按照邮件中的指引重置密码。
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                            如何下载图片和视频？
                                        </button>
                                    </h2>
                                    <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            下载功能需要三年会员或永久会员权限。升级会员后，您可以在图片和视频页面看到下载按钮，点击即可下载到本地。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <a href="/" class="btn btn-warning">
                            <i class="fas fa-home me-2"></i>返回首页
                        </a>
                        <a href="/about" class="btn btn-outline-warning ms-2">
                            <i class="fas fa-info-circle me-2"></i>关于我们
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
