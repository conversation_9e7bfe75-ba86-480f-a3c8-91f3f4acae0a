<?php
require_once __DIR__ . "/../core/Config.php";
session_start();

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$contentType = $_GET['type'] ?? 'album';
$limit = 20;
$offset = ($page - 1) * $limit;

// 获取下载记录
if ($contentType === 'album') {
    $downloads = $db->fetchAll("
        SELECT a.*, c.name as category_name, d.created_at as downloaded_at, d.download_url
        FROM {$db->getPrefix()}user_downloads d 
        JOIN {$db->getPrefix()}albums a ON d.content_id = a.id 
        LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
        WHERE d.user_id = :user_id AND d.content_type = 'album'
        ORDER BY d.created_at DESC 
        LIMIT $limit OFFSET $offset
    ", ['user_id' => $_SESSION['user_id']]);
} else {
    $downloads = $db->fetchAll("
        SELECT v.*, c.name as category_name, d.created_at as downloaded_at, d.download_url
        FROM {$db->getPrefix()}user_downloads d 
        JOIN {$db->getPrefix()}videos v ON d.content_id = v.id 
        LEFT JOIN {$db->getPrefix()}categories c ON v.category_id = c.id
        WHERE d.user_id = :user_id AND d.content_type = 'video'
        ORDER BY d.created_at DESC 
        LIMIT $limit OFFSET $offset
    ", ['user_id' => $_SESSION['user_id']]);
}

// 获取总数
$total = $db->fetchColumn("
    SELECT COUNT(*) FROM {$db->getPrefix()}user_downloads 
    WHERE user_id = :user_id AND content_type = :content_type
", [
    'user_id' => $_SESSION['user_id'],
    'content_type' => $contentType
]) ?: 0;

$totalPages = ceil($total / $limit);

// 获取统计信息
$albumCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}user_downloads WHERE user_id = :user_id AND content_type = 'album'", ['user_id' => $_SESSION['user_id']]) ?: 0;
$videoCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}user_downloads WHERE user_id = :user_id AND content_type = 'video'", ['user_id' => $_SESSION['user_id']]) ?: 0;

// 设置页面信息
$pageTitle = '下载记录 - ' . Config::getSiteName();
$pageDescription = '查看我的下载历史记录';

// 页面内容
ob_start();
?>

<div class="container mt-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item"><a href="/profile" class="text-warning">个人中心</a></li>
            <li class="breadcrumb-item active">下载记录</li>
        </ol>
    </nav>
    
    <!-- 页面标题和统计 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-warning">
            <i class="fas fa-download me-2"></i>下载记录
        </h2>
        <div class="text-muted">
            共下载 <span class="text-warning"><?php echo number_format($albumCount + $videoCount); ?></span> 个内容
        </div>
    </div>
    
    <!-- 内容类型切换 -->
    <div class="card bg-dark border-secondary mb-4">
        <div class="card-body">
            <div class="btn-group" role="group">
                <a href="?type=album" class="btn <?php echo $contentType === 'album' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                    <i class="fas fa-images me-2"></i>套图下载
                    <span class="badge bg-secondary ms-2"><?php echo $albumCount; ?></span>
                </a>
                <a href="?type=video" class="btn <?php echo $contentType === 'video' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                    <i class="fas fa-video me-2"></i>视频下载
                    <span class="badge bg-secondary ms-2"><?php echo $videoCount; ?></span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- 下载记录 -->
    <div class="card bg-dark border-secondary">
        <div class="card-body">
            <?php if (empty($downloads)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-download fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无下载记录</h5>
                    <p class="text-muted">
                        去发现更多精彩的
                        <?php if ($contentType === 'album'): ?>
                            <a href="/albums" class="text-warning">套图内容</a>
                        <?php else: ?>
                            <a href="/videos" class="text-warning">视频内容</a>
                        <?php endif; ?>
                        吧
                    </p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-dark table-hover">
                        <thead>
                            <tr>
                                <th>内容</th>
                                <th>分类</th>
                                <th>下载时间</th>
                                <th>浏览量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($downloads as $download): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="<?php echo htmlspecialchars($download['cover'] ?? $download['thumbnail'] ?? ''); ?>"
                                                 class="rounded me-3" width="60" height="45" style="object-fit: cover;">
                                            <div>
                                                <h6 class="mb-1">
                                                    <a href="/<?php echo $contentType; ?>/<?php echo htmlspecialchars($download['slug']); ?>" 
                                                       class="text-decoration-none text-light">
                                                        <?php echo htmlspecialchars($download['title']); ?>
                                                    </a>
                                                </h6>
                                                <small class="text-muted">
                                                    <?php if ($contentType === 'album' && !empty($download['image_count'])): ?>
                                                        <i class="fas fa-images me-1"></i><?php echo $download['image_count']; ?> 张图片
                                                    <?php elseif ($contentType === 'video' && !empty($download['duration'])): ?>
                                                        <i class="fas fa-clock me-1"></i><?php echo gmdate('H:i:s', $download['duration']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($download['category_name'])): ?>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($download['category_name']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">未分类</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="text-light"><?php echo date('Y-m-d H:i', strtotime($download['downloaded_at'])); ?></span>
                                    </td>
                                    <td>
                                        <span class="text-muted">
                                            <i class="fas fa-eye me-1"></i><?php echo number_format($download['view_count']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="/<?php echo $contentType; ?>/<?php echo htmlspecialchars($download['slug']); ?>" 
                                               class="btn btn-outline-primary" title="查看">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (!empty($download['download_url'])): ?>
                                                <a href="<?php echo htmlspecialchars($download['download_url']); ?>" 
                                                   class="btn btn-outline-success" title="重新下载" target="_blank">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <nav class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $page - 1; ?>">上一页</a>
                </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                </li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $contentType; ?>&page=<?php echo $page + 1; ?>">下一页</a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
