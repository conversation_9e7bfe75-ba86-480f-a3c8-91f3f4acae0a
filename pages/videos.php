<?php require_once __DIR__ . "/../core/Config.php"; ?>
<?php
session_start();

require_once __DIR__ . '/../models/Video.php';
require_once __DIR__ . '/../includes/functions.php';

// 获取参数
$page = max(1, intval($_GET['page'] ?? 1));
$category = trim($_GET['category'] ?? '');
$keyword = trim($_GET['q'] ?? '');
$order = $_GET['order'] ?? 'latest';

// 初始化视频模型
$videoModel = new Video();

// 构建查询参数
$params = [
    'page' => $page,
    'page_size' => 20,
    'order' => $order
];

if ($keyword) {
    $params['keyword'] = $keyword;
}

if ($category) {
    // 根据分类slug获取分类ID
    $db = Database::getInstance();
    $categoryInfo = $db->fetch(
        "SELECT * FROM {$db->getPrefix()}categories WHERE slug = :slug AND status = 1",
        ['slug' => $category]
    );
    if ($categoryInfo) {
        $params['category_id'] = $categoryInfo['id'];
    }
}

// 获取视频列表
try {
    $result = $videoModel->getList($params);
    $videos = $result['list'];
    $total = $result['total'];
    $totalPages = $result['total_pages'];
} catch (Exception $e) {
    $videos = [];
    $total = 0;
    $totalPages = 0;
    error_log("视频列表加载失败: " . $e->getMessage());
}

// 处理视频数据
foreach ($videos as $index => $video) {
    $videos[$index]['is_free'] = $video['is_free'];
    $videos[$index]['cover'] = $video['cover'] ?: '/assets/images/placeholder.svg';
    $videos[$index]['slug'] = $video['slug'] ?: 'video-' . $video['id'];
    $videos[$index]['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
}
unset($video); // 清除引用

// 设置页面信息
$pageTitle = '视频列表';
if ($category && isset($categoryInfo)) {
    $pageTitle = $categoryInfo['name'] . ' - 视频列表';
}
if ($keyword) {
    $pageTitle = '搜索: ' . htmlspecialchars($keyword) . ' - 视频列表';
}
$pageTitle .= ' - ' . getConfig('site_name', Config::getSiteName());

$pageDescription = '精彩视频内容，高清画质，精选推荐';
$pageKeywords = '视频,高清,在线观看,精选';

// 页面内容
ob_start();
?>

<div class="container">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/" class="text-warning">首页</a></li>
            <li class="breadcrumb-item"><a href="/videos" class="text-warning">视频</a></li>
            <?php if ($category && isset($categoryInfo)): ?>
            <li class="breadcrumb-item active"><?php echo htmlspecialchars($categoryInfo['name'] ?? ''); ?></li>
            <?php endif; ?>
        </ol>
    </nav>

    <!-- 页面标题和筛选 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-warning mb-3">
                <i class="fas fa-video me-2"></i>
                <?php if ($keyword): ?>
                    搜索结果: <?php echo htmlspecialchars($keyword); ?>
                <?php elseif ($category && isset($categoryInfo)): ?>
                    <?php echo htmlspecialchars($categoryInfo['name'] ?? ''); ?>
                <?php else: ?>
                    全部视频
                <?php endif; ?>
            </h2>
            <p class="text-muted">共找到 <?php echo number_format($total); ?> 个视频</p>
        </div>
        <div class="col-md-4">
            <!-- 排序选择 -->
            <div class="d-flex justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-outline-warning dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-sort me-1"></i>排序方式
                    </button>
                    <ul class="dropdown-menu bg-dark border-secondary">
                        <li><a class="dropdown-item text-light <?php echo $order === 'latest' ? 'active' : ''; ?>" 
                               href="?order=latest<?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>">最新发布</a></li>
                        <li><a class="dropdown-item text-light <?php echo $order === 'view' ? 'active' : ''; ?>" 
                               href="?order=view<?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>">最多播放</a></li>
                        <li><a class="dropdown-item text-light <?php echo $order === 'favorite' ? 'active' : ''; ?>" 
                               href="?order=favorite<?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>">最多收藏</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 视频网格 -->
    <?php if (!empty($videos)): ?>
    <div class="album-grid">
        <?php foreach ($videos as $video): ?>
        <div class="album-card video-card">
            <div class="card-img-container">
                <img src="<?php echo htmlspecialchars($video['cover'] ?? ''); ?>"
                     alt="<?php echo htmlspecialchars($video['title'] ?? ''); ?>" class="img-fluid"
                     onerror="this.src='/assets/images/placeholder.svg'">
                <div class="card-overlay">
                    <div class="card-stats">
                        <div class="d-flex justify-content-between">
                            <span><i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?></span>
                            <span><i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?></span>
                        </div>
                    </div>
                    <div class="play-button">
                        <i class="fas fa-play-circle"></i>
                    </div>
                </div>
                <?php if (!$video['is_free']): ?>
                <div class="position-absolute top-0 end-0 m-2">
                    <span class="badge bg-warning text-dark">VIP</span>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <a href="/video/<?php echo $video['id']; ?>" class="card-title text-decoration-none">
                    <?php echo htmlspecialchars($video['title'] ?? ''); ?>
                </a>
                <p class="card-text">
                    <?php echo htmlspecialchars(mb_substr($video['description'] ?? '', 0, 50) . '...'); ?>
                </p>
                <div class="card-meta">
                    <span class="text-muted">
                        <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($video['category_name'] ?? '未分类'); ?>
                    </span>
                    <span class="text-muted">
                        <i class="fas fa-heart me-1"></i><?php echo number_format($video['favorite_count']); ?>
                    </span>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <nav class="mt-5">
        <ul class="pagination justify-content-center">
            <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>&order=<?php echo $order; ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?><?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>&order=<?php echo $order; ?>">
                    <?php echo $i; ?>
                </a>
            </li>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $category ? '&category=' . urlencode($category) : ''; ?><?php echo $keyword ? '&q=' . urlencode($keyword) : ''; ?>&order=<?php echo $order; ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </nav>
    <?php endif; ?>
    
    <?php else: ?>
    <!-- 空状态 -->
    <div class="text-center py-5">
        <i class="fas fa-video fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">暂无视频内容</h4>
        <p class="text-muted">请稍后再来查看，或者尝试其他分类</p>
        <a href="/videos" class="btn btn-warning">
            <i class="fas fa-refresh me-1"></i>刷新页面
        </a>
    </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    // 视频卡片点击事件
    document.querySelectorAll('.video-card').forEach(card => {
        card.addEventListener('click', function(e) {
            if (!e.target.closest('a')) {
                const link = this.querySelector('.card-title');
                if (link) {
                    window.location.href = link.href;
                }
            }
        });
    });
});
";

// 包含布局模板
include __DIR__ . '/../templates/layout.php';
?>
