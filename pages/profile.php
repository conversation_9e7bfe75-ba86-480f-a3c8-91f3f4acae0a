<?php
require_once __DIR__ . "/../core/Config.php";
session_start();

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $data = [
            'nickname' => trim($_POST['nickname']),
            'email' => trim($_POST['email'])
        ];
        
        if (!empty($data['nickname']) && !empty($data['email'])) {
            $result = $db->update('users', $data, 'id = :id', ['id' => $_SESSION['user_id']]);
            if ($result) {
                $message = '个人资料更新成功';
                $messageType = 'success';
                $_SESSION['nickname'] = $data['nickname'];
            } else {
                $message = '更新失败';
                $messageType = 'danger';
            }
        }
    }
}

// 获取当前用户信息
$currentUser = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $_SESSION['user_id']]);

// 获取用户组信息
$userGroup = null;
if (!empty($currentUser['group_id'])) {
    $userGroup = $db->fetch("SELECT * FROM {$db->getPrefix()}user_groups WHERE id = :id", [
        'id' => $currentUser['group_id']
    ]);
}

// 获取统计信息
$userStats = [
    'favorite_count' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}favorites WHERE user_id = :user_id", ['user_id' => $_SESSION['user_id']]) ?: 0,
    'download_count' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}user_downloads WHERE user_id = :user_id", ['user_id' => $_SESSION['user_id']]) ?: 0,
    'view_count' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}user_view_history WHERE user_id = :user_id", ['user_id' => $_SESSION['user_id']]) ?: 0
];

// 获取收藏列表
$favorites = $db->fetchAll("
    SELECT a.*, c.name as category_name, f.created_at as favorited_at
    FROM {$db->getPrefix()}favorites f 
    JOIN {$db->getPrefix()}albums a ON f.content_id = a.id 
    LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
    WHERE f.user_id = :user_id AND f.content_type = 'album'
    ORDER BY f.created_at DESC LIMIT 12
", ['user_id' => $_SESSION['user_id']]);

// 设置页面信息
$pageTitle = '个人中心 - ' . Config::getSiteName();
$pageDescription = '查看个人资料、收藏、下载记录和账户设置';

// 页面内容
ob_start();
?>

<div class="container mt-4">
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3">
            <div class="card bg-dark border-secondary">
                <div class="card-body text-center">
                    <div class="bg-warning text-dark rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <?php echo strtoupper(substr($currentUser['username'], 0, 1)); ?>
                    </div>
                    <h5 class="text-warning"><?php echo htmlspecialchars($currentUser['username']); ?></h5>
                    <p class="text-muted mb-0"><?php echo htmlspecialchars($userGroup['group_name'] ?? '普通会员'); ?></p>
                </div>
            </div>
            
            <div class="card bg-dark border-secondary mt-3">
                <div class="list-group list-group-flush">
                    <a href="#profile" class="list-group-item list-group-item-action bg-dark text-light active" data-tab="profile">
                        <i class="fas fa-user me-2"></i>个人资料
                    </a>
                    <a href="/favorites" class="list-group-item list-group-item-action bg-dark text-light">
                        <i class="fas fa-heart me-2"></i>我的收藏
                        <span class="badge bg-secondary ms-auto"><?php echo $userStats['favorite_count']; ?></span>
                    </a>
                    <a href="/downloads" class="list-group-item list-group-item-action bg-dark text-light">
                        <i class="fas fa-download me-2"></i>下载记录
                        <span class="badge bg-secondary ms-auto"><?php echo $userStats['download_count']; ?></span>
                    </a>
                    <a href="#settings" class="list-group-item list-group-item-action bg-dark text-light" data-tab="settings">
                        <i class="fas fa-cog me-2"></i>账户设置
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 主内容区 -->
        <div class="col-md-9">
            <!-- 个人资料 -->
            <div id="tab-profile" class="tab-content active">
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>个人资料</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-control bg-dark border-secondary text-light" 
                                           value="<?php echo htmlspecialchars($currentUser['username']); ?>" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" class="form-control bg-dark border-secondary text-light" 
                                           value="<?php echo htmlspecialchars($currentUser['email']); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户组</label>
                                    <input type="text" class="form-control bg-dark border-secondary text-light" 
                                           value="<?php echo htmlspecialchars($userGroup['group_name'] ?? '普通会员'); ?>" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">积分</label>
                                    <input type="text" class="form-control bg-dark border-secondary text-light" 
                                           value="<?php echo number_format($currentUser['points']); ?>" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <h4 class="text-warning"><?php echo $userStats['favorite_count']; ?></h4>
                                <small class="text-muted">收藏数</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h4 class="text-info"><?php echo $userStats['download_count']; ?></h4>
                                <small class="text-muted">下载数</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <h4 class="text-success"><?php echo $userStats['view_count']; ?></h4>
                                <small class="text-muted">浏览数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

            
            <!-- 账户设置 -->
            <div id="tab-settings" class="tab-content">
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>账户设置</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_profile">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">昵称</label>
                                        <input type="text" class="form-control bg-dark border-secondary text-light" 
                                               name="nickname" value="<?php echo htmlspecialchars($currentUser['nickname'] ?? $currentUser['username']); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">邮箱</label>
                                        <input type="email" class="form-control bg-dark border-secondary text-light" 
                                               name="email" value="<?php echo htmlspecialchars($currentUser['email']); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>保存修改
                                </button>
                                <a href="/recharge" class="btn btn-success">
                                    <i class="fas fa-credit-card me-2"></i>充值中心
                                </a>
                                <a href="/vip" class="btn btn-info">
                                    <i class="fas fa-crown me-2"></i>VIP升级
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php
$content = ob_get_clean();
include __DIR__ . '/../templates/layout.php';
?>
