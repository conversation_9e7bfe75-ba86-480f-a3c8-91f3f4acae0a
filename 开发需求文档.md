原始需求：
我需要做一个合法正规的摄影美女套图+视频网站，这个网站的基础功能你需要帮我规划设计，要包含美女套图+美女视频网站基础的前端后端功能，用PHP+MYSQL设计。

前端页面要自适应模板，可以适应电脑和PAD还有手机的分辨率。
还要有一个火车头发布接口，可以通过火车头采集器批量发布内容，通过火车头的FTP上传功能上传图片和视频。

除了常规美女套图+视频网站的基础功能之外，还额外需要以下功能：

1、管理员后台可以设置每一套美女套图和每一个视频是收费还是免费，可以一键设置所有的套图和视频收费还是免费。
   管理员后台可以设置非会员允许免费浏览几张图片。
2、普通注册会员默认只可以看10张图片（注意是10张图片，不是10套图片），可以通过拉注册来获取积分，攒够一定积分可以兑换会员。
3、付费会员需要用积分购买，付费会员类型分以下几种：
	①、天费会员，权限是可以看全站图片一天。
	②、周费会员，可以免费看全站图片一周。
	③、月费会员、可以免费看全站图片一月。
	④、年费会员、可以免费看全站图片一年。
	⑤、三年会员、可免费看全站图片+视频三年+套图批量打包下载功能。
	⑥、永久会员，可以免费永久看全站图片+视频+套图、视频批量打包下载功能。
4、批量生成充值卡，生成的充值卡可以充值积分。要有充值卡使用记录，积分获取与消耗记录。
5、支付使用第三方发卡系统，只需要将对应的各等级付费会员设置几个购买链接录入框即可。
6、凡注册会员都需具备收藏功能，收藏某一套套图，某一个视频，会员管理页面要有这些收藏记录。
7、所有上传的附件使用专门的附件服务器，使用远程FTP连接上传。
8、付费会员专属功能：可设置前端图片每页显示数量，比如默认是1页显示1张，付费会员可以设置1页显示3张或者10张或者全部显示。
9、非会员浏览图片时，显示免费可浏览的3张图片后，显示一个锁定提醒：全本共x张图片，如需欣赏完整作品：请注册会员欣赏更多美图（这几个字是醒目按钮）
   非会员浏览视频，不允许观看，弹出会员登录框。普通注册会员和没有视频观看权限的付费会员浏览视频，弹出提示语：抱歉，你不是超级VIP会员，暂无权限观看。请升级三年会员或永久会员。点此升级（醒目按钮，连接到后台使用充值卡的卡号卡密充值的页面）
10、会员管理页面的充值卡充值页面要有三个模块：
    输入充值卡卡号卡密进行充值
    点击下面几个不同级别会员的购买连接购买充值卡
    会员权益说明部分
11、前端导航栏要一个排行榜链接：点击排行、收藏排行、点赞排行、下载排行
12、后台管理可以设置附件资源使用哪个url前缀，用以使用不同加速线路。
13、后台管理要有图片与视频的采集功能，可以设置目标站的采集规则，全自动采集目标站的图片和视频。
14、前端导航栏要一个最近更新链接，显示最近更新的1000条套图和视频记录。
   
   
   请根据以上需求帮我写一个开发需求，我要让VSCODE的AI来开发这各程序，请写出合理的开发需求，让AI理解这份需求，开发出我需要的程序。





下面是AI写的开发文档
# 美女套图+视频网站开发需求文档

## 项目概述
开发一个合法正规的美女套图+视频网站，采用PHP+MySQL技术栈，支持多终端自适应访问，包含完整的会员系统、付费体系、内容管理等功能。

## 技术架构
- **后端语言**: PHP 8.3
- **数据库**: MySQL 5.7
- **缓存系统**: Redis 7.4
- **前端技术**: HTML5 + CSS3 + JavaScript + Bootstrap 5 (使用国内CDN)
- **服务器**: Debian 12 + 宝塔Linux面板 + Nginx
- **域名**: https://www.liapian.com
- **附件存储**: 远程FTP服务器

MYSQL账号www_liapian_com ，数据库名www_liapian_com ，数据库密码LF4NFSiMy36eWim3 
redis的密码是 000)))00009000


## 核心功能模块

### 1. 用户系统模块
#### 1.1 用户类型
- **游客**: 未注册用户，可浏览3张免费图片
- **注册会员**: 默认可浏览10张图片，通过邀请获得积分
- **付费会员**: 分为6个等级
  - 天费会员：1天全站图片浏览权限
  - 周费会员：7天全站图片浏览权限
  - 月费会员：30天全站图片浏览权限
  - 年费会员：365天全站图片浏览权限
  - 三年会员：3年全站图片+视频+批量下载权限
  - 永久会员：永久全站图片+视频+批量下载权限

#### 1.2 用户功能
- 用户注册/登录/找回密码
- 邀请注册获取积分奖励
- 收藏套图和视频
- 个人信息管理
- 会员等级显示
- 权限剩余时间显示

### 2. 积分与充值系统
#### 2.1 积分获取方式
- 注册奖励积分
- 邀请新用户注册获得积分
- 充值卡充值获得积分

#### 2.2 充值卡系统
- 批量生成充值卡
- 卡号卡密验证
- 使用记录追踪
- 积分获取/消耗记录

#### 2.3 第三方支付
- 集成发卡系统购买链接
- 各等级会员购买入口
- 支付成功自动开通会员

### 3. 内容管理系统
#### 3.1 套图管理
- 套图分类管理
- 套图信息录入（标题、描述、标签等）
- 图片批量上传（支持FTP远程上传）
- 套图收费/免费设置
- 一键批量设置收费状态
- 套图状态管理（发布/下架）

#### 3.2 视频管理
- 视频分类管理
- 视频信息录入
- 视频文件上传（FTP远程上传）
- 视频收费/免费设置
- 视频状态管理

#### 3.3 内容采集功能
- 目标站点配置
- 采集规则设置
- 自动采集套图和视频
- 采集内容审核
- 图片格式自动转换WebP功能（可选开启/关闭）

### 4. 火车头发布接口
#### 4.1 接口功能
- 支持火车头采集器批量发布
- 图片/视频FTP批量上传
- 内容信息自动入库
- 发布状态返回

#### 4.2 接口规范
- POST请求接口
- JSON数据格式
- 身份验证机制
- 错误代码定义

### 5. 前端展示系统
#### 5.1 响应式设计
- 自适应PC、平板、手机分辨率
- Bootstrap 5框架
- 黑色主题设计风格
- 流畅的用户体验

#### 5.2 页面功能
- 首页：最新套图/视频展示
- 分类页：按分类浏览内容
- 详情页：套图/视频详细展示
- 排行榜：点击/收藏/点赞/下载排行
- 最近更新：最新1000条记录
- 搜索功能：关键词搜索

#### 5.3 会员专属功能
- 图片每页显示数量设置
- 批量打包下载功能
- 无广告浏览体验

### 6. 权限控制系统
#### 6.1 浏览权限
- 游客：3张免费图片预览
- 注册会员：10张图片浏览限制
- 付费会员：根据等级享受对应权限

#### 6.2 下载权限
- 三年会员及以上：支持批量打包下载
- 其他用户：无下载权限

#### 6.3 视频观看权限
- 三年会员及以上：可观看所有视频
- 其他用户：显示升级提示

### 7. 管理后台系统
#### 7.1 内容管理
- 套图/视频的增删改查
- 批量操作功能
- 内容审核管理
- 分类标签管理

#### 7.2 用户管理
- 用户信息查看/编辑
- 用户组权限详细配置
- 会员等级管理
- 积分手动调整
- 封禁/解封功能

#### 7.3 系统设置
- 网站基本信息配置
- 会员价格设置
- 积分规则配置
- 附件服务器设置
- CDN加速线路配置

#### 7.4 数据统计
- 用户注册统计
- 内容浏览统计
- 收入统计报表
- 系统运行状态

### 8. 附件存储系统
#### 8.1 远程FTP存储
- FTP服务器连接配置
- 图片/视频自动上传
- 多线路CDN支持
- 存储空间管理

#### 8.2 资源管理
- 附件URL前缀配置
- 多CDN线路切换
- 资源访问统计
- 存储容量监控

后台采集功能，需要可以设置图片在本地化保存的时候，保存并上传到FTP远程服务器，同时在本地化保存的时候可以设置是否把原图片格式转换为webp格式。

## 数据库设计

### 主要数据表
1. **users** - 用户表
2. **user_groups** - 用户组表
3. **user_permissions** - 用户组权限表
4. **albums** - 套图表
5. **album_images** - 套图图片表
6. **videos** - 视频表
7. **categories** - 分类表
8. **recharge_cards** - 充值卡表
9. **points_log** - 积分记录表
10. **favorites** - 收藏表
11. **view_logs** - 浏览记录表
12. **admin_users** - 管理员表
13. **system_config** - 系统配置表

## 安全考虑
1. SQL注入防护
2. XSS攻击防护
3. CSRF攻击防护
4. 文件上传安全检查
5. 用户权限严格验证
6. 敏感数据加密存储

## 性能优化
1. 数据库索引优化
2. 图片懒加载
3. CDN加速
4. 缓存机制
5. 数据库连接池
6. 静态资源压缩

## 部署要求
1. Debian 12服务器 + 宝塔Linux面板
2. PHP 8.3 环境
3. MySQL 5.7 数据库
4. Redis 7.4 缓存服务
5. Nginx Web服务器
6. 远程FTP存储服务器
7. SSL证书配置（已配置 https://www.liapian.com）
8. 使用国内CDN资源（避免被屏蔽的第三方资源）

## 开发进度计划
### 第一阶段（数据库设计 + 基础框架）
- 数据库表结构设计
- PHP基础框架搭建
- 用户系统开发

### 第二阶段（核心功能开发）
- 内容管理系统
- 权限控制系统
- 积分充值系统

### 第三阶段（前端页面开发）
- 响应式页面制作
- 用户交互功能
- 会员中心页面

### 第四阶段（接口与采集）
- 火车头发布接口
- 内容采集功能
- 第三方支付集成

### 第五阶段（测试与优化）
- 功能测试
- 性能优化
- 安全加固

## 验收标准
1. 所有功能模块正常运行
2. 多终端适配良好
3. 安全性能达标
4. 用户体验流畅
5. 后台管理便捷
6. 系统稳定可靠

---

**注意事项**：
1. 网站内容必须符合相关法律法规
2. 用户隐私保护措施到位
3. 支付安全机制完善
4. 定期备份重要数据
5. 保持系统安全更新

请仔细阅读以上需求文档，如有需要修改的地方请告知，确认后我们将开始详细的开发工作。
