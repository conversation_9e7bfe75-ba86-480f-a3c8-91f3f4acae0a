<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '仪表盘';
$currentPage = 'dashboard';

// 获取统计数据
$stats = [
    'users' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users") ?: 0,
    'albums' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums") ?: 0,
    'videos' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}videos") ?: 0,
    'categories' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}categories") ?: 0
];

// 获取最近活动
$recentUsers = $db->fetchAll("SELECT username, created_at FROM {$db->getPrefix()}users ORDER BY created_at DESC LIMIT 5") ?: [];
$recentAlbums = $db->fetchAll("SELECT title, created_at FROM {$db->getPrefix()}albums ORDER BY created_at DESC LIMIT 5") ?: [];

// 获取最新反馈
$recentFeedbacks = $db->fetchAll("
    SELECT f.*, u.username, u.nickname
    FROM {$db->getPrefix()}feedback f
    LEFT JOIN {$db->getPrefix()}users u ON f.user_id = u.id
    ORDER BY f.created_at DESC
    LIMIT 10
") ?: [];

// 获取反馈统计
$feedbackStats = [
    'total' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}feedback") ?: 0,
    'pending' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}feedback WHERE status = 'pending'") ?: 0,
    'processing' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}feedback WHERE status = 'processing'") ?: 0,
    'high_priority' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}feedback WHERE priority = 'high' AND status IN ('pending', 'processing')") ?: 0
];

include __DIR__ . '/templates/header.php';
?>
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['users']); ?></h4>
                        <p class="mb-0">注册用户</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['albums']); ?></h4>
                        <p class="mb-0">套图数量</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-images fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['videos']); ?></h4>
                        <p class="mb-0">视频数量</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['categories']); ?></h4>
                        <p class="mb-0">分类数量</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 快速操作 -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="/admin/albums.php?action=add" class="btn btn-primary btn-lg w-100 mb-3">
                            <i class="fas fa-plus me-2"></i>添加套图
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/videos.php?action=add" class="btn btn-success btn-lg w-100 mb-3">
                            <i class="fas fa-plus me-2"></i>添加视频
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/categories.php" class="btn btn-info btn-lg w-100 mb-3">
                            <i class="fas fa-tags me-2"></i>管理分类
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/settings.php" class="btn btn-warning btn-lg w-100 mb-3">
                            <i class="fas fa-cog me-2"></i>系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-users me-2"></i>最近注册用户</h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentUsers)): ?>
                    <p class="text-muted">暂无数据</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentUsers as $user): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><?php echo htmlspecialchars($user['username']); ?></span>
                                <small class="text-muted"><?php echo date('m-d H:i', strtotime($user['created_at'])); ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-images me-2"></i>最近添加套图</h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentAlbums)): ?>
                    <p class="text-muted">暂无数据</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentAlbums as $album): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><?php echo htmlspecialchars($album['title']); ?></span>
                                <small class="text-muted"><?php echo date('m-d H:i', strtotime($album['created_at'])); ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 最新反馈 -->
<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-comments me-2"></i>最新反馈</h6>
                <a href="/admin/feedback.php" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
                <?php if (empty($recentFeedbacks)): ?>
                    <p class="text-muted text-center py-3">暂无反馈</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentFeedbacks as $feedback): ?>
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-1">
                                        <?php if ($feedback['user_id']): ?>
                                            <span class="badge bg-info me-2">客服工单</span>
                                            <strong><?php echo htmlspecialchars($feedback['nickname'] ?: $feedback['username']); ?></strong>
                                        <?php else: ?>
                                            <span class="badge bg-secondary me-2">意见反馈</span>
                                            <strong><?php echo htmlspecialchars($feedback['name']); ?></strong>
                                        <?php endif; ?>

                                        <?php
                                        $priorityClass = [
                                            'low' => 'success',
                                            'medium' => 'warning',
                                            'high' => 'danger'
                                        ];
                                        $priorityText = [
                                            'low' => '低',
                                            'medium' => '中',
                                            'high' => '高'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $priorityClass[$feedback['priority']]; ?> ms-2">
                                            <?php echo $priorityText[$feedback['priority']]; ?>
                                        </span>

                                        <?php
                                        $statusClass = [
                                            'pending' => 'warning',
                                            'processing' => 'info',
                                            'resolved' => 'success',
                                            'closed' => 'secondary'
                                        ];
                                        $statusText = [
                                            'pending' => '待处理',
                                            'processing' => '处理中',
                                            'resolved' => '已解决',
                                            'closed' => '已关闭'
                                        ];
                                        ?>
                                        <span class="badge bg-<?php echo $statusClass[$feedback['status']]; ?> ms-1">
                                            <?php echo $statusText[$feedback['status']]; ?>
                                        </span>
                                    </div>
                                    <p class="mb-1 text-muted small">
                                        <?php echo htmlspecialchars(mb_substr($feedback['message'], 0, 80) . '...'); ?>
                                    </p>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted d-block">
                                        <?php echo date('m-d H:i', strtotime($feedback['created_at'])); ?>
                                    </small>
                                    <div class="btn-group btn-group-sm mt-1">
                                        <a href="/admin/feedback-detail.php?id=<?php echo $feedback['id']; ?>"
                                           class="btn btn-outline-primary btn-sm" target="_blank" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/admin/feedback-edit.php?id=<?php echo $feedback['id']; ?>"
                                           class="btn btn-outline-success btn-sm" title="处理">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>反馈统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-0"><?php echo $feedbackStats['total']; ?></h4>
                            <small class="text-muted">总反馈</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning mb-0"><?php echo $feedbackStats['pending']; ?></h4>
                        <small class="text-muted">待处理</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-0"><?php echo $feedbackStats['processing']; ?></h4>
                            <small class="text-muted">处理中</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger mb-0"><?php echo $feedbackStats['high_priority']; ?></h4>
                        <small class="text-muted">高优先级</small>
                    </div>
                </div>

                <?php if ($feedbackStats['pending'] > 0 || $feedbackStats['high_priority'] > 0): ?>
                <hr>
                <div class="alert alert-warning alert-sm mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php if ($feedbackStats['high_priority'] > 0): ?>
                        有 <strong><?php echo $feedbackStats['high_priority']; ?></strong> 个高优先级工单需要处理
                    <?php elseif ($feedbackStats['pending'] > 0): ?>
                        有 <strong><?php echo $feedbackStats['pending']; ?></strong> 个工单待处理
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/templates/footer.php'; ?>
