<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '系统设置';
$currentPage = 'settings';

// 获取配置值的辅助函数
function getConfigValue($configGroups, $key, $default = '') {
    foreach ($configGroups as $group) {
        foreach ($group as $config) {
            if ($config['key'] === $key) {
                return $config['value'];
            }
        }
    }
    return $default;
}

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_settings') {
        $settings = $_POST['settings'] ?? [];
        
        foreach ($settings as $key => $value) {
            // 检查配置是否存在
            $exists = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}system_config WHERE `key` = :key", ['key' => $key]);

            if ($exists) {
                // 更新配置
                $db->update('system_config', ['value' => $value], '`key` = :key', ['key' => $key]);
            } else {
                // 插入新配置
                $db->insert('system_config', [
                    'key' => $key,
                    'value' => $value,
                    'group' => 'basic',
                    'title' => $key,
                    'description' => ''
                ]);
            }
        }
        
        $message = '设置保存成功';
        $messageType = 'success';
    }
}

// 获取所有配置
$configs = $db->fetchAll("SELECT * FROM {$db->getPrefix()}system_config ORDER BY `group` ASC, `key` ASC");
$configGroups = [];

foreach ($configs as $config) {
    $configGroups[$config['group']][] = $config;
}

// 默认配置组
$defaultGroups = [
    'basic' => '基础设置',
    'user' => '用户设置',
    'points' => '积分设置',
    'upload' => '上传设置',
    'attachment' => '附件设置',
    'ftp' => 'FTP设置',
    'email' => '邮件设置'
];

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<form method="POST">
    <input type="hidden" name="action" value="save_settings">
    
    <div class="row">
        <div class="col-md-3">
            <!-- 设置导航 -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">设置分组</h6>
                </div>
                <div class="list-group list-group-flush">
                    <?php foreach ($defaultGroups as $groupKey => $groupName): ?>
                        <a href="#group-<?php echo $groupKey; ?>" class="list-group-item list-group-item-action" data-bs-toggle="pill">
                            <i class="fas fa-cog me-2"></i><?php echo $groupName; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="tab-content">
                <!-- 基础设置 -->
                <div class="tab-pane fade show active" id="group-basic">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-globe me-2"></i>基础设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">网站名称</label>
                                        <input type="text" class="form-control" name="settings[site_name]" 
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'site_name', '丽片网')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">网站URL</label>
                                        <input type="url" class="form-control" name="settings[site_url]" 
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'site_url', 'https://www.liapian.com')); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">网站描述</label>
                                <textarea class="form-control" name="settings[site_description]" rows="3"><?php echo htmlspecialchars(getConfigValue($configGroups, 'site_description', '专业的美女套图视频网站')); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">网站关键词</label>
                                <input type="text" class="form-control" name="settings[site_keywords]" 
                                       value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'site_keywords', '美女,套图,视频,写真')); ?>"
                                       placeholder="用逗号分隔">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 用户设置 -->
                <div class="tab-pane fade" id="group-user">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-users me-2"></i>用户设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">游客免费图片数</label>
                                        <input type="number" class="form-control" name="settings[guest_free_images]" 
                                               value="<?php echo getConfigValue($configGroups, 'guest_free_images', '3'); ?>" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">注册会员免费图片数</label>
                                        <input type="number" class="form-control" name="settings[member_free_images]" 
                                               value="<?php echo getConfigValue($configGroups, 'member_free_images', '10'); ?>" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="settings[allow_register]" value="1" 
                                                   <?php echo getConfigValue($configGroups, 'allow_register', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">允许用户注册</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="settings[email_verify]" value="1" 
                                                   <?php echo getConfigValue($configGroups, 'email_verify', '0') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">邮箱验证</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 积分设置 -->
                <div class="tab-pane fade" id="group-points">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-coins me-2"></i>积分设置</h5>
                        </div>
                        <div class="card-body">
                            <!-- 基础积分设置 -->
                            <h6 class="text-primary mb-3"><i class="fas fa-gift me-2"></i>基础奖励</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">注册奖励积分</label>
                                        <input type="number" class="form-control" name="settings[register_points]"
                                               value="<?php echo getConfigValue($configGroups, 'register_points', '100'); ?>" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">每日签到积分</label>
                                        <input type="number" class="form-control" name="settings[daily_points]"
                                               value="<?php echo getConfigValue($configGroups, 'daily_points', '10'); ?>" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">积分有效期（天）</label>
                                        <input type="number" class="form-control" name="settings[points_expire_days]"
                                               value="<?php echo getConfigValue($configGroups, 'points_expire_days', '365'); ?>" min="0">
                                        <small class="text-muted">0表示永不过期</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">积分兑换比例</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="settings[points_to_money_rate]"
                                                   value="<?php echo getConfigValue($configGroups, 'points_to_money_rate', '100'); ?>" min="1">
                                            <span class="input-group-text">积分 = 1元</span>
                                        </div>
                                        <small class="text-muted">用于提现时的积分兑换比例</small>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <!-- 邀请奖励设置 -->
                            <h6 class="text-success mb-3"><i class="fas fa-users me-2"></i>邀请奖励</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">邀请注册奖励积分</label>
                                        <input type="number" class="form-control" name="settings[invite_register_reward]"
                                               value="<?php echo getConfigValue($configGroups, 'invite_register_reward', '100'); ?>" min="0">
                                        <small class="text-muted">邀请人获得的注册奖励积分</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">充值分成比例（%）</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="settings[invite_recharge_rate]"
                                                   value="<?php echo getConfigValue($configGroups, 'invite_recharge_rate', '30'); ?>" min="0" max="100">
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <small class="text-muted">邀请人获得的充值分成百分比</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="settings[invite_level_enabled]"
                                                   value="1" <?php echo getConfigValue($configGroups, 'invite_level_enabled', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">启用多级邀请</label>
                                        </div>
                                        <small class="text-muted">是否启用二级邀请奖励</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">二级邀请分成比例（%）</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="settings[invite_level2_rate]"
                                                   value="<?php echo getConfigValue($configGroups, 'invite_level2_rate', '10'); ?>" min="0" max="100">
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <small class="text-muted">二级邀请人获得的分成百分比</small>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <!-- 提现设置 -->
                            <h6 class="text-warning mb-3"><i class="fas fa-money-bill-wave me-2"></i>提现设置</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="settings[withdraw_enabled]"
                                                   value="1" <?php echo getConfigValue($configGroups, 'withdraw_enabled', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">启用提现功能</label>
                                        </div>
                                        <small class="text-muted">是否允许用户申请提现</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">提现门槛积分</label>
                                        <input type="number" class="form-control" name="settings[withdraw_threshold]"
                                               value="<?php echo getConfigValue($configGroups, 'withdraw_threshold', '1000'); ?>" min="1">
                                        <small class="text-muted">用户申请提现的最低积分要求</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">每日最大提现积分</label>
                                        <input type="number" class="form-control" name="settings[max_withdraw_daily]"
                                               value="<?php echo getConfigValue($configGroups, 'max_withdraw_daily', '10000'); ?>" min="1">
                                        <small class="text-muted">单个用户每日最大提现积分数量</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">提现手续费比例（%）</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="settings[withdraw_fee_rate]"
                                                   value="<?php echo getConfigValue($configGroups, 'withdraw_fee_rate', '0'); ?>" min="0" max="50" step="0.1">
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <small class="text-muted">提现时收取的手续费百分比</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 上传设置 -->
                <div class="tab-pane fade" id="group-upload">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-upload me-2"></i>上传设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">图片最大尺寸（MB）</label>
                                        <input type="number" class="form-control" name="settings[max_image_size]" 
                                               value="<?php echo getConfigValue($configGroups, 'max_image_size', '10'); ?>" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">视频最大尺寸（MB）</label>
                                        <input type="number" class="form-control" name="settings[max_video_size]" 
                                               value="<?php echo getConfigValue($configGroups, 'max_video_size', '100'); ?>" min="1">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="settings[enable_webp_convert]" value="1" 
                                                   <?php echo getConfigValue($configGroups, 'enable_webp_convert', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">启用WebP转换</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">图片质量（1-100）</label>
                                        <input type="number" class="form-control" name="settings[image_quality]" 
                                               value="<?php echo getConfigValue($configGroups, 'image_quality', '85'); ?>" min="1" max="100">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件设置 -->
                <div class="tab-pane fade" id="group-attachment">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-file-image me-2"></i>附件设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">附件模式</label>
                                        <select class="form-select" name="settings[attachment_mode]">
                                            <option value="remote" <?php echo getConfigValue($configGroups, 'attachment_mode', 'remote') === 'remote' ? 'selected' : ''; ?>>
                                                盗链模式（使用原始图片链接）
                                            </option>
                                            <option value="local" <?php echo getConfigValue($configGroups, 'attachment_mode', 'remote') === 'local' ? 'selected' : ''; ?>>
                                                本地化模式（使用本地化图片）
                                            </option>
                                        </select>
                                        <small class="form-text text-muted">
                                            选择前端显示图片的模式。盗链模式直接使用采集的图片链接，本地化模式使用已本地化的图片。
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">图片CDN地址</label>
                                        <input type="url" class="form-control" name="settings[image_cdn_url]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'image_cdn_url', 'https://cdn.example.com/')); ?>"
                                               placeholder="https://cdn.example.com/">
                                        <small class="form-text text-muted">
                                            用于拼接本地化图片的完整URL，末尾需要包含斜杠（/）。仅在本地化模式下使用。
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>使用说明</h6>
                                <ul class="mb-0">
                                    <li><strong>盗链模式</strong>：直接使用采集时的原始图片链接，节省存储空间但依赖外部资源</li>
                                    <li><strong>本地化模式</strong>：使用已本地化到FTP服务器的图片，稳定可靠但需要存储空间</li>
                                    <li>切换模式后，前端会自动使用对应的图片源</li>
                                    <li>建议先完成图片本地化后再切换到本地化模式</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FTP设置 -->
                <div class="tab-pane fade" id="group-ftp">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-server me-2"></i>FTP设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">FTP主机</label>
                                        <input type="text" class="form-control" name="settings[ftp_host]" 
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'ftp_host', '')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">FTP端口</label>
                                        <input type="number" class="form-control" name="settings[ftp_port]" 
                                               value="<?php echo getConfigValue($configGroups, 'ftp_port', '21'); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">FTP用户名</label>
                                        <input type="text" class="form-control" name="settings[ftp_username]" 
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'ftp_username', '')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">FTP密码</label>
                                        <input type="password" class="form-control" name="settings[ftp_password]" 
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'ftp_password', '')); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">FTP路径</label>
                                <input type="text" class="form-control" name="settings[ftp_path]" 
                                       value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'ftp_path', '/')); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邮件设置 -->
                <div class="tab-pane fade" id="group-email">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>邮件设置</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">SMTP主机</label>
                                        <input type="text" class="form-control" name="settings[smtp_host]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'smtp_host', '')); ?>"
                                               placeholder="smtp.example.com">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">SMTP端口</label>
                                        <input type="number" class="form-control" name="settings[smtp_port]"
                                               value="<?php echo getConfigValue($configGroups, 'smtp_port', '587'); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">SMTP用户名</label>
                                        <input type="text" class="form-control" name="settings[smtp_username]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'smtp_username', '')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">SMTP密码</label>
                                        <input type="password" class="form-control" name="settings[smtp_password]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'smtp_password', '')); ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">发件人邮箱</label>
                                        <input type="email" class="form-control" name="settings[smtp_from_email]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'smtp_from_email', '')); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">发件人名称</label>
                                        <input type="text" class="form-control" name="settings[smtp_from_name]"
                                               value="<?php echo htmlspecialchars(getConfigValue($configGroups, 'smtp_from_name', '')); ?>"
                                               placeholder="网站名称">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="settings[smtp_ssl]" value="1"
                                                   <?php echo getConfigValue($configGroups, 'smtp_ssl', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">启用SSL/TLS</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="settings[smtp_auth]" value="1"
                                                   <?php echo getConfigValue($configGroups, 'smtp_auth', '1') ? 'checked' : ''; ?>>
                                            <label class="form-check-label">启用SMTP认证</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 保存按钮 -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>保存所有设置
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="location.reload()">
                        <i class="fas fa-undo me-2"></i>重置
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<?php

$pageScript = "
    // 设置导航切换
    document.querySelectorAll('[data-bs-toggle=\"pill\"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href');
            
            // 隐藏所有tab内容
            document.querySelectorAll('.tab-pane').forEach(function(pane) {
                pane.classList.remove('show', 'active');
            });
            
            // 显示目标tab
            const targetPane = document.querySelector(target);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
            
            // 更新导航状态
            document.querySelectorAll('.list-group-item').forEach(function(item) {
                item.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
    
    // 默认激活第一个导航
    document.querySelector('.list-group-item').classList.add('active');
";

include __DIR__ . '/templates/footer.php';
?>
