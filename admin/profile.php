<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '个人资料';
$currentPage = 'profile';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $data = [
            'username' => trim($_POST['username']),
            'email' => trim($_POST['email']),
            'real_name' => trim($_POST['real_name']),
            'phone' => trim($_POST['phone'])
        ];
        
        if (empty($data['username']) || empty($data['email'])) {
            $message = '用户名和邮箱不能为空';
            $messageType = 'danger';
        } else {
            // 检查用户名是否被其他用户使用
            $existingUser = $db->fetch("SELECT id FROM {$db->getPrefix()}admin_users WHERE username = :username AND id != :id", [
                'username' => $data['username'],
                'id' => $_SESSION['admin_id']
            ]);
            
            if ($existingUser) {
                $message = '用户名已被使用';
                $messageType = 'danger';
            } else {
                $result = $db->update('admin_users', $data, 'id = :id', ['id' => $_SESSION['admin_id']]);
                if ($result) {
                    $_SESSION['admin_username'] = $data['username'];
                    $message = '个人资料更新成功';
                    $messageType = 'success';
                } else {
                    $message = '个人资料更新失败';
                    $messageType = 'danger';
                }
            }
        }
    }
}

// 获取当前管理员信息
$admin = $db->fetch("SELECT * FROM {$db->getPrefix()}admin_users WHERE id = :id", ['id' => $_SESSION['admin_id']]);

if (!$admin) {
    header('Location: /admin/logout.php');
    exit;
}

// 获取登录日志
$loginLogs = $db->fetchAll("
    SELECT * FROM {$db->getPrefix()}admin_login_logs 
    WHERE admin_id = :admin_id 
    ORDER BY created_at DESC 
    LIMIT 10
", ['admin_id' => $_SESSION['admin_id']]);

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 个人资料编辑 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>个人资料
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">用户名 *</label>
                                <input type="text" class="form-control" name="username" 
                                       value="<?php echo htmlspecialchars($admin['username']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">邮箱 *</label>
                                <input type="email" class="form-control" name="email" 
                                       value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">真实姓名</label>
                                <input type="text" class="form-control" name="real_name" 
                                       value="<?php echo htmlspecialchars($admin['real_name'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">手机号码</label>
                                <input type="tel" class="form-control" name="phone" 
                                       value="<?php echo htmlspecialchars($admin['phone'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">角色</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo htmlspecialchars($admin['role']); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">状态</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo $admin['status'] ? '正常' : '禁用'; ?>" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">创建时间</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo date('Y-m-d H:i:s', strtotime($admin['created_at'])); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">最后登录</label>
                                <input type="text" class="form-control" 
                                       value="<?php echo $admin['last_login_at'] ? date('Y-m-d H:i:s', strtotime($admin['last_login_at'])) : '从未登录'; ?>" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>保存修改
                        </button>
                        <a href="/admin/change-password.php" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>修改密码
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 侧边信息 -->
    <div class="col-md-4">
        <!-- 账户信息 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>账户信息
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <?php echo strtoupper(substr($admin['username'], 0, 1)); ?>
                    </div>
                    <h5 class="mt-2 mb-0"><?php echo htmlspecialchars($admin['username']); ?></h5>
                    <small class="text-muted"><?php echo htmlspecialchars($admin['role']); ?></small>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="mb-0"><?php echo $admin['login_count'] ?? 0; ?></h6>
                            <small class="text-muted">登录次数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-0"><?php echo date('Y-m-d', strtotime($admin['created_at'])); ?></h6>
                        <small class="text-muted">加入时间</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>安全设置
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-0">密码强度</h6>
                        <small class="text-muted">建议定期更换密码</small>
                    </div>
                    <span class="badge bg-warning">中等</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-0">两步验证</h6>
                        <small class="text-muted">增强账户安全性</small>
                    </div>
                    <span class="badge bg-secondary">未启用</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0">登录IP限制</h6>
                        <small class="text-muted">限制登录IP地址</small>
                    </div>
                    <span class="badge bg-secondary">未启用</span>
                </div>
                
                <hr>
                
                <div class="d-grid">
                    <a href="/admin/change-password.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-key me-1"></i>修改密码
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 登录日志 -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>登录日志
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($loginLogs)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无登录日志</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>登录时间</th>
                                    <th>IP地址</th>
                                    <th>用户代理</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($loginLogs as $log): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></td>
                                        <td>
                                            <code><?php echo htmlspecialchars($log['ip_address']); ?></code>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo htmlspecialchars(mb_substr($log['user_agent'], 0, 50)); ?>...
                                            </small>
                                        </td>
                                        <td>
                                            <?php if ($log['status'] === 'success'): ?>
                                                <span class="badge bg-success">成功</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">失败</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/templates/footer.php'; ?>
