<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login.php');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '提现管理';
$currentPage = 'withdraw';

// 处理操作
$message = '';
$messageType = '';

if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'update_status':
                $withdrawId = intval($_POST['withdraw_id']);
                $status = $_POST['status'];
                $adminNote = trim($_POST['admin_note'] ?? '');
                $rejectReason = trim($_POST['reject_reason'] ?? '');
                
                // 验证状态
                $validStatuses = ['pending', 'processing', 'approved', 'rejected', 'completed'];
                if (!in_array($status, $validStatuses)) {
                    throw new Exception('无效的状态');
                }
                
                // 获取提现申请信息
                $withdraw = $db->fetch("
                    SELECT * FROM {$db->getPrefix()}withdraw_requests 
                    WHERE id = :id
                ", ['id' => $withdrawId]);
                
                if (!$withdraw) {
                    throw new Exception('提现申请不存在');
                }
                
                // 更新提现申请状态
                $updateData = [
                    'status' => $status,
                    'admin_note' => $adminNote,
                    'processed_by' => $_SESSION['admin_id'],
                    'processed_at' => date('Y-m-d H:i:s')
                ];
                
                if ($status === 'rejected') {
                    $updateData['reject_reason'] = $rejectReason;
                    
                    // 解冻用户积分
                    $db->execute("
                        UPDATE {$db->getPrefix()}users 
                        SET frozen_points = GREATEST(0, frozen_points - :points)
                        WHERE id = :user_id
                    ", [
                        'points' => $withdraw['points_amount'],
                        'user_id' => $withdraw['user_id']
                    ]);
                    
                } elseif ($status === 'completed') {
                    // 扣除用户积分和冻结积分
                    $db->execute("
                        UPDATE {$db->getPrefix()}users 
                        SET points = GREATEST(0, points - :points),
                            frozen_points = GREATEST(0, frozen_points - :points)
                        WHERE id = :user_id
                    ", [
                        'points' => $withdraw['points_amount'],
                        'user_id' => $withdraw['user_id']
                    ]);
                    
                    // 记录积分流水
                    $db->execute("
                        INSERT INTO {$db->getPrefix()}points_log 
                        (user_id, type, source, amount, balance_before, balance_after, description, related_id) 
                        VALUES (:user_id, 'withdraw', 'withdraw', :amount, :balance_before, :balance_after, :description, :related_id)
                    ", [
                        'user_id' => $withdraw['user_id'],
                        'amount' => -$withdraw['points_amount'],
                        'balance_before' => 0, // 需要重新计算
                        'balance_after' => 0,  // 需要重新计算
                        'description' => '提现扣除积分',
                        'related_id' => $withdrawId
                    ]);
                }
                
                $db->execute("
                    UPDATE {$db->getPrefix()}withdraw_requests 
                    SET status = :status, admin_note = :admin_note, reject_reason = :reject_reason,
                        processed_by = :processed_by, processed_at = :processed_at
                    WHERE id = :id
                ", array_merge($updateData, ['id' => $withdrawId, 'reject_reason' => $rejectReason]));
                
                $message = '提现申请状态更新成功';
                $messageType = 'success';
                break;
                
            case 'batch_update':
                $withdrawIds = $_POST['withdraw_ids'] ?? [];
                $batchStatus = $_POST['batch_status'];
                $batchNote = trim($_POST['batch_note'] ?? '');
                
                if (empty($withdrawIds)) {
                    throw new Exception('请选择要操作的提现申请');
                }
                
                $validStatuses = ['processing', 'approved', 'rejected'];
                if (!in_array($batchStatus, $validStatuses)) {
                    throw new Exception('无效的批量操作状态');
                }
                
                $successCount = 0;
                foreach ($withdrawIds as $withdrawId) {
                    $withdrawId = intval($withdrawId);
                    
                    // 获取提现申请信息
                    $withdraw = $db->fetch("
                        SELECT * FROM {$db->getPrefix()}withdraw_requests 
                        WHERE id = :id AND status = 'pending'
                    ", ['id' => $withdrawId]);
                    
                    if ($withdraw) {
                        $updateData = [
                            'status' => $batchStatus,
                            'admin_note' => $batchNote,
                            'processed_by' => $_SESSION['admin_id'],
                            'processed_at' => date('Y-m-d H:i:s')
                        ];
                        
                        if ($batchStatus === 'rejected') {
                            // 解冻用户积分
                            $db->execute("
                                UPDATE {$db->getPrefix()}users 
                                SET frozen_points = GREATEST(0, frozen_points - :points)
                                WHERE id = :user_id
                            ", [
                                'points' => $withdraw['points_amount'],
                                'user_id' => $withdraw['user_id']
                            ]);
                        }
                        
                        $db->execute("
                            UPDATE {$db->getPrefix()}withdraw_requests 
                            SET status = :status, admin_note = :admin_note, processed_by = :processed_by, processed_at = :processed_at
                            WHERE id = :id
                        ", array_merge($updateData, ['id' => $withdrawId]));
                        
                        $successCount++;
                    }
                }
                
                $message = "批量操作成功，共处理 {$successCount} 个提现申请";
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = '操作失败：' . $e->getMessage();
        $messageType = 'error';
    }
}

// 获取筛选参数
$status = $_GET['status'] ?? '';
$userId = $_GET['user_id'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereConditions = [];
$params = [];

if ($status) {
    $whereConditions[] = "wr.status = :status";
    $params['status'] = $status;
}

if ($userId) {
    $whereConditions[] = "wr.user_id = :user_id";
    $params['user_id'] = $userId;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(wr.created_at) >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(wr.created_at) <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 获取提现申请列表
$withdraws = $db->fetchAll("
    SELECT wr.*, u.username, u.nickname,
           a.username as admin_username
    FROM {$db->getPrefix()}withdraw_requests wr
    LEFT JOIN {$db->getPrefix()}users u ON wr.user_id = u.id
    LEFT JOIN {$db->getPrefix()}admin_users a ON wr.processed_by = a.id
    {$whereClause}
    ORDER BY wr.created_at DESC
    LIMIT :limit OFFSET :offset
", array_merge($params, ['limit' => $limit, 'offset' => $offset]));

// 获取总数
$totalCount = $db->fetchColumn("
    SELECT COUNT(*) 
    FROM {$db->getPrefix()}withdraw_requests wr
    LEFT JOIN {$db->getPrefix()}users u ON wr.user_id = u.id
    {$whereClause}
", $params);

$totalPages = ceil($totalCount / $limit);

// 获取统计数据
$stats = $db->fetch("
    SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_count,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(CASE WHEN status = 'completed' THEN actual_amount ELSE 0 END) as total_paid
    FROM {$db->getPrefix()}withdraw_requests
");

$stats = $stats ?: [
    'total_requests' => 0, 'pending_count' => 0, 'processing_count' => 0,
    'approved_count' => 0, 'completed_count' => 0, 'rejected_count' => 0, 'total_paid' => 0
];

include __DIR__ . '/templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">提现管理</h3>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible">
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['total_requests']; ?></h4>
                                    <p class="mb-0">总申请</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['pending_count']; ?></h4>
                                    <p class="mb-0">待处理</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['processing_count']; ?></h4>
                                    <p class="mb-0">处理中</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['approved_count']; ?></h4>
                                    <p class="mb-0">已审核</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['completed_count']; ?></h4>
                                    <p class="mb-0">已完成</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $stats['rejected_count']; ?></h4>
                                    <p class="mb-0">已驳回</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选表单 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">状态</label>
                                    <select name="status" class="form-select">
                                        <option value="">全部状态</option>
                                        <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>待处理</option>
                                        <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>处理中</option>
                                        <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>已审核</option>
                                        <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>已完成</option>
                                        <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>已驳回</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">用户ID</label>
                                    <input type="number" name="user_id" class="form-control" value="<?php echo htmlspecialchars($userId); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($dateFrom); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($dateTo); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">筛选</button>
                                        <a href="?" class="btn btn-secondary">重置</a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 批量操作 -->
                    <div class="mb-3">
                        <button class="btn btn-success btn-sm" onclick="batchUpdate('processing')">
                            <i class="fas fa-play"></i> 批量处理中
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="batchUpdate('approved')">
                            <i class="fas fa-check"></i> 批量审核通过
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="batchUpdate('rejected')">
                            <i class="fas fa-times"></i> 批量驳回
                        </button>
                    </div>

                    <!-- 提现申请列表 -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>ID</th>
                                    <th>用户</th>
                                    <th>积分/金额</th>
                                    <th>收款方式</th>
                                    <th>状态</th>
                                    <th>申请时间</th>
                                    <th>处理人</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($withdraws)): ?>
                                    <tr>
                                        <td colspan="9" class="text-center text-muted py-4">
                                            <i class="fas fa-inbox fa-2x mb-2"></i>
                                            <p>暂无提现申请</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($withdraws as $withdraw): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="withdraw-checkbox" value="<?php echo $withdraw['id']; ?>">
                                            </td>
                                            <td><?php echo $withdraw['id']; ?></td>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($withdraw['username']); ?></strong>
                                                    <?php if ($withdraw['nickname']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($withdraw['nickname']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?php echo number_format($withdraw['points_amount']); ?></strong> 积分
                                                    <br><small class="text-success">¥<?php echo number_format($withdraw['actual_amount'], 2); ?></small>
                                                    <?php if ($withdraw['fee_amount'] > 0): ?>
                                                        <br><small class="text-muted">手续费: ¥<?php echo number_format($withdraw['fee_amount'], 2); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $accountInfo = json_decode($withdraw['account_info'], true);
                                                $accountTypeNames = [
                                                    'alipay' => '支付宝',
                                                    'wechat' => '微信',
                                                    'bank' => '银行卡'
                                                ];
                                                ?>
                                                <div>
                                                    <span class="badge bg-info"><?php echo $accountTypeNames[$withdraw['account_type']] ?? $withdraw['account_type']; ?></span>
                                                    <br><small><?php echo htmlspecialchars($accountInfo['name'] ?? ''); ?></small>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($accountInfo['number'] ?? ''); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo match($withdraw['status']) {
                                                        'pending' => 'warning',
                                                        'processing' => 'info',
                                                        'approved' => 'primary',
                                                        'completed' => 'success',
                                                        'rejected' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php
                                                        echo match($withdraw['status']) {
                                                            'pending' => '待处理',
                                                            'processing' => '处理中',
                                                            'approved' => '已审核',
                                                            'completed' => '已完成',
                                                            'rejected' => '已驳回',
                                                            default => $withdraw['status']
                                                        };
                                                    ?>
                                                </span>
                                                <?php if ($withdraw['reject_reason']): ?>
                                                    <br><small class="text-danger"><?php echo htmlspecialchars($withdraw['reject_reason']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo date('Y-m-d H:i', strtotime($withdraw['created_at'])); ?></td>
                                            <td>
                                                <?php if ($withdraw['admin_username']): ?>
                                                    <?php echo htmlspecialchars($withdraw['admin_username']); ?>
                                                    <br><small class="text-muted"><?php echo date('m-d H:i', strtotime($withdraw['processed_at'])); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"
                                                        onclick="showUpdateModal(<?php echo htmlspecialchars(json_encode($withdraw)); ?>)">
                                                    <i class="fas fa-edit"></i> 处理
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <nav>
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 更新状态模态框 -->
<div class="modal fade" id="updateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="updateForm">
                <div class="modal-header">
                    <h5 class="modal-title">处理提现申请</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="withdraw_id" id="withdrawId">

                    <div class="mb-3">
                        <label class="form-label">申请信息</label>
                        <div id="withdrawInfo" class="alert alert-info"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <select name="status" id="statusSelect" class="form-select" required>
                            <option value="pending">待处理</option>
                            <option value="processing">处理中</option>
                            <option value="approved">已审核</option>
                            <option value="completed">已完成</option>
                            <option value="rejected">已驳回</option>
                        </select>
                    </div>

                    <div class="mb-3" id="rejectReasonDiv" style="display: none;">
                        <label class="form-label">驳回原因</label>
                        <textarea name="reject_reason" class="form-control" rows="3" placeholder="请填写驳回原因"></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">管理员备注</label>
                        <textarea name="admin_note" class="form-control" rows="3" placeholder="可选的管理员备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="batchForm">
                <div class="modal-header">
                    <h5 class="modal-title">批量操作</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="batch_update">
                    <input type="hidden" name="batch_status" id="batchStatus">

                    <div class="mb-3">
                        <label class="form-label">选中的申请</label>
                        <div id="selectedCount" class="alert alert-info"></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">批量备注</label>
                        <textarea name="batch_note" class="form-control" rows="3" placeholder="可选的批量操作备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认操作</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 全选/取消全选
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.withdraw-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// 显示更新模态框
function showUpdateModal(withdraw) {
    document.getElementById('withdrawId').value = withdraw.id;
    document.getElementById('statusSelect').value = withdraw.status;

    // 显示申请信息
    const accountInfo = JSON.parse(withdraw.account_info);
    const accountTypeNames = {
        'alipay': '支付宝',
        'wechat': '微信',
        'bank': '银行卡'
    };

    const info = `
        <strong>用户：</strong>${withdraw.username}<br>
        <strong>积分：</strong>${parseInt(withdraw.points_amount).toLocaleString()} 积分<br>
        <strong>金额：</strong>¥${parseFloat(withdraw.actual_amount).toFixed(2)}<br>
        <strong>收款方式：</strong>${accountTypeNames[withdraw.account_type] || withdraw.account_type}<br>
        <strong>收款账号：</strong>${accountInfo.name} - ${accountInfo.number}
    `;

    document.getElementById('withdrawInfo').innerHTML = info;

    // 根据状态显示/隐藏驳回原因
    toggleRejectReason();

    const modal = new bootstrap.Modal(document.getElementById('updateModal'));
    modal.show();
}

// 状态改变时切换驳回原因显示
document.getElementById('statusSelect').addEventListener('change', toggleRejectReason);

function toggleRejectReason() {
    const status = document.getElementById('statusSelect').value;
    const rejectDiv = document.getElementById('rejectReasonDiv');

    if (status === 'rejected') {
        rejectDiv.style.display = 'block';
        rejectDiv.querySelector('textarea').required = true;
    } else {
        rejectDiv.style.display = 'none';
        rejectDiv.querySelector('textarea').required = false;
    }
}

// 批量操作
function batchUpdate(status) {
    const checkboxes = document.querySelectorAll('.withdraw-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('请选择要操作的提现申请');
        return;
    }

    // 设置批量状态
    document.getElementById('batchStatus').value = status;

    // 显示选中数量
    const statusNames = {
        'processing': '处理中',
        'approved': '审核通过',
        'rejected': '驳回'
    };

    document.getElementById('selectedCount').innerHTML =
        `将对 <strong>${checkboxes.length}</strong> 个提现申请设置为：<strong>${statusNames[status]}</strong>`;

    // 添加选中的ID到表单
    const form = document.getElementById('batchForm');

    // 移除之前的隐藏字段
    const existingInputs = form.querySelectorAll('input[name="withdraw_ids[]"]');
    existingInputs.forEach(input => input.remove());

    // 添加新的隐藏字段
    checkboxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'withdraw_ids[]';
        input.value = checkbox.value;
        form.appendChild(input);
    });

    const modal = new bootstrap.Modal(document.getElementById('batchModal'));
    modal.show();
}
</script>

<?php include __DIR__ . '/templates/footer.php'; ?>
