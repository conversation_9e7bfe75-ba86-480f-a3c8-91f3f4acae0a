<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '采集管理';
$currentPage = 'spider';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add_rule':
            $data = [
                'name' => trim($_POST['name']),
                'source_url' => trim($_POST['source_url']),
                'list_selector' => trim($_POST['list_selector']),
                'title_selector' => trim($_POST['title_selector']),
                'image_selector' => trim($_POST['image_selector']),
                'content_selector' => trim($_POST['content_selector']),
                'category_id' => intval($_POST['category_id']),
                'is_active' => intval($_POST['is_active']),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            if (empty($data['name']) || empty($data['source_url'])) {
                $message = '规则名称和源网址不能为空';
                $messageType = 'danger';
            } else {
                $result = $db->insert('spider_rules', $data);
                if ($result) {
                    $message = '采集规则添加成功';
                    $messageType = 'success';
                } else {
                    $message = '采集规则添加失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'delete_rule':
            $ruleId = intval($_POST['rule_id']);
            if ($ruleId > 0) {
                $result = $db->delete('spider_rules', 'id = :id', ['id' => $ruleId]);
                if ($result) {
                    $message = '采集规则删除成功';
                    $messageType = 'success';
                } else {
                    $message = '采集规则删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'toggle_rule':
            $ruleId = intval($_POST['rule_id']);
            $isActive = intval($_POST['is_active']);
            if ($ruleId > 0) {
                $result = $db->update('spider_rules', ['is_active' => $isActive], 'id = :id', ['id' => $ruleId]);
                if ($result) {
                    $message = '规则状态更新成功';
                    $messageType = 'success';
                } else {
                    $message = '规则状态更新失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'run_spider':
            $ruleId = intval($_POST['rule_id']);
            if ($ruleId > 0) {
                // 这里应该调用采集脚本
                $message = '采集任务已启动，请查看采集日志';
                $messageType = 'info';
            }
            break;
    }
}

// 获取采集规则列表
$spiderRules = $db->fetchAll("
    SELECT sr.*, c.name as category_name 
    FROM {$db->getPrefix()}spider_rules sr 
    LEFT JOIN {$db->getPrefix()}categories c ON sr.category_id = c.id 
    ORDER BY sr.created_at DESC
");

// 获取分类列表
$categories = $db->fetchAll("SELECT * FROM {$db->getPrefix()}categories WHERE status = 1 ORDER BY sort_order ASC");

// 获取采集日志
$spiderLogs = $db->fetchAll("
    SELECT * FROM {$db->getPrefix()}spider_logs 
    ORDER BY created_at DESC 
    LIMIT 20
");

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 采集规则列表 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-spider me-2"></i>采集规则
                </h5>
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRuleModal">
                    <i class="fas fa-plus me-1"></i>添加规则
                </button>
            </div>
            <div class="card-body p-0">
                <?php if (empty($spiderRules)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-spider fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无采集规则</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRuleModal">
                            <i class="fas fa-plus me-2"></i>添加第一个规则
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>规则名称</th>
                                    <th>源网址</th>
                                    <th>目标分类</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($spiderRules as $rule): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($rule['name']); ?></strong>
                                        </td>
                                        <td>
                                            <a href="<?php echo htmlspecialchars($rule['source_url']); ?>" target="_blank" class="text-decoration-none">
                                                <?php echo htmlspecialchars(parse_url($rule['source_url'], PHP_URL_HOST)); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php if ($rule['category_name']): ?>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($rule['category_name']); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">未分类</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($rule['is_active']): ?>
                                                <span class="badge bg-success">启用</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">禁用</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($rule['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-success" onclick="runSpider(<?php echo $rule['id']; ?>)" title="运行采集">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-<?php echo $rule['is_active'] ? 'warning' : 'success'; ?>" 
                                                        onclick="toggleRule(<?php echo $rule['id']; ?>, <?php echo $rule['is_active'] ? 0 : 1; ?>)" 
                                                        title="<?php echo $rule['is_active'] ? '禁用' : '启用'; ?>">
                                                    <i class="fas fa-<?php echo $rule['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-primary" onclick="editRule(<?php echo $rule['id']; ?>)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteRule(<?php echo $rule['id']; ?>)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 采集日志 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-list me-2"></i>采集日志</h6>
            </div>
            <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($spiderLogs)): ?>
                    <div class="text-center py-3">
                        <small class="text-muted">暂无日志</small>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($spiderLogs as $log): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <small class="text-muted"><?php echo date('m-d H:i', strtotime($log['created_at'])); ?></small>
                                        <p class="mb-1 small"><?php echo htmlspecialchars($log['message']); ?></p>
                                    </div>
                                    <span class="badge bg-<?php echo $log['status'] === 'success' ? 'success' : ($log['status'] === 'error' ? 'danger' : 'warning'); ?>">
                                        <?php echo $log['status']; ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 采集设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cog me-2"></i>采集设置</h6>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/settings.php">
                    <input type="hidden" name="action" value="save_spider_settings">
                    
                    <div class="mb-3">
                        <label class="form-label">采集间隔（秒）</label>
                        <input type="number" class="form-control" name="spider_interval" value="5" min="1">
                        <small class="text-muted">两次请求之间的间隔时间</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">超时时间（秒）</label>
                        <input type="number" class="form-control" name="spider_timeout" value="30" min="5">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">User-Agent</label>
                        <input type="text" class="form-control" name="spider_user_agent" 
                               value="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="spider_auto_webp" value="1" checked>
                            <label class="form-check-label">自动转换WebP</label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-save me-1"></i>保存设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 添加规则模态框 -->
<div class="modal fade" id="addRuleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加采集规则</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="add_rule">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">规则名称 *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">目标分类</label>
                                <select class="form-select" name="category_id">
                                    <option value="0">未分类</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>">
                                            <?php echo htmlspecialchars($category['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">源网址 *</label>
                        <input type="url" class="form-control" name="source_url" required placeholder="https://example.com/list">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">列表选择器</label>
                        <input type="text" class="form-control" name="list_selector" placeholder=".item-list a">
                        <small class="text-muted">用于选择列表页面中的链接</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">标题选择器</label>
                        <input type="text" class="form-control" name="title_selector" placeholder="h1, .title">
                        <small class="text-muted">用于提取文章标题</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">图片选择器</label>
                        <input type="text" class="form-control" name="image_selector" placeholder=".content img">
                        <small class="text-muted">用于提取图片</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">内容选择器</label>
                        <input type="text" class="form-control" name="content_selector" placeholder=".content, .article-body">
                        <small class="text-muted">用于提取文章内容</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="is_active" value="1" checked>
                            <label class="form-check-label">启用规则</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加规则</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$pageScript = "
    function runSpider(ruleId) {
        if (confirm('确定要运行这个采集规则吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"run_spider\"><input type=\"hidden\" name=\"rule_id\" value=\"' + ruleId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function toggleRule(ruleId, isActive) {
        const action = isActive ? '启用' : '禁用';
        if (confirm('确定要' + action + '这个规则吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"toggle_rule\"><input type=\"hidden\" name=\"rule_id\" value=\"' + ruleId + '\"><input type=\"hidden\" name=\"is_active\" value=\"' + isActive + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function deleteRule(ruleId) {
        if (confirmDelete('确定要删除这个采集规则吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete_rule\"><input type=\"hidden\" name=\"rule_id\" value=\"' + ruleId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function editRule(ruleId) {
        // 这里可以实现编辑功能
        showInfo('编辑功能开发中...');
    }
";

include __DIR__ . '/templates/footer.php';
?>
