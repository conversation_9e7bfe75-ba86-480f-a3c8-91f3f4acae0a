<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$id = intval($_GET['id'] ?? 0);

if (!$id) {
    echo '<script>alert("无效的工单ID"); window.close();</script>';
    exit;
}

// 获取工单详情
$sql = "SELECT f.*, u.username, u.nickname, u.email as user_email
        FROM {$db->getPrefix()}feedback f
        LEFT JOIN {$db->getPrefix()}users u ON f.user_id = u.id
        WHERE f.id = :id";

$feedback = $db->fetch($sql, ['id' => $id]);

// 获取工单回复记录
$replies = [];
if ($feedback) {
    $replies = $db->fetchAll("
        SELECT r.*,
               u.username, u.nickname,
               a.username as admin_username
        FROM {$db->getPrefix()}feedback_replies r
        LEFT JOIN {$db->getPrefix()}users u ON r.user_id = u.id
        LEFT JOIN {$db->getPrefix()}admin_users a ON r.admin_id = a.id
        WHERE r.feedback_id = :feedback_id
        ORDER BY r.created_at ASC
    ", ['feedback_id' => $id]) ?: [];
}

if (!$feedback) {
    echo '<script>alert("工单不存在"); window.close();</script>';
    exit;
}

$statusText = [
    'pending' => '待处理',
    'processing' => '处理中',
    'resolved' => '已解决',
    'closed' => '已关闭'
];

$priorityText = [
    'low' => '低',
    'medium' => '中',
    'high' => '高'
];

$subjectText = [
    'bug' => 'Bug反馈',
    'feature' => '功能建议',
    'content' => '内容问题',
    'account' => '账户问题',
    'payment' => '支付问题',
    'technical' => '技术问题',
    'other' => '其他问题'
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单详情 #<?php echo $feedback['id']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .feedback-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .status-badge { font-size: 0.9rem; }
        .message-content { 
            background: #fff; 
            border-left: 4px solid #007bff; 
            padding: 20px; 
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .admin-reply { 
            background: #f8f9fa; 
            border-left: 4px solid #28a745; 
            padding: 20px; 
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- 工单头部 -->
        <div class="card mb-4">
            <div class="card-header feedback-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">
                            <i class="fas fa-ticket-alt me-2"></i>
                            工单 #<?php echo $feedback['id']; ?>
                        </h4>
                        <p class="mb-0 opacity-75">
                            <?php echo $feedback['user_id'] ? '客服工单' : '意见反馈'; ?> - 
                            <?php echo $subjectText[$feedback['subject']] ?? $feedback['subject']; ?>
                        </p>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-light btn-sm" onclick="window.close()">
                            <i class="fas fa-times me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 工单信息 -->
            <div class="col-md-8">
                <!-- 用户消息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>用户反馈
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="message-content">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <strong>
                                        <?php if ($feedback['user_id']): ?>
                                            <?php echo htmlspecialchars($feedback['nickname'] ?: $feedback['username']); ?>
                                        <?php else: ?>
                                            <?php echo htmlspecialchars($feedback['name']); ?>
                                        <?php endif; ?>
                                    </strong>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo htmlspecialchars($feedback['user_email'] ?: $feedback['email']); ?>
                                    </small>
                                </div>
                                <small class="text-muted">
                                    <?php echo date('Y-m-d H:i:s', strtotime($feedback['created_at'])); ?>
                                </small>
                            </div>
                            <div class="message-text">
                                <?php echo nl2br(htmlspecialchars($feedback['message'])); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 对话记录 -->
                <?php if (!empty($replies)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-comments me-2"></i>对话记录 (<?php echo count($replies); ?>条)
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <?php foreach ($replies as $reply): ?>
                            <div class="timeline-item mb-4">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <?php if ($reply['admin_id']): ?>
                                        <div class="avatar bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <?php else: ?>
                                        <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <strong>
                                                    <?php if ($reply['admin_id']): ?>
                                                        管理员 (<?php echo htmlspecialchars($reply['admin_username']); ?>)
                                                    <?php else: ?>
                                                        用户 (<?php echo htmlspecialchars($reply['nickname'] ?: $reply['username']); ?>)
                                                    <?php endif; ?>
                                                </strong>
                                                <?php if ($reply['admin_id']): ?>
                                                <span class="badge bg-success ms-2">官方回复</span>
                                                <?php else: ?>
                                                <span class="badge bg-primary ms-2">用户回复</span>
                                                <?php endif; ?>

                                                <?php if ($reply['is_internal']): ?>
                                                <span class="badge bg-warning ms-1">内部备注</span>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d H:i:s', strtotime($reply['created_at'])); ?>
                                            </small>
                                        </div>
                                        <div class="<?php echo $reply['admin_id'] ? 'admin-reply' : 'message-content'; ?>">
                                            <?php echo nl2br(htmlspecialchars($reply['content'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 添加回复 -->
                <?php if (!in_array($feedback['status'], ['resolved', 'closed'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-reply me-2"></i>添加回复
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/api/feedback-reply.php">
                            <input type="hidden" name="feedback_id" value="<?php echo $feedback['id']; ?>">

                            <div class="mb-3">
                                <label class="form-label">回复内容 *</label>
                                <textarea name="content" class="form-control" rows="4" required
                                          placeholder="输入回复内容..."></textarea>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_internal" id="is_internal">
                                        <label class="form-check-label" for="is_internal">
                                            内部备注 <small class="text-muted">(仅管理员可见)</small>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="update_status" id="update_status">
                                        <label class="form-check-label" for="update_status">
                                            同时更新工单状态
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3" id="status_section" style="display: none;">
                                <div class="col-md-6">
                                    <label class="form-label">新状态</label>
                                    <select name="new_status" class="form-select">
                                        <?php foreach ($statusText as $key => $text): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $key === 'processing' ? 'selected' : ''; ?>>
                                            <?php echo $text; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-1"></i>发送回复
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="window.close()">
                                    <i class="fas fa-times me-1"></i>取消
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 状态管理 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-cog me-2"></i>状态管理
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="feedback.php">
                            <input type="hidden" name="action" value="update_status">
                            <input type="hidden" name="id" value="<?php echo $feedback['id']; ?>">

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">当前状态</label>
                                    <select name="status" class="form-select" required>
                                        <?php foreach ($statusText as $key => $text): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $feedback['status'] === $key ? 'selected' : ''; ?>>
                                            <?php echo $text; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">状态变更备注</label>
                                <textarea name="status_note" class="form-control" rows="2"
                                          placeholder="可选：说明状态变更原因..."></textarea>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-1"></i>更新状态
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 侧边栏信息 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>工单信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>工单ID:</strong></td>
                                <td>#<?php echo $feedback['id']; ?></td>
                            </tr>
                            <tr>
                                <td><strong>类型:</strong></td>
                                <td>
                                    <?php if ($feedback['user_id']): ?>
                                        <span class="badge bg-info">客服工单</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">意见反馈</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>主题:</strong></td>
                                <td><?php echo $subjectText[$feedback['subject']] ?? $feedback['subject']; ?></td>
                            </tr>
                            <tr>
                                <td><strong>优先级:</strong></td>
                                <td>
                                    <?php
                                    $priorityClass = [
                                        'low' => 'success',
                                        'medium' => 'warning',
                                        'high' => 'danger'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $priorityClass[$feedback['priority']]; ?>">
                                        <?php echo $priorityText[$feedback['priority']]; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>状态:</strong></td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'resolved' => 'success',
                                        'closed' => 'secondary'
                                    ];
                                    ?>
                                    <span class="badge bg-<?php echo $statusClass[$feedback['status']]; ?>">
                                        <?php echo $statusText[$feedback['status']]; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>创建时间:</strong></td>
                                <td><?php echo date('Y-m-d H:i:s', strtotime($feedback['created_at'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong>更新时间:</strong></td>
                                <td><?php echo date('Y-m-d H:i:s', strtotime($feedback['updated_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- 用户信息 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>用户信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <?php if ($feedback['user_id']): ?>
                            <tr>
                                <td><strong>用户ID:</strong></td>
                                <td><?php echo $feedback['user_id']; ?></td>
                            </tr>
                            <tr>
                                <td><strong>用户名:</strong></td>
                                <td><?php echo htmlspecialchars($feedback['username']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>昵称:</strong></td>
                                <td><?php echo htmlspecialchars($feedback['nickname'] ?: '未设置'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>邮箱:</strong></td>
                                <td><?php echo htmlspecialchars($feedback['user_email']); ?></td>
                            </tr>
                            <?php else: ?>
                            <tr>
                                <td><strong>姓名:</strong></td>
                                <td><?php echo htmlspecialchars($feedback['name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>邮箱:</strong></td>
                                <td><?php echo htmlspecialchars($feedback['email']); ?></td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        未注册用户提交的反馈
                                    </small>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 控制状态更新选项显示
        document.getElementById('update_status').addEventListener('change', function() {
            const statusSection = document.getElementById('status_section');
            statusSection.style.display = this.checked ? 'block' : 'none';
        });

        // 自动滚动到最新回复
        document.addEventListener('DOMContentLoaded', function() {
            const timeline = document.querySelector('.timeline');
            if (timeline) {
                const lastItem = timeline.querySelector('.timeline-item:last-child');
                if (lastItem) {
                    lastItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    </script>
</body>
</html>
