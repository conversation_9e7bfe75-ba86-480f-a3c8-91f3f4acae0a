<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '用户组管理';
$currentPage = 'user-groups';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $data = [
                'group_key' => trim($_POST['group_key']),
                'group_name' => trim($_POST['group_name']),
                'free_image_count' => intval($_POST['free_image_count']),
                'can_view_video' => isset($_POST['can_view_video']) ? 1 : 0,
                'can_download' => isset($_POST['can_download']) ? 1 : 0,
                'default_images_per_page' => intval($_POST['default_images_per_page']),
                'max_images_per_page' => intval($_POST['max_images_per_page']),
                'duration_days' => intval($_POST['duration_days']),
                'purchase_link' => trim($_POST['purchase_link'] ?? ''),
                'sort_order' => intval($_POST['sort_order']),
                'status' => isset($_POST['status']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            if (empty($data['group_key']) || empty($data['group_name'])) {
                $message = '用户组标识和名称不能为空';
                $messageType = 'danger';
            } else {
                try {
                    // 检查group_key是否已存在
                    $existingGroup = $db->fetchColumn(
                        "SELECT COUNT(*) FROM {$db->getPrefix()}user_groups WHERE group_key = :group_key",
                        ['group_key' => $data['group_key']]
                    );

                    if ($existingGroup > 0) {
                        $message = '用户组标识已存在，请使用其他标识';
                        $messageType = 'danger';
                    } else {
                        $result = $db->insert('user_groups', $data);
                        if ($result) {
                            $message = '用户组添加成功';
                            $messageType = 'success';
                        } else {
                            $message = '用户组添加失败：数据库操作返回false';
                            $messageType = 'danger';
                        }
                    }
                } catch (Exception $e) {
                    $message = '用户组添加失败：' . $e->getMessage();
                    $messageType = 'danger';
                    error_log('用户组添加错误: ' . $e->getMessage());
                }
            }
            break;
            
        case 'edit':
            $id = intval($_POST['id']);
            $data = [
                'group_key' => trim($_POST['group_key']),
                'group_name' => trim($_POST['group_name']),
                'free_image_count' => intval($_POST['free_image_count']),
                'can_view_video' => isset($_POST['can_view_video']) ? 1 : 0,
                'can_download' => isset($_POST['can_download']) ? 1 : 0,
                'default_images_per_page' => intval($_POST['default_images_per_page']),
                'max_images_per_page' => intval($_POST['max_images_per_page']),
                'duration_days' => intval($_POST['duration_days']),
                'purchase_link' => trim($_POST['purchase_link'] ?? ''),
                'sort_order' => intval($_POST['sort_order']),
                'status' => isset($_POST['status']) ? 1 : 0
            ];
            
            if (empty($data['group_key']) || empty($data['group_name'])) {
                $message = '用户组标识和名称不能为空';
                $messageType = 'danger';
            } else {
                try {
                    // 检查group_key是否已被其他用户组使用
                    $existingGroup = $db->fetchColumn(
                        "SELECT COUNT(*) FROM {$db->getPrefix()}user_groups WHERE group_key = :group_key AND id != :id",
                        ['group_key' => $data['group_key'], 'id' => $id]
                    );

                    if ($existingGroup > 0) {
                        $message = '用户组标识已被其他用户组使用，请使用其他标识';
                        $messageType = 'danger';
                    } else {
                        $result = $db->update('user_groups', $data, 'id = :id', ['id' => $id]);
                        if ($result !== false) {
                            $message = '用户组更新成功';
                            $messageType = 'success';
                        } else {
                            $message = '用户组更新失败：数据库操作错误';
                            $messageType = 'danger';
                        }
                    }
                } catch (Exception $e) {
                    $message = '用户组更新失败：' . $e->getMessage();
                    $messageType = 'danger';
                    error_log('用户组更新错误: ' . $e->getMessage());
                }
            }
            break;
            
        case 'delete':
            $id = intval($_POST['id']);
            if ($id > 0) {
                // 检查是否有用户使用此用户组
                $userCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE group_id = :id", ['id' => $id]);
                if ($userCount > 0) {
                    $message = '该用户组下还有用户，无法删除';
                    $messageType = 'danger';
                } else {
                    $result = $db->delete('user_groups', 'id = :id', ['id' => $id]);
                    if ($result) {
                        $message = '用户组删除成功';
                        $messageType = 'success';
                    } else {
                        $message = '用户组删除失败';
                        $messageType = 'danger';
                    }
                }
            }
            break;
    }
}

// 获取用户组列表
$userGroups = $db->fetchAll("SELECT ug.*, COUNT(u.id) as user_count
                            FROM {$db->getPrefix()}user_groups ug
                            LEFT JOIN {$db->getPrefix()}users u ON ug.id = u.group_id
                            GROUP BY ug.id
                            ORDER BY ug.sort_order ASC");

// 获取编辑的用户组
$editGroup = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $editId = intval($_GET['id']);
    $editGroup = $db->fetch("SELECT * FROM {$db->getPrefix()}user_groups WHERE id = :id", ['id' => $editId]);
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 用户组列表 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-tag me-2"></i>用户组列表
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($userGroups)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-user-tag fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无用户组数据</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>用户组名称</th>
                                    <th>标识</th>
                                    <th>免费图片</th>
                                    <th>视频权限</th>
                                    <th>下载权限</th>
                                    <th>有效期</th>
                                    <th>购买链接</th>
                                    <th>用户数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($userGroups as $group): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($group['group_name']); ?></strong>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($group['group_key']); ?></code>
                                        </td>
                                        <td>
                                            <?php if ($group['free_image_count'] == -1): ?>
                                                <span class="badge bg-success">无限制</span>
                                            <?php else: ?>
                                                <?php echo number_format($group['free_image_count']); ?>张
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($group['can_view_video']): ?>
                                                <span class="badge bg-success">可观看</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">不可观看</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($group['can_download']): ?>
                                                <span class="badge bg-success">可下载</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">不可下载</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($group['duration_days'] == -1): ?>
                                                <span class="badge bg-warning text-dark">永久</span>
                                            <?php elseif ($group['duration_days'] == 0): ?>
                                                <span class="badge bg-info">无期限</span>
                                            <?php else: ?>
                                                <?php echo $group['duration_days']; ?>天
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($group['purchase_link'])): ?>
                                                <a href="<?php echo htmlspecialchars($group['purchase_link']); ?>"
                                                   target="_blank" class="btn btn-sm btn-outline-success" title="查看购买链接">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">未设置</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo number_format($group['user_count']); ?></span>
                                        </td>
                                        <td>
                                            <?php if ($group['status']): ?>
                                                <span class="badge bg-success">启用</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">禁用</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $group['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($group['user_count'] == 0): ?>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteGroup(<?php echo $group['id']; ?>)" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑用户组 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo $editGroup ? 'edit' : 'plus'; ?> me-2"></i>
                    <?php echo $editGroup ? '编辑用户组' : '添加用户组'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $editGroup ? 'edit' : 'add'; ?>">
                    <?php if ($editGroup): ?>
                        <input type="hidden" name="id" value="<?php echo $editGroup['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label class="form-label">用户组标识 *</label>
                        <input type="text" class="form-control" name="group_key" 
                               value="<?php echo htmlspecialchars($editGroup['group_key'] ?? ''); ?>" 
                               placeholder="如：vip_month" required>
                        <small class="text-muted">英文标识，用于程序识别</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">用户组名称 *</label>
                        <input type="text" class="form-control" name="group_name" 
                               value="<?php echo htmlspecialchars($editGroup['group_name'] ?? ''); ?>" 
                               placeholder="如：月费会员" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">免费图片数量</label>
                        <input type="number" class="form-control" name="free_image_count" 
                               value="<?php echo $editGroup['free_image_count'] ?? 10; ?>" min="-1">
                        <small class="text-muted">-1表示无限制</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">默认每页图片数</label>
                        <input type="number" class="form-control" name="default_images_per_page" 
                               value="<?php echo $editGroup['default_images_per_page'] ?? 1; ?>" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">最大每页图片数</label>
                        <input type="number" class="form-control" name="max_images_per_page" 
                               value="<?php echo $editGroup['max_images_per_page'] ?? 1; ?>" min="-1">
                        <small class="text-muted">-1表示无限制</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">有效期（天）</label>
                        <input type="number" class="form-control" name="duration_days"
                               value="<?php echo $editGroup['duration_days'] ?? 0; ?>" min="-1">
                        <small class="text-muted">0表示无期限，-1表示永久</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">第三方购买链接</label>
                        <input type="url" class="form-control" name="purchase_link"
                               value="<?php echo htmlspecialchars($editGroup['purchase_link'] ?? ''); ?>"
                               placeholder="https://example.com/buy">
                        <small class="text-muted">填写发卡站或第三方购买页面的链接，用户点击"立即购买"时将跳转到此链接</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">排序</label>
                        <input type="number" class="form-control" name="sort_order"
                               value="<?php echo $editGroup['sort_order'] ?? 0; ?>">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="can_view_video" value="1" 
                                   <?php echo ($editGroup['can_view_video'] ?? 0) ? 'checked' : ''; ?>>
                            <label class="form-check-label">可观看视频</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="can_download" value="1" 
                                   <?php echo ($editGroup['can_download'] ?? 0) ? 'checked' : ''; ?>>
                            <label class="form-check-label">可下载内容</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="status" value="1" 
                                   <?php echo ($editGroup['status'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label">启用状态</label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i><?php echo $editGroup ? '更新' : '添加'; ?>
                        </button>
                        <?php if ($editGroup): ?>
                            <a href="/admin/user-groups.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>取消
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$pageScript = "
    function deleteGroup(groupId) {
        if (confirmDelete('确定要删除这个用户组吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + groupId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
";

include __DIR__ . '/templates/footer.php';
?>
