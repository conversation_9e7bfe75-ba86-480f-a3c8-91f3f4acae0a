<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '火车头发布';
$currentPage = 'train-publish';

// 处理火车头发布请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'publish') {
        // 验证发布密钥
        $publishKey = $_POST['publish_key'] ?? '';
        $configKey = $db->fetchColumn("SELECT value FROM {$db->getPrefix()}settings WHERE name = 'train_publish_key'") ?: 'your_secret_key';

        if ($publishKey !== $configKey) {
            http_response_code(403);
            echo json_encode(['error' => 'Invalid publish key']);
            exit;
        }
        
        // 获取发布数据
        $title = trim($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $categoryId = intval($_POST['category_id'] ?? 0);
        $tags = trim($_POST['tags'] ?? '');
        $images = $_POST['images'] ?? [];

        // 数据验证
        if (empty($title)) {
            http_response_code(400);
            echo json_encode(['error' => 'Title is required']);
            exit;
        }

        if (strlen($title) > 200) {
            http_response_code(400);
            echo json_encode(['error' => 'Title too long (max 200 characters)']);
            exit;
        }

        // 验证分类ID是否存在
        if ($categoryId > 0) {
            $categoryExists = $db->fetchColumn(
                "SELECT COUNT(*) FROM {$db->getPrefix()}categories WHERE id = :id AND status = 1 AND type = 'album'",
                ['id' => $categoryId]
            );
            if (!$categoryExists) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid category ID']);
                exit;
            }
        }
        
        try {
            // 生成slug，确保唯一性
            $slug = generateSlug($title);
            $originalSlug = $slug;
            $counter = 1;

            // 检查slug是否已存在
            while ($db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE slug = :slug", ['slug' => $slug])) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // 插入套图数据
            $albumData = [
                'title' => $title,
                'slug' => $slug,
                'description' => $content,
                'category_id' => $categoryId ?: null,
                'tags' => $tags,
                'status' => 1,
                'is_free' => 0,
                'view_count' => 0,
                'like_count' => 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $albumId = $db->insert('albums', $albumData);

            if (!$albumId) {
                throw new Exception('Failed to create album');
            }

            // 处理图片数据
            if (!empty($images) && is_array($images)) {
                $validImages = [];
                foreach ($images as $index => $imageUrl) {
                    $imageUrl = trim($imageUrl);
                    if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                        $imageData = [
                            'album_id' => $albumId,
                            'image_url' => $imageUrl,
                            'sort_order' => $index + 1,
                            'created_at' => date('Y-m-d H:i:s')
                        ];

                        if ($db->insert('album_images', $imageData)) {
                            $validImages[] = $imageUrl;
                        }
                    }
                }

                // 设置封面图片和图片数量
                if (!empty($validImages)) {
                    $db->update('albums', [
                        'cover_image' => $validImages[0],
                        'image_count' => count($validImages)
                    ], 'id = :id', ['id' => $albumId]);
                }
            }

            echo json_encode([
                'success' => true,
                'album_id' => $albumId,
                'slug' => $slug,
                'image_count' => count($validImages ?? [])
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
        }
        exit;
    }
}

// 获取发布统计
$publishStats = [
    'today' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE DATE(created_at) = CURDATE()") ?: 0,
    'week' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)") ?: 0,
    'month' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0,
    'total' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums") ?: 0
];

// 获取最近发布的内容
$recentPublished = $db->fetchAll("
    SELECT title, created_at, view_count 
    FROM {$db->getPrefix()}albums 
    ORDER BY created_at DESC 
    LIMIT 10
");

// 获取分类列表（只获取套图分类）
$categories = $db->fetchAll("SELECT * FROM {$db->getPrefix()}categories WHERE status = 1 AND type = 'album' ORDER BY sort ASC");

// 生成slug的函数
function generateSlug($title) {
    // 对于中文标题，使用拼音或者时间戳
    if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $title)) {
        // 包含中文，使用时间戳和随机数
        return 'album-' . time() . '-' . rand(1000, 9999);
    }

    // 英文标题的处理
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    $slug = preg_replace('/-+/', '-', $slug); // 去除多余的连字符
    $slug = trim($slug, '-'); // 去除首尾连字符

    return substr($slug, 0, 100) ?: 'album-' . time();
}

include __DIR__ . '/templates/header.php';
?>

<div class="row">
    <!-- 发布统计 -->
    <div class="col-md-12 mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3><?php echo number_format($publishStats['today']); ?></h3>
                        <p class="mb-0">今日发布</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3><?php echo number_format($publishStats['week']); ?></h3>
                        <p class="mb-0">本周发布</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3><?php echo number_format($publishStats['month']); ?></h3>
                        <p class="mb-0">本月发布</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h3><?php echo number_format($publishStats['total']); ?></h3>
                        <p class="mb-0">总计发布</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 发布接口配置 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-train me-2"></i>火车头发布接口
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>接口说明</h6>
                    <p class="mb-2">火车头采集器可以通过POST请求向以下接口发布内容：</p>
                    <code><?php echo $_SERVER['HTTP_HOST']; ?>/admin/train-publish.php</code>
                </div>
                
                <h6>请求参数</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>action</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>固定值：publish</td>
                            </tr>
                            <tr>
                                <td><code>publish_key</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>发布密钥</td>
                            </tr>
                            <tr>
                                <td><code>title</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>套图标题</td>
                            </tr>
                            <tr>
                                <td><code>content</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>套图描述</td>
                            </tr>
                            <tr>
                                <td><code>category_id</code></td>
                                <td>int</td>
                                <td>否</td>
                                <td>分类ID</td>
                            </tr>
                            <tr>
                                <td><code>tags</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>标签，逗号分隔</td>
                            </tr>
                            <tr>
                                <td><code>images[]</code></td>
                                <td>array</td>
                                <td>否</td>
                                <td>图片URL数组</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mt-4">分类ID对照表</h6>
                <div class="row">
                    <?php foreach ($categories as $category): ?>
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-info me-2"><?php echo $category['id']; ?></span>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <h6 class="mt-4">返回格式</h6>
                <pre class="bg-light p-3"><code>{
  "success": true,
  "album_id": 123
}

// 或错误时
{
  "error": "错误信息"
}</code></pre>
            </div>
        </div>
    </div>
    
    <!-- 最近发布 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>最近发布
                </h6>
            </div>
            <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($recentPublished)): ?>
                    <div class="text-center py-3">
                        <small class="text-muted">暂无发布记录</small>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recentPublished as $item): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars(mb_substr($item['title'], 0, 30)); ?></h6>
                                        <small class="text-muted"><?php echo date('m-d H:i', strtotime($item['created_at'])); ?></small>
                                    </div>
                                    <span class="badge bg-primary"><?php echo number_format($item['view_count']); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 发布设置 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-cog me-2"></i>发布设置
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="/admin/settings.php">
                    <input type="hidden" name="action" value="save_train_settings">
                    
                    <div class="mb-3">
                        <label class="form-label">发布密钥</label>
                        <div class="input-group">
                            <?php
                            $currentKey = $db->fetchColumn("SELECT value FROM {$db->getPrefix()}settings WHERE name = 'train_publish_key'") ?: 'your_secret_key';
                            ?>
                            <input type="text" class="form-control" name="train_publish_key"
                                   value="<?php echo htmlspecialchars($currentKey); ?>" readonly>
                            <button type="button" class="btn btn-outline-secondary" onclick="generateKey()">
                                <i class="fas fa-refresh"></i>
                            </button>
                        </div>
                        <small class="text-muted">用于验证火车头发布请求</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">默认分类</label>
                        <select class="form-select" name="train_default_category">
                            <option value="0">未分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="train_auto_publish" value="1" checked>
                            <label class="form-check-label">自动发布</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="train_auto_webp" value="1" checked>
                            <label class="form-check-label">自动转换WebP</label>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-save me-1"></i>保存设置
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 测试发布 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-vial me-2"></i>测试发布
                </h6>
            </div>
            <div class="card-body">
                <form id="testForm">
                    <div class="mb-3">
                        <label class="form-label">标题</label>
                        <input type="text" class="form-control" name="title" value="测试套图标题" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">分类</label>
                        <select class="form-select" name="category_id">
                            <option value="0">未分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>">
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">图片URL</label>
                        <textarea class="form-control" name="images" rows="3" placeholder="每行一个图片URL"></textarea>
                    </div>
                    
                    <div class="d-grid">
                        <button type="button" class="btn btn-success btn-sm" onclick="testPublish()">
                            <i class="fas fa-paper-plane me-1"></i>测试发布
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$pageScript = "
    function generateKey() {
        const key = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        document.querySelector('input[name=\"train_publish_key\"]').value = key;
    }
    
    function testPublish() {
        const form = document.getElementById('testForm');
        const formData = new FormData(form);
        formData.append('action', 'publish');
        formData.append('publish_key', document.querySelector('input[name=\"train_publish_key\"]').value);
        
        // 处理图片URL
        const imagesText = formData.get('images');
        if (imagesText) {
            const imageUrls = imagesText.split('\\n').filter(url => url.trim());
            formData.delete('images');
            imageUrls.forEach(url => {
                formData.append('images[]', url.trim());
            });
        }
        
        fetch('/admin/train-publish.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('测试发布成功！套图ID: ' + data.album_id);
                form.reset();
            } else {
                showError('发布失败: ' + data.error);
            }
        })
        .catch(error => {
            showError('网络错误: ' + error.message);
        });
    }
";

include __DIR__ . '/templates/footer.php';
?>
