<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '充值卡管理';
$currentPage = 'recharge-cards';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'generate':
            $count = intval($_POST['count']);
            $points = intval($_POST['points']);
            $cardType = $_POST['card_type'];
            $vipDays = intval($_POST['vip_days']);
            $userGroup = $_POST['user_group'];
            // 过期时间功能暂时移除
            
            if ($count > 0 && $count <= 1000) {
                $batchId = date('YmdHis') . rand(1000, 9999);
                $generated = 0;
                
                for ($i = 0; $i < $count; $i++) {
                    $cardNumber = generateCardNumber();
                    $cardPassword = generateCardPassword();
                    
                    // 对于VIP卡，根据天数自动选择合适的用户组
                    $defaultGroupId = null;
                    if ($cardType === 'vip') {
                        if ($userGroup) {
                            $defaultGroupId = intval($userGroup);
                        } else {
                            // 根据VIP天数自动选择用户组
                            if ($vipDays == 30) {
                                $defaultGroupId = 2; // 月费会员
                            } elseif ($vipDays == 365) {
                                $defaultGroupId = 3; // 年费会员
                            } elseif ($vipDays == 1095) {
                                $defaultGroupId = 5; // 三年会员
                            } elseif ($vipDays == -1 || $vipDays > 3650) {
                                $defaultGroupId = 4; // 终身会员
                            } else {
                                $defaultGroupId = 2; // 默认月费会员
                            }
                        }
                    } else {
                        $defaultGroupId = $userGroup ? intval($userGroup) : null;
                    }

                    $data = [
                        'card_number' => $cardNumber,
                        'card_password' => $cardPassword,
                        'points' => $points,
                        'type' => $cardType,
                        'group_days' => $vipDays,
                        'group_id' => $defaultGroupId,
                        'admin_id' => $_SESSION['admin_id'] ?? 1,
                        'batch_id' => $batchId,
                        'status' => 1
                    ];
                    
                    if ($db->insert('recharge_cards', $data)) {
                        $generated++;
                    }
                }
                
                $message = "成功生成 {$generated} 张充值卡，批次号：{$batchId}";
                $messageType = 'success';
            } else {
                $message = '生成数量必须在1-1000之间';
                $messageType = 'danger';
            }
            break;
            
        case 'delete':
            $cardId = intval($_POST['card_id']);
            if ($cardId > 0) {
                // 检查是否已使用
                $card = $db->fetch("SELECT used_at FROM {$db->getPrefix()}recharge_cards WHERE id = :id", ['id' => $cardId]);
                if ($card && !$card['used_at']) {
                    $result = $db->delete('recharge_cards', 'id = :id', ['id' => $cardId]);
                    if ($result) {
                        $message = '充值卡删除成功';
                        $messageType = 'success';
                    } else {
                        $message = '充值卡删除失败';
                        $messageType = 'danger';
                    }
                } else {
                    $message = '已使用的充值卡无法删除';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'batch_delete':
            $cardIds = $_POST['card_ids'] ?? [];
            if (!empty($cardIds)) {
                $placeholders = str_repeat('?,', count($cardIds) - 1) . '?';
                $result = $db->query("DELETE FROM {$db->getPrefix()}recharge_cards WHERE id IN ($placeholders) AND used_at IS NULL", $cardIds);
                if ($result) {
                    $message = '批量删除成功';
                    $messageType = 'success';
                } else {
                    $message = '批量删除失败';
                    $messageType = 'danger';
                }
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$cardType = $_GET['card_type'] ?? '';
$isUsed = $_GET['is_used'] ?? '';
$batchId = $_GET['batch_id'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = '1=1';
$params = [];

if ($search) {
    $where .= ' AND (card_number LIKE :search OR card_password LIKE :search)';
    $params['search'] = "%$search%";
}

if ($cardType) {
    $where .= ' AND type = :type';
    $params['type'] = $cardType;
}

if ($isUsed !== '') {
    if ($isUsed == '1') {
        $where .= ' AND used_at IS NOT NULL';
    } else {
        $where .= ' AND used_at IS NULL';
    }
}

if ($batchId) {
    $where .= ' AND batch_id = :batch_id';
    $params['batch_id'] = $batchId;
}

// 获取充值卡列表
$sql = "SELECT rc.*, u.username as used_by_username
        FROM {$db->getPrefix()}recharge_cards rc
        LEFT JOIN {$db->getPrefix()}users u ON rc.used_user_id = u.id
        WHERE $where
        ORDER BY rc.created_at DESC
        LIMIT $limit OFFSET $offset";

$cards = $db->fetchAll($sql, $params);

// 获取总数
$countSql = "SELECT COUNT(*) FROM {$db->getPrefix()}recharge_cards WHERE $where";
$total = $db->fetchColumn($countSql, $params);
$totalPages = ceil($total / $limit);

// 获取批次列表
$batches = $db->fetchAll("SELECT batch_id, COUNT(*) as count FROM {$db->getPrefix()}recharge_cards GROUP BY batch_id ORDER BY batch_id DESC LIMIT 20");

// 生成卡号和密码的函数
function generateCardNumber() {
    return strtoupper(substr(md5(uniqid(rand(), true)), 0, 16));
}

function generateCardPassword() {
    return strtoupper(substr(md5(uniqid(rand(), true)), 0, 12));
}

// 获取用户组列表
$userGroups = $db->fetchAll("SELECT * FROM {$db->getPrefix()}user_groups ORDER BY sort_order ASC");

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 生成充值卡 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>生成充值卡
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="generate">
                    
                    <div class="mb-3">
                        <label class="form-label">生成数量</label>
                        <input type="number" class="form-control" name="count" value="10" min="1" max="1000" required>
                        <small class="text-muted">最多一次生成1000张</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">卡片类型</label>
                        <select class="form-select" name="card_type" required onchange="toggleCardOptions()">
                            <option value="points">积分卡</option>
                            <option value="vip">会员卡</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="points-option">
                        <label class="form-label">积分数量</label>
                        <input type="number" class="form-control" name="points" value="100" min="0">
                    </div>
                    
                    <div class="mb-3" id="vip-days-option" style="display: none;">
                        <label class="form-label">VIP天数</label>
                        <input type="number" class="form-control" name="vip_days" value="30" min="1">
                    </div>
                    
                    <div class="mb-3" id="user-group-option" style="display: none;">
                        <label class="form-label">用户组</label>
                        <select class="form-select" name="user_group">
                            <option value="">选择用户组</option>
                            <?php foreach ($userGroups as $group): ?>
                                <option value="<?php echo htmlspecialchars($group['group_key']); ?>">
                                    <?php echo htmlspecialchars($group['group_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- 过期时间功能暂时移除 -->
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-magic me-2"></i>生成充值卡
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 批次统计 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">最近批次</h6>
            </div>
            <div class="card-body p-0">
                <?php if (empty($batches)): ?>
                    <div class="text-center py-3">
                        <small class="text-muted">暂无批次数据</small>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($batches as $batch): ?>
                            <a href="?batch_id=<?php echo htmlspecialchars($batch['batch_id']); ?>" 
                               class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><?php echo htmlspecialchars($batch['batch_id']); ?></span>
                                <span class="badge bg-primary"><?php echo $batch['count']; ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 充值卡列表 -->
    <div class="col-md-8">
        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="卡号/密码">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="card_type">
                            <option value="">全部类型</option>
                            <option value="points" <?php echo $cardType === 'points' ? 'selected' : ''; ?>>积分卡</option>
                            <option value="vip" <?php echo $cardType === 'vip' ? 'selected' : ''; ?>>会员卡</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="is_used">
                            <option value="">全部状态</option>
                            <option value="0" <?php echo $isUsed === '0' ? 'selected' : ''; ?>>未使用</option>
                            <option value="1" <?php echo $isUsed === '1' ? 'selected' : ''; ?>>已使用</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="batch_id" value="<?php echo htmlspecialchars($batchId); ?>" placeholder="批次号">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>充值卡列表 
                    <span class="badge bg-secondary"><?php echo number_format($total); ?></span>
                </h5>
                <div>
                    <button type="button" class="btn btn-danger btn-sm batch-action" disabled onclick="batchDelete()">
                        <i class="fas fa-trash me-1"></i>批量删除
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($cards)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无充值卡数据</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>卡号</th>
                                    <th>密码</th>
                                    <th>类型</th>
                                    <th>价值</th>
                                    <th>状态</th>
                                    <th>使用者</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cards as $card): ?>
                                    <tr>
                                        <td>
                                            <?php if (!$card['used_at']): ?>
                                                <input type="checkbox" class="form-check-input row-select" value="<?php echo $card['id']; ?>">
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($card['card_number']); ?></code>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($card['card_password']); ?></code>
                                        </td>
                                        <td>
                                            <?php if ($card['type'] === 'points'): ?>
                                                <span class="badge bg-info">积分卡</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark">会员卡</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($card['type'] === 'points'): ?>
                                                <?php echo number_format($card['points']); ?> 积分
                                            <?php else: ?>
                                                <?php echo $card['group_days']; ?> 天VIP
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($card['used_at']): ?>
                                                <span class="badge bg-success">已使用</span>
                                            <?php elseif ($card['status'] == 0): ?>
                                                <span class="badge bg-danger">已禁用</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">未使用</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($card['used_by_username']): ?>
                                                <?php echo htmlspecialchars($card['used_by_username']); ?>
                                                <br><small class="text-muted"><?php echo date('m-d H:i', strtotime($card['used_at'])); ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('Y-m-d H:i', strtotime($card['created_at'])); ?>
                                            <br><small class="text-muted">by Admin</small>
                                        </td>
                                        <td>
                                            <?php if (!$card['used_at']): ?>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteCard(<?php echo $card['id']; ?>)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 分页 -->
        <?php if ($totalPages > 1): ?>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&card_type=<?php echo $cardType; ?>&is_used=<?php echo $isUsed; ?>&batch_id=<?php echo $batchId; ?>">上一页</a>
                    </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&card_type=<?php echo $cardType; ?>&is_used=<?php echo $isUsed; ?>&batch_id=<?php echo $batchId; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&card_type=<?php echo $cardType; ?>&is_used=<?php echo $isUsed; ?>&batch_id=<?php echo $batchId; ?>">下一页</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>
</div>

<?php
$pageScript = "
    function toggleCardOptions() {
        const cardType = document.querySelector('select[name=\"card_type\"]').value;
        const pointsOption = document.getElementById('points-option');
        const vipDaysOption = document.getElementById('vip-days-option');
        const userGroupOption = document.getElementById('user-group-option');
        
        if (cardType === 'points') {
            pointsOption.style.display = 'block';
            vipDaysOption.style.display = 'none';
            userGroupOption.style.display = 'none';
        } else {
            pointsOption.style.display = 'none';
            vipDaysOption.style.display = 'block';
            userGroupOption.style.display = 'block';
        }
    }
    
    function deleteCard(cardId) {
        if (confirmDelete('确定要删除这张充值卡吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"card_id\" value=\"' + cardId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function batchDelete() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showWarning('请选择要删除的充值卡');
            return;
        }
        
        if (confirmBatchAction('删除', selectedIds.length)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"batch_delete\">';
            selectedIds.forEach(id => {
                form.innerHTML += '<input type=\"hidden\" name=\"card_ids[]\" value=\"' + id + '\">';
            });
            document.body.appendChild(form);
            form.submit();
        }
    }
";

include __DIR__ . '/templates/footer.php';
?>
