<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login.php');
    exit;
}

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/LocalizationQueueManager.php';

$db = Database::getInstance();
$queueManager = new LocalizationQueueManager();

// 处理操作
$message = '';
$messageType = '';

if ($_POST) {
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'add_album':
                $albumId = intval($_POST['album_id']);
                $priority = intval($_POST['priority'] ?? 5);
                $result = $queueManager->addAlbumToQueue($albumId, $priority);
                $message = $result['message'];
                $messageType = 'success';
                break;
                
            case 'add_batch':
                $albumIds = array_map('intval', explode(',', $_POST['album_ids']));
                $batchName = trim($_POST['batch_name']);
                $priority = intval($_POST['priority'] ?? 5);
                $result = $queueManager->addBatchToQueue($albumIds, $batchName, $priority);
                $message = $result['message'];
                $messageType = 'success';
                break;
                
            case 'reset_failed':
                $queueManager->resetFailedTasks();
                $message = '已重置所有失败任务';
                $messageType = 'success';
                break;
                
            case 'cleanup_workers':
                $queueManager->cleanupDeadWorkers();
                $message = '已清理僵死工作进程';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = '操作失败: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// 获取统计信息
$stats = $queueManager->getQueueStats();
$workers = $queueManager->getActiveWorkers();

// 获取最近的任务
$recentTasks = $db->fetchAll("
    SELECT q.*, a.title as album_title 
    FROM {$db->getPrefix()}localization_queue q
    LEFT JOIN {$db->getPrefix()}albums a ON q.album_id = a.id
    ORDER BY q.updated_at DESC 
    LIMIT 20
");

// 获取批次信息
$batches = $db->fetchAll("
    SELECT * FROM {$db->getPrefix()}localization_batches 
    ORDER BY created_at DESC 
    LIMIT 10
");

$pageTitle = '本地化队列管理';
include __DIR__ . '/templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">本地化队列管理</h3>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible">
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <!-- 统计信息 -->
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['total']; ?></h4>
                                    <p>总任务数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['pending']; ?></h4>
                                    <p>待处理</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['processing']; ?></h4>
                                    <p>处理中</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['completed']; ?></h4>
                                    <p>已完成</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['failed']; ?></h4>
                                    <p>失败</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card bg-secondary text-white">
                                <div class="card-body">
                                    <h4><?php echo $stats['retry']; ?></h4>
                                    <p>重试中</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAlbumModal">
                                <i class="fas fa-plus"></i> 添加套图到队列
                            </button>
                            <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addBatchModal">
                                <i class="fas fa-layer-group"></i> 批量添加
                            </button>
                            <form method="post" class="d-inline">
                                <input type="hidden" name="action" value="reset_failed">
                                <button type="submit" class="btn btn-warning" onclick="return confirm('确定要重置所有失败任务吗？')">
                                    <i class="fas fa-redo"></i> 重置失败任务
                                </button>
                            </form>
                            <form method="post" class="d-inline">
                                <input type="hidden" name="action" value="cleanup_workers">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-broom"></i> 清理僵死进程
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- 工作进程状态 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>活跃工作进程</h5>
                            <?php if (empty($workers)): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    没有活跃的工作进程。请启动工作进程：
                                    <code>php scripts/localization_worker.php</code>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>进程名</th>
                                                <th>状态</th>
                                                <th>已处理</th>
                                                <th>当前任务</th>
                                                <th>最后心跳</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($workers as $worker): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($worker['worker_name']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $worker['status'] === 'working' ? 'success' : 'secondary'; ?>">
                                                            <?php echo $worker['status']; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo $worker['processed_count']; ?></td>
                                                    <td><?php echo $worker['current_task_id'] ?: '-'; ?></td>
                                                    <td><?php echo $worker['last_heartbeat']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 最近任务 -->
                    <div class="row">
                        <div class="col-12">
                            <h5>最近任务</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>套图</th>
                                            <th>图片ID</th>
                                            <th>状态</th>
                                            <th>尝试次数</th>
                                            <th>优先级</th>
                                            <th>创建时间</th>
                                            <th>更新时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentTasks as $task): ?>
                                            <tr>
                                                <td><?php echo $task['id']; ?></td>
                                                <td><?php echo htmlspecialchars($task['album_title'] ?: '未知'); ?></td>
                                                <td><?php echo $task['image_id']; ?></td>
                                                <td>
                                                    <span class="badge bg-<?php 
                                                        echo match($task['status']) {
                                                            'pending' => 'warning',
                                                            'processing' => 'info',
                                                            'completed' => 'success',
                                                            'failed' => 'danger',
                                                            'retry' => 'secondary',
                                                            default => 'light'
                                                        };
                                                    ?>">
                                                        <?php echo $task['status']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $task['attempts']; ?>/<?php echo $task['max_attempts']; ?></td>
                                                <td><?php echo $task['priority']; ?></td>
                                                <td><?php echo $task['created_at']; ?></td>
                                                <td><?php echo $task['updated_at']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加套图模态框 -->
<div class="modal fade" id="addAlbumModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title">添加套图到队列</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_album">
                    <div class="mb-3">
                        <label class="form-label">套图ID</label>
                        <input type="number" name="album_id" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">优先级 (1-10，数字越小优先级越高)</label>
                        <input type="number" name="priority" class="form-control" value="5" min="1" max="10">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 批量添加模态框 -->
<div class="modal fade" id="addBatchModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title">批量添加套图</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_batch">
                    <div class="mb-3">
                        <label class="form-label">套图ID列表 (用逗号分隔)</label>
                        <textarea name="album_ids" class="form-control" rows="3" placeholder="1,2,3,4,5" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">批次名称</label>
                        <input type="text" name="batch_name" class="form-control" placeholder="留空自动生成">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">优先级 (1-10，数字越小优先级越高)</label>
                        <input type="number" name="priority" class="form-control" value="5" min="1" max="10">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include __DIR__ . '/templates/footer.php'; ?>
