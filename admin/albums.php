<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '套图管理';
$currentPage = 'albums';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create':
            $title = trim($_POST['title']);
            $description = trim($_POST['description'] ?? '');
            $categoryId = intval($_POST['category_id'] ?? 0);
            $tags = trim($_POST['tags'] ?? '');
            $isFree = intval($_POST['is_free'] ?? 0);
            $status = intval($_POST['status'] ?? 0);

            if (empty($title)) {
                $message = '套图标题不能为空';
                $messageType = 'danger';
            } else {
                try {
                    // 生成唯一的slug
                    $baseSlug = generateSlug($title);
                    $slug = $baseSlug;
                    $counter = 1;

                    // 检查slug是否已存在，如果存在则添加数字后缀
                    while ($db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE slug = :slug", ['slug' => $slug]) > 0) {
                        $slug = $baseSlug . '-' . $counter;
                        $counter++;
                    }

                    $data = [
                        'title' => $title,
                        'slug' => $slug,
                        'description' => $description,
                        'category_id' => $categoryId > 0 ? $categoryId : 1, // 确保category_id不为0
                        'tags' => $tags,
                        'is_free' => $isFree,
                        'status' => $status,
                        'view_count' => intval($_POST['view_count'] ?? 0),
                        'like_count' => 0,
                        'favorite_count' => 0,
                        'download_count' => 0,
                        'image_count' => 0,
                        'sort' => 0,
                        'admin_id' => $_SESSION['admin_id'] ?? null
                    ];

                    $albumId = $db->insert('albums', $data);
                    if ($albumId) {
                        // 处理图片数据
                        $success = processAlbumImages($albumId, $_POST, $_FILES);

                        $message = '套图创建成功';
                        if (!$success) {
                            $message .= '，但部分图片处理失败';
                        }
                        $messageType = 'success';

                        // 重定向到编辑页面
                        header("Location: /admin/albums.php?action=edit&id=$albumId");
                        exit;
                    } else {
                        $message = '套图创建失败，请检查数据库连接';
                        $messageType = 'danger';
                    }
                } catch (Exception $e) {
                    error_log('套图创建失败: ' . $e->getMessage());
                    $message = '套图创建失败: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            }
            break;

        case 'update':
            $id = intval($_POST['id']);
            $data = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'category_id' => intval($_POST['category_id']),
                'tags' => trim($_POST['tags']),
                'is_free' => intval($_POST['is_free'] ?? 0),
                'status' => intval($_POST['status'] ?? 0),
                'view_count' => intval($_POST['view_count'] ?? 0),
                'slug' => generateSlug($_POST['title'])
            ];

            if (empty($data['title'])) {
                $message = '套图标题不能为空';
                $messageType = 'danger';
            } else {
                $result = $db->update('albums', $data, 'id = :id', ['id' => $id]);
                if ($result) {
                    // 处理图片数据
                    $success = processAlbumImages($id, $_POST, $_FILES);

                    $message = '套图更新成功';
                    if (!$success) {
                        $message .= '，但部分图片处理失败';
                    }
                    $messageType = 'success';
                } else {
                    $message = '套图更新失败';
                    $messageType = 'danger';
                }
            }
            break;

        case 'delete':
            $albumId = intval($_POST['album_id']);
            if ($albumId > 0) {
                // 删除套图图片
                $db->delete('album_images', 'album_id = :album_id', ['album_id' => $albumId]);
                // 删除套图
                $result = $db->delete('albums', 'id = :id', ['id' => $albumId]);
                if ($result) {
                    $message = '套图删除成功';
                    $messageType = 'success';
                } else {
                    $message = '套图删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'batch_delete':
            $albumIds = $_POST['album_ids'] ?? [];
            if (!empty($albumIds)) {
                $placeholders = str_repeat('?,', count($albumIds) - 1) . '?';
                // 删除套图图片
                $db->query("DELETE FROM {$db->getPrefix()}album_images WHERE album_id IN ($placeholders)", $albumIds);
                // 删除套图
                $result = $db->query("DELETE FROM {$db->getPrefix()}albums WHERE id IN ($placeholders)", $albumIds);
                if ($result) {
                    $message = '批量删除成功，共删除 ' . count($albumIds) . ' 个套图';
                    $messageType = 'success';
                } else {
                    $message = '批量删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'update_status':
            $albumId = intval($_POST['album_id']);
            $status = intval($_POST['status']);
            if ($albumId > 0) {
                $result = $db->update('albums', ['status' => $status], 'id = :id', ['id' => $albumId]);
                if ($result) {
                    $message = '套图状态更新成功';
                    $messageType = 'success';
                } else {
                    $message = '套图状态更新失败';
                    $messageType = 'danger';
                }
            }
            break;

        case 'toggle_status':
            $albumId = intval($_POST['album_id']);
            $status = intval($_POST['status']);
            if ($albumId > 0) {
                $result = $db->update('albums', ['status' => $status], 'id = :id', ['id' => $albumId]);
                if ($result) {
                    $message = '套图状态更新成功';
                    $messageType = 'success';
                } else {
                    $message = '套图状态更新失败';
                    $messageType = 'danger';
                }
            }
            break;

        case 'batch_set_free':
            $albumIds = $_POST['album_ids'] ?? [];
            $isFree = intval($_POST['is_free']);
            if (!empty($albumIds)) {
                $placeholders = str_repeat('?,', count($albumIds) - 1) . '?';
                $result = $db->query("UPDATE {$db->getPrefix()}albums SET is_free = ? WHERE id IN ($placeholders)", array_merge([$isFree], $albumIds));
                if ($result) {
                    $message = '批量设置成功，共设置 ' . count($albumIds) . ' 个套图为' . ($isFree ? '免费' : '收费');
                    $messageType = 'success';
                } else {
                    $message = '批量设置失败';
                    $messageType = 'danger';
                }
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';
$isFree = $_GET['is_free'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = '1=1';
$params = [];

if ($search) {
    $where .= ' AND (a.title LIKE :search OR a.description LIKE :search OR a.tags LIKE :search)';
    $params['search'] = "%$search%";
}

if ($category) {
    $where .= ' AND a.category_id = :category';
    $params['category'] = $category;
}

if ($status !== '') {
    $where .= ' AND a.status = :status';
    $params['status'] = $status;
}

if ($isFree !== '') {
    $where .= ' AND a.is_free = :is_free';
    $params['is_free'] = $isFree;
}

// 获取套图列表
$sql = "SELECT a.*, c.name as category_name,
               (SELECT COUNT(*) FROM {$db->getPrefix()}album_images ai WHERE ai.album_id = a.id) as image_count,
               (SELECT COUNT(*) FROM {$db->getPrefix()}album_images ai WHERE ai.album_id = a.id AND ai.local_file_url IS NOT NULL) as localized_count
        FROM {$db->getPrefix()}albums a
        LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
        WHERE $where
        ORDER BY a.created_at DESC
        LIMIT $limit OFFSET $offset";

$albums = $db->fetchAll($sql, $params);

// 获取总数
$countSql = "SELECT COUNT(*) FROM {$db->getPrefix()}albums a WHERE $where";
$total = $db->fetchColumn($countSql, $params);
$totalPages = ceil($total / $limit);

// 生成slug函数
function generateSlug($title) {
    $slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));
    return substr($slug, 0, 100);
}

// 处理套图图片
function processAlbumImages($albumId, $postData, $files) {
    global $db;
    $success = true;



    try {
        // 处理上传的文件
        if (!empty($files['images']['name'][0])) {
            $uploadDir = __DIR__ . '/../uploads/albums/' . date('Y/m/');
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $sort = 1;
            foreach ($files['images']['name'] as $key => $filename) {
                if ($files['images']['error'][$key] === UPLOAD_ERR_OK) {
                    $extension = pathinfo($filename, PATHINFO_EXTENSION);
                    $newFilename = uniqid() . '.' . $extension;
                    $filepath = $uploadDir . $newFilename;

                    if (move_uploaded_file($files['images']['tmp_name'][$key], $filepath)) {
                        $imageUrl = '/uploads/albums/' . date('Y/m/') . $newFilename;
                        $db->insert('album_images', [
                            'album_id' => $albumId,
                            'filename' => $newFilename,
                            'original_name' => $filename,
                            'file_path' => $filepath,
                            'file_url' => $imageUrl,
                            'sort' => $sort++,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
            }
        }

        // 处理图片链接（简化版本）
        if (!empty($postData['image_urls_text'])) {
            // 清理和处理文本输入
            $urlsText = trim($postData['image_urls_text']);
            $imageUrls = array_filter(array_map('trim', explode("\n", $urlsText)));

            // 去重并验证URL
            $validUrls = [];
            foreach ($imageUrls as $url) {
                if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL) && !in_array($url, $validUrls)) {
                    $validUrls[] = $url;
                }
            }

            if (!empty($validUrls)) {
                // 如果是编辑模式，先删除现有图片
                if (isset($postData['action']) && $postData['action'] === 'update') {
                    $db->delete('album_images', 'album_id = :album_id', ['album_id' => $albumId]);
                }

                // 插入新的图片数据
                foreach ($validUrls as $index => $imageUrl) {
                    $filename = basename(parse_url($imageUrl, PHP_URL_PATH)) ?: 'image_' . ($index + 1);
                    $imageResult = $db->insert('album_images', [
                        'album_id' => $albumId,
                        'filename' => $filename,
                        'original_name' => $filename,
                        'file_path' => $imageUrl,
                        'file_url' => $imageUrl,
                        'file_size' => 0,
                        'width' => 0,
                        'height' => 0,
                        'format' => pathinfo($filename, PATHINFO_EXTENSION),
                        'is_webp' => 0,
                        'sort' => $index + 1
                    ]);

                    if (!$imageResult) {
                        error_log("插入图片失败: " . $imageUrl);
                        $success = false;
                    }
                }
            }
        }

        // 更新套图封面和图片数量
        $firstImage = $db->fetch("SELECT file_url FROM {$db->getPrefix()}album_images WHERE album_id = :album_id ORDER BY sort ASC LIMIT 1", ['album_id' => $albumId]);
        $imageCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}album_images WHERE album_id = :album_id", ['album_id' => $albumId]);

        $updateData = ['image_count' => $imageCount];
        if ($firstImage) {
            $updateData['cover'] = $firstImage['file_url'];
        }

        $updateResult = $db->update('albums', $updateData, 'id = :id', ['id' => $albumId]);
        if (!$updateResult) {
            error_log("更新套图封面和图片数量失败: albumId=" . $albumId);
            $success = false;
        }

    } catch (Exception $e) {
        error_log("处理套图图片失败: " . $e->getMessage());
        $success = false;
    }

    return $success;
}

// 获取分类列表
$categories = $db->fetchAll("SELECT * FROM {$db->getPrefix()}categories WHERE type = 'album' AND status = 1 ORDER BY sort ASC");

// 检查是否是添加/编辑页面
$action = $_GET['action'] ?? '';
$editAlbum = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $editId = intval($_GET['id']);
    $editAlbum = $db->fetch("SELECT * FROM {$db->getPrefix()}albums WHERE id = :id", ['id' => $editId]);
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($action === 'add' || $action === 'edit'): ?>
<!-- 添加/编辑套图表单 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-<?php echo $action === 'edit' ? 'edit' : 'plus'; ?> me-2"></i>
            <?php echo $action === 'edit' ? '编辑套图' : '添加套图'; ?>
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" value="<?php echo $action === 'edit' ? 'update' : 'create'; ?>">
            <?php if ($editAlbum): ?>
                <input type="hidden" name="id" value="<?php echo $editAlbum['id']; ?>">
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="mb-3">
                        <label class="form-label">套图标题 *</label>
                        <input type="text" class="form-control" name="title"
                               value="<?php echo htmlspecialchars($editAlbum['title'] ?? ''); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">套图描述</label>
                        <textarea class="form-control" name="description" rows="4"><?php echo htmlspecialchars($editAlbum['description'] ?? ''); ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">标签</label>
                        <input type="text" class="form-control" name="tags"
                               value="<?php echo htmlspecialchars($editAlbum['tags'] ?? ''); ?>"
                               placeholder="用逗号分隔多个标签">
                    </div>

                    <!-- 图片管理 -->
                    <div class="mb-3">
                        <label class="form-label">套图图片</label>

                        <!-- 图片添加标签页 -->
                        <ul class="nav nav-tabs mb-3" id="imageAddTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-pane" type="button" role="tab">
                                    <i class="fas fa-upload me-2"></i>上传本地图片
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="batch-tab" data-bs-toggle="tab" data-bs-target="#batch-pane" type="button" role="tab">
                                    <i class="fas fa-link me-2"></i>添加图片链接
                                </button>
                            </li>
                            <?php if ($action === 'edit' && $editAlbum): ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="local-tab" data-bs-toggle="tab" data-bs-target="#local-pane" type="button" role="tab">
                                    <i class="fas fa-server me-2"></i>本地化图片
                                </button>
                            </li>
                            <?php endif; ?>
                        </ul>

                        <div class="tab-content" id="imageAddTabContent">
                            <!-- 上传图片 -->
                            <div class="tab-pane fade show active" id="upload-pane" role="tabpanel">
                                <input type="file" class="form-control mb-2" name="images[]" multiple accept="image/*">
                                <div class="form-text">支持批量选择本地图片文件，格式：JPG、PNG、GIF、WebP</div>
                            </div>

                            <!-- 批量添加图片链接 -->
                            <div class="tab-pane fade" id="batch-pane" role="tabpanel">
                                <textarea class="form-control mb-3" name="image_urls_text" id="imageUrlsText" rows="8"
                                          placeholder="请粘贴图片链接，每行一个：&#10;https://oss-img-mmxxdd.ojbkcdn.cc/img/80949/001.jpg&#10;https://oss-img-mmxxdd.ojbkcdn.cc/img/80949/002.jpg&#10;https://oss-img-mmxxdd.ojbkcdn.cc/img/80949/003.jpg&#10;&#10;程序会自动处理格式、去重和验证"><?php
                                    // 如果是编辑模式，显示现有图片URL
                                    if ($action === 'edit' && $editAlbum) {
                                        $existingImages = $db->fetchAll("SELECT file_url FROM {$db->getPrefix()}album_images WHERE album_id = :album_id ORDER BY sort ASC", ['album_id' => $editAlbum['id']]);
                                        foreach ($existingImages as $img) {
                                            echo htmlspecialchars($img['file_url']) . "\n";
                                        }
                                    }
                                ?></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    直接粘贴图片链接即可，每行一个。程序会自动去重、验证URL格式，并按顺序排列。
                                    <?php if ($action === 'edit'): ?>
                                    <br><strong>编辑模式：</strong>上方已显示现有图片链接，您可以修改、删除或添加新的链接。
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if ($action === 'edit' && $editAlbum): ?>
                            <!-- 本地化图片管理 -->
                            <div class="tab-pane fade" id="local-pane" role="tabpanel">
                                <?php
                                // 获取套图的图片信息
                                $albumImages = $db->fetchAll("
                                    SELECT id, file_url, local_file_url, sort
                                    FROM {$db->getPrefix()}album_images
                                    WHERE album_id = :album_id
                                    ORDER BY sort ASC, id ASC
                                ", ['album_id' => $editAlbum['id']]);

                                $localizedCount = 0;
                                foreach ($albumImages as $img) {
                                    if (!empty($img['local_file_url'])) {
                                        $localizedCount++;
                                    }
                                }
                                ?>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>本地化状态</h6>
                                    <p class="mb-2">
                                        总图片数：<strong><?php echo count($albumImages); ?></strong> 张，
                                        已本地化：<strong><?php echo $localizedCount; ?></strong> 张
                                    </p>
                                    <?php if ($localizedCount < count($albumImages)): ?>
                                    <button type="button" class="btn btn-sm btn-success" onclick="localizeCurrentAlbum()">
                                        <i class="fas fa-download me-1"></i>一键本地化当前套图
                                    </button>
                                    <?php else: ?>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>已完全本地化
                                    </span>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($albumImages)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th width="60">序号</th>
                                                <th>盗链图片</th>
                                                <th>本地化图片</th>
                                                <th width="100">状态</th>
                                                <th width="120">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($albumImages as $index => $image): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo htmlspecialchars($image['file_url']); ?>"
                                                             class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;"
                                                             onerror="this.src='/admin/assets/img/no-image.png'">
                                                        <small class="text-muted text-truncate" style="max-width: 300px;">
                                                            <?php echo htmlspecialchars($image['file_url']); ?>
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if (!empty($image['local_file_url'])): ?>
                                                    <div class="d-flex align-items-center">
                                                        <?php
                                                        // 获取CDN地址
                                                        $cdnConfig = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'image_cdn_url'");
                                                        $cdnUrl = $cdnConfig['value'] ?? 'https://cdn.example.com/';
                                                        if (substr($cdnUrl, -1) !== '/') $cdnUrl .= '/';
                                                        $fullLocalUrl = $cdnUrl . ltrim($image['local_file_url'], '/');
                                                        ?>
                                                        <img src="<?php echo htmlspecialchars($fullLocalUrl); ?>"
                                                             class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;"
                                                             onerror="this.src='/admin/assets/img/no-image.png'">
                                                        <small class="text-muted text-truncate" style="max-width: 300px;">
                                                            <?php echo htmlspecialchars($image['local_file_url']); ?>
                                                        </small>
                                                    </div>
                                                    <?php else: ?>
                                                    <span class="text-muted">未本地化</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($image['local_file_url'])): ?>
                                                    <span class="badge bg-success">已本地化</span>
                                                    <?php else: ?>
                                                    <span class="badge bg-secondary">未本地化</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($image['local_file_url'])): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteLocalImage(<?php echo $image['id']; ?>)"
                                                            title="删除本地化图片">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline-success"
                                                            onclick="localizeImage(<?php echo $image['id']; ?>)"
                                                            title="本地化此图片">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted text-center py-3">此套图暂无图片</p>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">分类</label>
                        <select class="form-select" name="category_id">
                            <option value="0">未分类</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"
                                        <?php echo ($editAlbum['category_id'] ?? 0) == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="is_free" value="1"
                                   <?php echo ($editAlbum['is_free'] ?? 0) ? 'checked' : ''; ?>>
                            <label class="form-check-label">免费套图</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">浏览量</label>
                        <input type="number" class="form-control" name="view_count" min="0"
                               value="<?php echo htmlspecialchars($editAlbum['view_count'] ?? '0'); ?>"
                               placeholder="浏览量">
                        <small class="text-muted">套图浏览次数</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="status" value="1"
                                   <?php echo ($editAlbum['status'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label">立即发布</label>
                        </div>
                    </div>




                </div>
            </div>

            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i><?php echo $action === 'edit' ? '更新套图' : '创建套图'; ?>
                </button>
                <a href="/admin/albums.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回列表
                </a>
            </div>
        </form>
    </div>
</div>


<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">搜索套图</label>
                <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="标题/描述/标签">
            </div>
            <div class="col-md-2">
                <label class="form-label">分类</label>
                <select class="form-select" name="category">
                    <option value="">全部分类</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($cat['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>已发布</option>
                    <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>未发布</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">收费状态</label>
                <select class="form-select" name="is_free">
                    <option value="">全部</option>
                    <option value="1" <?php echo $isFree === '1' ? 'selected' : ''; ?>>免费</option>
                    <option value="0" <?php echo $isFree === '0' ? 'selected' : ''; ?>>收费</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="/admin/albums.php?action=add" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加套图
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 套图列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-images me-2"></i>套图列表 
            <span class="badge bg-secondary"><?php echo number_format($total); ?></span>
        </h5>
        <div class="btn-group">
            <button type="button" class="btn btn-info btn-sm" onclick="batchLocalizeAll()">
                <i class="fas fa-download me-1"></i>一键本地化所有盗链图片
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="cleanupTasks()">
                <i class="fas fa-broom me-1"></i>清理未完成任务
            </button>
            <button type="button" class="btn btn-success btn-sm batch-action" disabled onclick="batchSetFree(1)">
                <i class="fas fa-unlock me-1"></i>批量设为免费
            </button>
            <button type="button" class="btn btn-warning btn-sm batch-action" disabled onclick="batchSetFree(0)">
                <i class="fas fa-lock me-1"></i>批量设为收费
            </button>
            <button type="button" class="btn btn-danger btn-sm batch-action" disabled onclick="batchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($albums)): ?>
            <div class="text-center py-5">
                <i class="fas fa-images fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无套图数据</p>
                <a href="/admin/albums.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加第一个套图
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>套图信息</th>
                            <th>分类</th>
                            <th>图片数</th>
                            <th>本地化</th>
                            <th>浏览量</th>
                            <th>收费状态</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th width="180">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($albums as $album): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?php echo $album['id']; ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if ($album['cover']): ?>
                                            <img src="<?php echo htmlspecialchars($album['cover']); ?>"
                                                 class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($album['title']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars(mb_substr($album['description'], 0, 50)); ?>...</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($album['category_name']): ?>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($album['category_name']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?php echo number_format($album['image_count']); ?></span>
                                </td>
                                <td>
                                    <?php
                                    $localizedCount = intval($album['localized_count']);
                                    $totalCount = intval($album['image_count']);
                                    $localizationStatus = $album['localization_status'] ?? 'none';

                                    if ($localizedCount == 0): ?>
                                        <span class="badge bg-secondary">未本地化</span>
                                    <?php elseif ($localizedCount == $totalCount): ?>
                                        <span class="badge bg-success">已完成</span>
                                        <small class="d-block text-muted"><?php echo $localizedCount; ?>/<?php echo $totalCount; ?></small>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">部分完成</span>
                                        <small class="d-block text-muted"><?php echo $localizedCount; ?>/<?php echo $totalCount; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($album['view_count']); ?></td>
                                <td>
                                    <?php if ($album['is_free']): ?>
                                        <span class="badge bg-success">免费</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">收费</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($album['status']): ?>
                                        <span class="badge bg-success">已发布</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">未发布</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($album['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/admin/albums.php?action=edit&id=<?php echo $album['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/album/<?php echo $album['id']; ?>" target="_blank" class="btn btn-outline-info" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($localizedCount < $totalCount): ?>
                                        <button type="button" class="btn btn-outline-success"
                                                onclick="localizeAlbum(<?php echo $album['id']; ?>)"
                                                title="本地化图片">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-<?php echo $album['status'] ? 'warning' : 'success'; ?>"
                                                onclick="toggleStatus(<?php echo $album['id']; ?>, <?php echo $album['status'] ? 0 : 1; ?>)"
                                                title="<?php echo $album['status'] ? '下架' : '发布'; ?>">
                                            <i class="fas fa-<?php echo $album['status'] ? 'eye-slash' : 'eye'; ?>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteAlbum(<?php echo $album['id']; ?>)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 分页 -->
<?php if ($totalPages > 1): ?>
<nav class="mt-4">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>">上一页</a>
            </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
        
        <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>">下一页</a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<?php
// 移除原来的$pageScript变量，使用直接的JavaScript代码









include __DIR__ . '/templates/footer.php';
?>

<!-- 调试：直接输出JavaScript -->
<script>
console.log('JavaScript代码开始加载...');

// 表单验证和图片计数
document.addEventListener('DOMContentLoaded', function() {
    const imageUrlsText = document.getElementById('imageUrlsText');
    if (imageUrlsText) {
        imageUrlsText.addEventListener('input', updateImageCount);
        updateImageCount();
    }

    // 表单验证
    const submitForm = document.querySelector('form[method="POST"]');
    if (submitForm) {
        submitForm.addEventListener('submit', function(e) {
            const textarea = document.getElementById('imageUrlsText');
            const fileInput = document.querySelector('input[name="images[]"]');

            const hasTextUrls = textarea && textarea.value.trim();
            const hasFiles = fileInput && fileInput.files.length > 0;

            if (!hasTextUrls && !hasFiles) {
                if (!confirm('您还没有添加任何图片，确定要继续吗？')) {
                    e.preventDefault();
                }
            }
        });
    }
});

function updateImageCount() {
    const textarea = document.getElementById('imageUrlsText');
    if (!textarea) return;

    const text = textarea.value.trim();
    const lines = text ? text.split('\n').filter(line => {
        const url = line.trim();
        return url && (url.startsWith('http://') || url.startsWith('https://'));
    }) : [];

    showImageCount(lines.length);
}

function showImageCount(count) {
    let countElement = document.getElementById('imageCountDisplay');
    if (!countElement) {
        countElement = document.createElement('div');
        countElement.id = 'imageCountDisplay';
        countElement.className = 'mt-2 text-info';
        const textarea = document.getElementById('imageUrlsText');
        if (textarea && textarea.parentNode) {
            textarea.parentNode.insertBefore(countElement, textarea.nextSibling);
        }
    }

    if (count > 0) {
        countElement.innerHTML = '<i class="fas fa-images me-1"></i>检测到 <strong>' + count + '</strong> 张有效图片';
        countElement.style.display = 'block';
    } else {
        countElement.style.display = 'none';
    }
}

// 套图管理函数
function deleteAlbum(albumId) {
    if (!confirm('确定要删除这个套图吗？删除后无法恢复！')) return;

    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    form.innerHTML = '<input type="hidden" name="action" value="delete">' +
                     '<input type="hidden" name="album_id" value="' + albumId + '">';

    document.body.appendChild(form);
    form.submit();
}

function toggleStatus(albumId, newStatus) {
    const statusText = newStatus ? '发布' : '下架';
    if (!confirm('确定要' + statusText + '这个套图吗？')) return;

    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    form.innerHTML = '<input type="hidden" name="action" value="toggle_status">' +
                     '<input type="hidden" name="album_id" value="' + albumId + '">' +
                     '<input type="hidden" name="status" value="' + newStatus + '">';

    document.body.appendChild(form);
    form.submit();
}

function batchDelete() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        alert('请先选择要删除的套图');
        return;
    }

    if (!confirm('确定要删除 ' + selectedIds.length + ' 个套图吗？')) return;

    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    let html = '<input type="hidden" name="action" value="batch_delete">';
    selectedIds.forEach(id => {
        html += '<input type="hidden" name="album_ids[]" value="' + id + '">';
    });
    form.innerHTML = html;

    document.body.appendChild(form);
    form.submit();
}

function batchSetFree(isFree) {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        alert('请先选择要操作的套图');
        return;
    }

    const actionText = isFree ? '设为免费' : '设为收费';
    if (!confirm('确定要' + actionText + ' ' + selectedIds.length + ' 个套图吗？')) return;

    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    let html = '<input type="hidden" name="action" value="batch_set_free">' +
               '<input type="hidden" name="is_free" value="' + isFree + '">';
    selectedIds.forEach(id => {
        html += '<input type="hidden" name="album_ids[]" value="' + id + '">';
    });
    form.innerHTML = html;

    document.body.appendChild(form);
    form.submit();
}

// 清理未完成的本地化任务
function cleanupTasks() {
    if (!confirm('确定要清理所有未完成的本地化任务吗？')) {
        return;
    }

    fetch('/admin/api/cleanup-tasks.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
        return response.text();
    })
    .then(text => {
        console.log('Cleanup API Response:', text);

        if (!text.trim()) {
            throw new Error('服务器返回空响应');
        }

        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('清理失败: ' + data.error);
            }
        } catch (e) {
            alert('响应解析失败，原始响应：\n' + text);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}

// 批量本地化所有套图
function batchLocalizeAll() {
    if (!confirm('确定要本地化所有未本地化的套图吗？这可能需要很长时间，请耐心等待。')) {
        return;
    }

    // 显示批量进度模态框
    showBatchLocalizationModal();

    // 使用fetch接收实时进度
    fetch('/admin/api/batch-localize.php', {
        method: 'POST'
    })
    .then(response => {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        function readStream() {
            return reader.read().then(({ done, value }) => {
                if (done) {
                    hideBatchLocalizationModal();
                    alert('批量本地化完成！');
                    location.reload();
                    return;
                }

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                lines.forEach(line => {
                    if (line.trim()) {
                        try {
                            const data = JSON.parse(line);
                            updateBatchProgress(data);
                        } catch (e) {
                            console.error('解析进度数据失败:', e);
                        }
                    }
                });

                return readStream();
            });
        }

        return readStream();
    })
    .catch(error => {
        hideBatchLocalizationModal();
        alert('批量本地化失败: ' + error.message);
    });
}

// 本地化单个套图
function localizeAlbum(albumId, useQueue = false) {
    console.log('localizeAlbum函数被调用，albumId:', albumId, 'useQueue:', useQueue);

    if (useQueue) {
        if (!confirm('确定要将这个套图添加到本地化队列吗？')) {
            return;
        }

        // 队列模式 - 简单的AJAX请求
        fetch('/admin/api/localize-album.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'album_id=' + albumId + '&use_queue=1'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('成功添加到队列：' + data.message);
                location.reload();
            } else {
                alert('添加失败：' + data.error);
            }
        })
        .catch(error => {
            alert('请求失败：' + error.message);
        });

        return;
    }

    if (!confirm('确定要立即本地化这个套图的所有图片吗？这可能需要一些时间。')) {
        return;
    }

    // 显示进度模态框
    showLocalizationProgressModal();

    // 使用XMLHttpRequest进行流式请求
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '/admin/api/localize-album.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    // 只设置安全的头部
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

    let processedLength = 0;
    let isCompleted = false;

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 3 || xhr.readyState === 4) { // LOADING or DONE
            const newData = xhr.responseText.substring(processedLength);
            processedLength = xhr.responseText.length;

            if (newData) {
                // 解析 "data: {json}\n\n" 格式的数据
                const dataBlocks = newData.split('\n\n');
                dataBlocks.forEach(block => {
                    if (block.trim() && block.startsWith('data: ')) {
                        const jsonStr = block.substring(6); // 移除 "data: " 前缀
                        try {
                            const data = JSON.parse(jsonStr);
                            console.log('收到进度数据:', data);

                            // 跳过初始化消息
                            if (data.type === 'init') {
                                return;
                            }

                            // 检查是否是最终结果
                            if (data.success !== undefined) {
                                isCompleted = true;
                                hideLocalizationProgressModal();
                                if (data.success) {
                                    alert('本地化完成！成功: ' + (data.success_count || 0) + ', 失败: ' + (data.failed_count || 0));
                                } else {
                                    alert('本地化失败: ' + data.error);
                                }
                                location.reload();
                                return;
                            }

                            updateLocalizationProgress(data);
                        } catch (e) {
                            console.log('解析进度数据失败:', jsonStr);
                        }
                    }
                });
            }

            // 如果请求完成但没有收到最终结果
            if (xhr.readyState === 4 && !isCompleted) {
                if (xhr.status === 200) {
                    hideLocalizationProgressModal();
                    alert('本地化完成！');
                    location.reload();
                } else {
                    hideLocalizationProgressModal();
                    alert('请求失败: HTTP ' + xhr.status);
                }
            }
        }
    };

    xhr.onerror = function() {
        console.error('流式请求网络错误');
        hideLocalizationProgressModal();
        alert('网络连接错误，请检查网络后重试');
    };

    xhr.ontimeout = function() {
        console.error('流式请求超时');
        hideLocalizationProgressModal();
        alert('请求超时，请重试');
    };

    // 设置超时时间为15分钟
    xhr.timeout = 15 * 60 * 1000;

    xhr.send('album_id=' + albumId + '&stream=1');

}

// 本地化当前套图
function localizeCurrentAlbum() {
    console.log('localizeCurrentAlbum函数被调用');
    const albumId = <?php echo ($editAlbum['id'] ?? 0); ?>;
    if (albumId <= 0) return;

    localizeAlbum(albumId);
}

// 本地化单张图片
function localizeImage(imageId) {
    console.log('localizeImage函数被调用，imageId:', imageId);
    if (!confirm('确定要本地化这张图片吗？')) {
        return;
    }

    fetch('/admin/api/localize-image.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'image_id=' + imageId
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
        return response.text();
    })
    .then(text => {
        if (!text.trim()) {
            throw new Error('服务器返回空响应');
        }

        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('图片本地化成功');
                location.reload();
            } else {
                alert('本地化失败: ' + data.error);
            }
        } catch (e) {
            alert('响应解析失败，原始响应：\n' + text);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}

// 删除本地化图片
function deleteLocalImage(imageId) {
    console.log('deleteLocalImage函数被调用，imageId:', imageId);
    if (!confirm('确定要删除这张图片的本地化版本吗？删除后将使用盗链图片。')) {
        return;
    }

    fetch('/admin/api/delete-local-image.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'image_id=' + imageId
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
        return response.text();
    })
    .then(text => {
        if (!text.trim()) {
            throw new Error('服务器返回空响应');
        }

        try {
            const data = JSON.parse(text);
            if (data.success) {
                alert('本地化图片删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.error);
            }
        } catch (e) {
            alert('响应解析失败，原始响应：\n' + text);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error.message);
    });
}

// 模态框函数
function showLocalizationModal() {
    console.log('显示本地化模态框');
    const modal = document.createElement('div');
    modal.id = 'localizationModal';
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.innerHTML = '<div class="modal-dialog modal-dialog-centered">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title">正在本地化图片</h5>' +
        '</div>' +
        '<div class="modal-body text-center">' +
        '<div class="spinner-border text-primary mb-3" role="status">' +
        '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '<p>正在下载和上传图片，请稍候...</p>' +
        '</div>' +
        '</div>' +
        '</div>';
    document.body.appendChild(modal);
}

function hideLocalizationModal() {
    console.log('隐藏本地化模态框');
    const modal = document.getElementById('localizationModal');
    if (modal) {
        modal.remove();
    }
}

// 带进度条的本地化模态框
function showLocalizationProgressModal() {
    console.log('显示本地化进度模态框');
    const modal = document.createElement('div');
    modal.id = 'localizationProgressModal';
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.innerHTML = '<div class="modal-dialog modal-lg modal-dialog-centered">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title">正在本地化图片</h5>' +
        '</div>' +
        '<div class="modal-body">' +
        '<div class="mb-3">' +
        '<div class="d-flex justify-content-between mb-2">' +
        '<span id="progressText">准备开始...</span>' +
        '<span id="progressCount">0/0</span>' +
        '</div>' +
        '<div class="progress mb-3">' +
        '<div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" ' +
        'role="progressbar" style="width: 0%"></div>' +
        '</div>' +
        '</div>' +
        '<div id="progressLog" class="border rounded p-2" style="height: 200px; overflow-y: auto; font-size: 0.9em; background-color: #f8f9fa;">' +
        '<div class="text-muted">等待开始...</div>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';
    document.body.appendChild(modal);
}

function hideLocalizationProgressModal() {
    console.log('隐藏本地化进度模态框');
    const modal = document.getElementById('localizationProgressModal');
    if (modal) {
        modal.remove();
    }
}

function updateLocalizationProgress(data) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const progressCount = document.getElementById('progressCount');
    const progressLog = document.getElementById('progressLog');

    if (!progressBar || !progressText || !progressCount || !progressLog) {
        return;
    }

    if (data.type === 'start') {
        progressText.textContent = data.message || '开始处理...';
        progressCount.textContent = data.current + '/' + data.total;

        // 清空日志并添加开始信息
        progressLog.innerHTML = '';
        const logEntry = document.createElement('div');
        logEntry.className = 'text-info';
        logEntry.innerHTML = '<small>' + new Date().toLocaleTimeString() + '</small> ' + data.message;
        progressLog.appendChild(logEntry);

    } else if (data.type === 'processing') {
        progressText.textContent = data.message || '正在处理...';
        progressCount.textContent = data.current + '/' + data.total;

        // 添加处理日志
        const logEntry = document.createElement('div');
        logEntry.className = 'text-primary';
        logEntry.innerHTML = '<small>' + new Date().toLocaleTimeString() + '</small> ' + data.message;
        progressLog.appendChild(logEntry);
        progressLog.scrollTop = progressLog.scrollHeight;

    } else if (data.type === 'progress') {
        const percent = Math.round((data.current / data.total) * 100);

        progressBar.style.width = percent + '%';
        progressBar.textContent = percent + '%';

        progressText.textContent = data.message || '正在处理...';
        progressCount.textContent = data.current + '/' + data.total;

        // 添加日志
        const logEntry = document.createElement('div');
        logEntry.className = data.error ? 'text-danger' : 'text-success';
        logEntry.innerHTML = '<small>' + new Date().toLocaleTimeString() + '</small> ' + data.message;
        progressLog.appendChild(logEntry);

        // 自动滚动到底部
        progressLog.scrollTop = progressLog.scrollHeight;

        // 如果启用了WebP转换，显示相关信息
        if (data.file_path && data.file_path.includes('.webp')) {
            const webpInfo = document.createElement('div');
            webpInfo.className = 'text-info';
            webpInfo.innerHTML = '<small><i class="fas fa-compress-alt"></i> 已转换为WebP格式</small>';
            progressLog.appendChild(webpInfo);
        }
    } else if (data.success !== undefined) {
        // 最终结果
        progressText.textContent = data.success ? '本地化完成！' : '本地化失败';
        if (data.success) {
            progressBar.className = 'progress-bar bg-success';
            progressBar.style.width = '100%';
            progressBar.textContent = '完成';
        }
    }
}

function showBatchLocalizationModal() {
    const modal = document.createElement('div');
    modal.id = 'batchLocalizationModal';
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.innerHTML = '<div class="modal-dialog modal-lg modal-dialog-centered">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h5 class="modal-title">批量本地化进度</h5>' +
        '</div>' +
        '<div class="modal-body">' +
        '<div id="batchProgress">' +
        '<div class="text-center mb-3">' +
        '<div class="spinner-border text-primary" role="status">' +
        '<span class="visually-hidden">Loading...</span>' +
        '</div>' +
        '</div>' +
        '<p class="text-center">正在准备批量本地化...</p>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';
    document.body.appendChild(modal);
}

function hideBatchLocalizationModal() {
    const modal = document.getElementById('batchLocalizationModal');
    if (modal) {
        modal.remove();
    }
}

function updateBatchProgress(data) {
    const progressDiv = document.getElementById('batchProgress');
    if (!progressDiv) return;

    if (data.type === 'progress') {
        const percent = Math.round((data.current / data.total) * 100);
        progressDiv.innerHTML = '<h6>正在处理套图 [' + data.current + '/' + data.total + ']: ' + data.album_title + '</h6>' +
            '<div class="progress mb-3">' +
            '<div class="progress-bar" style="width: ' + percent + '%">' + percent + '%</div>' +
            '</div>' +
            '<div class="alert alert-' + (data.result.success ? 'success' : 'warning') + '">' +
            data.result.message +
            '</div>';
    } else if (data.type === 'error') {
        progressDiv.innerHTML += '<div class="alert alert-danger">' +
            '套图 ' + data.album_title + ' 处理失败: ' + data.error +
            '</div>';
    } else if (data.type === 'complete') {
        progressDiv.innerHTML += '<div class="alert alert-success">' +
            '批量本地化完成！成功: ' + data.success + '，失败: ' + data.failed +
            '</div>';
    }
}

console.log('JavaScript代码加载完成');
</script>
