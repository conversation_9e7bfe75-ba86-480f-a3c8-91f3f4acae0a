<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '用户管理';
$currentPage = 'users';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create':
            $data = [
                'username' => trim($_POST['username']),
                'email' => trim($_POST['email']),
                'password' => password_hash($_POST['password'], PASSWORD_DEFAULT),
                'salt' => substr(md5(uniqid()), 0, 32),
                'nickname' => trim($_POST['nickname']),
                'group_id' => intval($_POST['group_id']),
                'points' => intval($_POST['points']),
                'status' => isset($_POST['status']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];

            if (empty($data['username']) || empty($data['email']) || empty($_POST['password'])) {
                $message = '用户名、邮箱和密码不能为空';
                $messageType = 'danger';
            } else {
                // 检查用户名和邮箱是否已存在
                $exists = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE username = :username OR email = :email", [
                    'username' => $data['username'],
                    'email' => $data['email']
                ]);

                if ($exists) {
                    $message = '用户名或邮箱已存在';
                    $messageType = 'danger';
                } else {
                    $result = $db->insert('users', $data);
                    if ($result) {
                        $message = '用户创建成功';
                        $messageType = 'success';
                    } else {
                        $message = '用户创建失败';
                        $messageType = 'danger';
                    }
                }
            }
            break;

        case 'delete':
            $userId = intval($_POST['user_id']);
            if ($userId > 0) {
                $result = $db->delete('users', 'id = :id', ['id' => $userId]);
                if ($result) {
                    $message = '用户删除成功';
                    $messageType = 'success';
                } else {
                    $message = '用户删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'batch_delete':
            $userIds = $_POST['user_ids'] ?? [];
            if (!empty($userIds)) {
                $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
                $sql = "DELETE FROM {$db->getPrefix()}users WHERE id IN ($placeholders)";
                $result = $db->query($sql, $userIds);
                if ($result) {
                    $message = '批量删除成功，共删除 ' . count($userIds) . ' 个用户';
                    $messageType = 'success';
                } else {
                    $message = '批量删除失败';
                    $messageType = 'danger';
                }
            }
            break;

        case 'edit':
            $id = intval($_POST['id']);
            $data = [
                'username' => trim($_POST['username']),
                'email' => trim($_POST['email']),
                'nickname' => trim($_POST['nickname']),
                'group_id' => intval($_POST['group_id']),
                'points' => intval($_POST['points']),
                'status' => isset($_POST['status']) ? 1 : 0
            ];

            // 如果提供了新密码，则更新密码
            if (!empty($_POST['password'])) {
                $data['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
            }

            if (empty($data['username']) || empty($data['email'])) {
                $message = '用户名和邮箱不能为空';
                $messageType = 'danger';
            } else {
                try {
                    // 检查用户名和邮箱是否被其他用户使用
                    $exists = $db->fetchColumn(
                        "SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE (username = :username OR email = :email) AND id != :id",
                        [
                            'username' => $data['username'],
                            'email' => $data['email'],
                            'id' => $id
                        ]
                    );

                    if ($exists) {
                        $message = '用户名或邮箱已被其他用户使用';
                        $messageType = 'danger';
                    } else {
                        $result = $db->update('users', $data, 'id = :id', ['id' => $id]);
                        if ($result !== false) {
                            $message = '用户更新成功';
                            $messageType = 'success';
                        } else {
                            $message = '用户更新失败';
                            $messageType = 'danger';
                        }
                    }
                } catch (Exception $e) {
                    $message = '用户更新失败：' . $e->getMessage();
                    $messageType = 'danger';
                }
            }
            break;

        case 'update_status':
            $userId = intval($_POST['user_id']);
            $status = intval($_POST['status']);
            if ($userId > 0) {
                $result = $db->update('users', ['status' => $status], 'id = :id', ['id' => $userId]);
                if ($result) {
                    $message = '用户状态更新成功';
                    $messageType = 'success';
                } else {
                    $message = '用户状态更新失败';
                    $messageType = 'danger';
                }
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$userGroup = $_GET['user_group'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = '1=1';
$params = [];

if ($search) {
    $where .= ' AND (username LIKE :search OR email LIKE :search OR nickname LIKE :search)';
    $params['search'] = "%$search%";
}

if ($status !== '') {
    $where .= ' AND status = :status';
    $params['status'] = $status;
}

if ($userGroup) {
    $where .= ' AND u.group_id = :group_id';
    $params['group_id'] = intval($userGroup);
}

// 获取用户列表
$sql = "SELECT u.*, ug.group_name
        FROM {$db->getPrefix()}users u
        LEFT JOIN {$db->getPrefix()}user_groups ug ON u.group_id = ug.id
        WHERE $where
        ORDER BY u.created_at DESC
        LIMIT $limit OFFSET $offset";

$users = $db->fetchAll($sql, $params);

// 获取总数
$countSql = "SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE $where";
$total = $db->fetchColumn($countSql, $params);
$totalPages = ceil($total / $limit);

// 获取用户组列表
$userGroups = $db->fetchAll("SELECT * FROM {$db->getPrefix()}user_groups ORDER BY sort_order ASC");

// 获取编辑的用户
$editUser = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $editId = intval($_GET['id']);
    $editUser = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $editId]);
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">搜索用户</label>
                <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="用户名/邮箱/昵称">
            </div>
            <div class="col-md-2">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>正常</option>
                    <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>禁用</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">用户组</label>
                <select class="form-select" name="user_group">
                    <option value="">全部用户组</option>
                    <?php foreach ($userGroups as $group): ?>
                        <option value="<?php echo $group['id']; ?>" <?php echo $userGroup == $group['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($group['group_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <a href="/admin/users.php?action=add" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加用户
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>用户列表 
            <span class="badge bg-secondary"><?php echo number_format($total); ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-danger btn-sm batch-action" disabled onclick="batchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($users)): ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无用户数据</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>用户名</th>
                            <th>邮箱</th>
                            <th>昵称</th>
                            <th>用户组</th>
                            <th>积分</th>
                            <th>状态</th>
                            <th>注册时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?php echo $user['id']; ?>">
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                    <?php if ($user['group_expire_time'] && strtotime($user['group_expire_time']) > time()): ?>
                                        <span class="badge bg-warning text-dark">VIP</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td><?php echo htmlspecialchars($user['nickname'] ?: '-'); ?></td>
                                <td>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($user['group_name'] ?: '未分组'); ?></span>
                                </td>
                                <td><?php echo number_format($user['points']); ?></td>
                                <td>
                                    <?php if ($user['status']): ?>
                                        <span class="badge bg-success">正常</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">禁用</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/admin/users.php?action=edit&id=<?php echo $user['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-<?php echo $user['status'] ? 'warning' : 'success'; ?>" 
                                                onclick="toggleStatus(<?php echo $user['id']; ?>, <?php echo $user['status'] ? 0 : 1; ?>)" 
                                                title="<?php echo $user['status'] ? '禁用' : '启用'; ?>">
                                            <i class="fas fa-<?php echo $user['status'] ? 'ban' : 'check'; ?>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteUser(<?php echo $user['id']; ?>)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 分页 -->
<?php if ($totalPages > 1): ?>
<nav class="mt-4">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&user_group=<?php echo $userGroup; ?>">上一页</a>
            </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&user_group=<?php echo $userGroup; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
        
        <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo $status; ?>&user_group=<?php echo $userGroup; ?>">下一页</a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<!-- 编辑用户模态框 -->
<?php if ($editUser): ?>
<div class="modal fade show" id="editUserModal" tabindex="-1" style="display: block; background: rgba(0,0,0,0.5);">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <a href="/admin/users.php" class="btn-close"></a>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" value="<?php echo $editUser['id']; ?>">

                    <div class="mb-3">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" name="username"
                               value="<?php echo htmlspecialchars($editUser['username']); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">邮箱 *</label>
                        <input type="email" class="form-control" name="email"
                               value="<?php echo htmlspecialchars($editUser['email']); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">昵称</label>
                        <input type="text" class="form-control" name="nickname"
                               value="<?php echo htmlspecialchars($editUser['nickname'] ?? ''); ?>">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">新密码</label>
                        <input type="password" class="form-control" name="password"
                               placeholder="留空则不修改密码">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">用户组</label>
                        <select class="form-select" name="group_id">
                            <?php foreach ($userGroups as $group): ?>
                                <option value="<?php echo $group['id']; ?>"
                                        <?php echo $editUser['group_id'] == $group['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($group['group_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">积分</label>
                        <input type="number" class="form-control" name="points"
                               value="<?php echo $editUser['points']; ?>" min="0">
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="status" value="1"
                                   <?php echo $editUser['status'] ? 'checked' : ''; ?>>
                            <label class="form-check-label">启用状态</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="/admin/users.php" class="btn btn-secondary">取消</a>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
$pageScript = "
    function deleteUser(userId) {
        if (confirmDelete('确定要删除这个用户吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function toggleStatus(userId, status) {
        const action = status ? '启用' : '禁用';
        if (confirm('确定要' + action + '这个用户吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"update_status\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\"><input type=\"hidden\" name=\"status\" value=\"' + status + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function batchDelete() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showWarning('请选择要删除的用户');
            return;
        }
        
        if (confirmBatchAction('删除', selectedIds.length)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"batch_delete\">';
            selectedIds.forEach(id => {
                form.innerHTML += '<input type=\"hidden\" name=\"user_ids[]\" value=\"' + id + '\">';
            });
            document.body.appendChild(form);
            form.submit();
        }
    }
";

include __DIR__ . '/templates/footer.php';
?>
