<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$id = intval($_GET['id'] ?? 0);
$message = '';
$messageType = 'success';

if (!$id) {
    header('Location: feedback.php');
    exit;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $status = $_POST['status'];
        $adminReply = trim($_POST['admin_reply'] ?? '');
        
        $validStatuses = ['pending', 'processing', 'resolved', 'closed'];
        if (!in_array($status, $validStatuses)) {
            throw new Exception('无效的状态');
        }
        
        $updateData = ['status' => $status];
        if (!empty($adminReply)) {
            $updateData['admin_reply'] = $adminReply;
        }
        
        $result = $db->update('feedback', $updateData, 'id = :id', ['id' => $id]);
        if ($result) {
            $message = '工单更新成功';
            header('Location: feedback.php?updated=1');
            exit;
        } else {
            throw new Exception('更新失败');
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
    }
}

// 获取工单详情
$sql = "SELECT f.*, u.username, u.nickname, u.email as user_email 
        FROM {$db->getPrefix()}feedback f 
        LEFT JOIN {$db->getPrefix()}users u ON f.user_id = u.id 
        WHERE f.id = :id";

$feedback = $db->fetch($sql, ['id' => $id]);

if (!$feedback) {
    header('Location: feedback.php');
    exit;
}

$pageTitle = '编辑工单 #' . $feedback['id'];
$currentPage = 'feedback';

// 包含头部模板
include __DIR__ . '/templates/header.php';

$statusText = [
    'pending' => '待处理',
    'processing' => '处理中',
    'resolved' => '已解决',
    'closed' => '已关闭'
];

$priorityText = [
    'low' => '低',
    'medium' => '中',
    'high' => '高'
];

$subjectText = [
    'bug' => 'Bug反馈',
    'feature' => '功能建议',
    'content' => '内容问题',
    'account' => '账户问题',
    'payment' => '支付问题',
    'technical' => '技术问题',
    'other' => '其他问题'
];
?>

<!-- 主内容 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">编辑工单 #<?php echo $feedback['id']; ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="feedback.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 工单信息 -->
    <div class="col-md-8">
        <!-- 原始反馈 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>用户反馈
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>提交者:</strong>
                        <?php if ($feedback['user_id']): ?>
                            <?php echo htmlspecialchars($feedback['nickname'] ?: $feedback['username']); ?>
                        <?php else: ?>
                            <?php echo htmlspecialchars($feedback['name']); ?>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>邮箱:</strong>
                        <?php echo htmlspecialchars($feedback['user_email'] ?: $feedback['email']); ?>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>主题:</strong>
                        <?php echo $subjectText[$feedback['subject']] ?? $feedback['subject']; ?>
                    </div>
                    <div class="col-md-6">
                        <strong>提交时间:</strong>
                        <?php echo date('Y-m-d H:i:s', strtotime($feedback['created_at'])); ?>
                    </div>
                </div>
                
                <div class="mb-3">
                    <strong>详细内容:</strong>
                    <div class="mt-2 p-3 bg-light border rounded">
                        <?php echo nl2br(htmlspecialchars($feedback['message'])); ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- 处理表单 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>处理工单
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">工单状态 *</label>
                            <select name="status" class="form-select" required>
                                <?php foreach ($statusText as $key => $text): ?>
                                <option value="<?php echo $key; ?>" <?php echo $feedback['status'] === $key ? 'selected' : ''; ?>>
                                    <?php echo $text; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">管理员回复</label>
                        <textarea name="admin_reply" class="form-control" rows="6" 
                                  placeholder="输入回复内容，将发送给用户..."><?php echo htmlspecialchars($feedback['admin_reply'] ?: ''); ?></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            回复内容将显示给用户，请使用专业和友好的语言
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存更改
                        </button>
                        <a href="feedback.php" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-md-4">
        <!-- 工单信息 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>工单信息
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>工单ID:</strong></td>
                        <td>#<?php echo $feedback['id']; ?></td>
                    </tr>
                    <tr>
                        <td><strong>类型:</strong></td>
                        <td>
                            <?php if ($feedback['user_id']): ?>
                                <span class="badge bg-info">客服工单</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">意见反馈</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>优先级:</strong></td>
                        <td>
                            <?php
                            $priorityClass = [
                                'low' => 'success',
                                'medium' => 'warning',
                                'high' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $priorityClass[$feedback['priority']]; ?>">
                                <?php echo $priorityText[$feedback['priority']]; ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>当前状态:</strong></td>
                        <td>
                            <?php
                            $statusClass = [
                                'pending' => 'warning',
                                'processing' => 'info',
                                'resolved' => 'success',
                                'closed' => 'secondary'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $statusClass[$feedback['status']]; ?>">
                                <?php echo $statusText[$feedback['status']]; ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>创建时间:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($feedback['created_at'])); ?></td>
                    </tr>
                    <tr>
                        <td><strong>最后更新:</strong></td>
                        <td><?php echo date('Y-m-d H:i:s', strtotime($feedback['updated_at'])); ?></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 处理指南 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>处理指南
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong class="text-warning">待处理:</strong> 新提交的工单
                    </div>
                    <div class="mb-2">
                        <strong class="text-info">处理中:</strong> 正在调查或解决中
                    </div>
                    <div class="mb-2">
                        <strong class="text-success">已解决:</strong> 问题已解决，等待用户确认
                    </div>
                    <div class="mb-2">
                        <strong class="text-secondary">已关闭:</strong> 工单已完成或取消
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <p class="mb-1">
                        <i class="fas fa-clock me-1"></i>
                        <strong>响应时间目标:</strong>
                    </p>
                    <ul class="mb-0 ps-3">
                        <li>高优先级: 1小时内</li>
                        <li>中优先级: 4小时内</li>
                        <li>低优先级: 24小时内</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// 包含底部模板
include __DIR__ . '/templates/footer.php';
?>
