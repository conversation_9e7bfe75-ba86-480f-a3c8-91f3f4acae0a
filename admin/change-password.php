<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '修改密码';
$currentPage = 'change-password';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = '所有字段都不能为空';
        $messageType = 'danger';
    } elseif ($newPassword !== $confirmPassword) {
        $message = '新密码和确认密码不一致';
        $messageType = 'danger';
    } elseif (strlen($newPassword) < 6) {
        $message = '新密码长度不能少于6位';
        $messageType = 'danger';
    } else {
        // 验证当前密码
        $admin = $db->fetch("SELECT password FROM {$db->getPrefix()}admin_users WHERE id = :id", ['id' => $_SESSION['admin_id']]);
        
        if (!$admin || !password_verify($currentPassword, $admin['password'])) {
            $message = '当前密码不正确';
            $messageType = 'danger';
        } else {
            // 更新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $result = $db->update('admin_users', ['password' => $hashedPassword], 'id = :id', ['id' => $_SESSION['admin_id']]);
            
            if ($result) {
                $message = '密码修改成功，请重新登录';
                $messageType = 'success';
                
                // 记录密码修改日志
                $db->insert('admin_login_logs', [
                    'admin_id' => $_SESSION['admin_id'],
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'action' => 'password_changed',
                    'status' => 'success',
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                
                // 3秒后跳转到登录页
                echo '<script>setTimeout(function(){ window.location.href="/admin/logout.php"; }, 3000);</script>';
            } else {
                $message = '密码修改失败，请重试';
                $messageType = 'danger';
            }
        }
    }
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-key me-2"></i>修改密码
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="changePasswordForm">
                    <div class="mb-3">
                        <label class="form-label">当前密码 *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" name="current_password" id="currentPassword" required>
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('currentPassword')">
                                <i class="fas fa-eye" id="currentPasswordIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">新密码 *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" name="new_password" id="newPassword" 
                                   minlength="6" required onkeyup="checkPasswordStrength()">
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('newPassword')">
                                <i class="fas fa-eye" id="newPasswordIcon"></i>
                            </button>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted" id="passwordStrengthText">密码强度：弱</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">确认新密码 *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" name="confirm_password" id="confirmPassword" 
                                   minlength="6" required onkeyup="checkPasswordMatch()">
                            <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('confirmPassword')">
                                <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                            </button>
                        </div>
                        <div class="mt-1">
                            <small class="text-muted" id="passwordMatchText"></small>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>密码要求</h6>
                        <ul class="mb-0 small">
                            <li>密码长度至少6位</li>
                            <li>建议包含大小写字母、数字和特殊字符</li>
                            <li>不要使用过于简单的密码</li>
                            <li>定期更换密码以确保安全</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <i class="fas fa-save me-2"></i>修改密码
                        </button>
                        <a href="/admin/profile.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回个人资料
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 安全提示 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>安全提示
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">
                            <i class="fas fa-check-circle me-2"></i>安全建议
                        </h6>
                        <ul class="small">
                            <li>使用强密码</li>
                            <li>定期更换密码</li>
                            <li>不要在多个网站使用相同密码</li>
                            <li>启用两步验证</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>安全警告
                        </h6>
                        <ul class="small">
                            <li>不要使用生日、姓名等个人信息</li>
                            <li>不要在公共场所输入密码</li>
                            <li>不要将密码告诉他人</li>
                            <li>发现异常登录立即修改密码</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$pageScript = "
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + 'Icon');
        
        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
    
    function checkPasswordStrength() {
        const password = document.getElementById('newPassword').value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        
        let strength = 0;
        let text = '弱';
        let color = 'bg-danger';
        
        if (password.length >= 6) strength += 20;
        if (password.length >= 8) strength += 20;
        if (/[a-z]/.test(password)) strength += 20;
        if (/[A-Z]/.test(password)) strength += 20;
        if (/[0-9]/.test(password)) strength += 10;
        if (/[^A-Za-z0-9]/.test(password)) strength += 10;
        
        if (strength >= 80) {
            text = '强';
            color = 'bg-success';
        } else if (strength >= 60) {
            text = '中等';
            color = 'bg-warning';
        } else if (strength >= 40) {
            text = '一般';
            color = 'bg-info';
        }
        
        strengthBar.style.width = strength + '%';
        strengthBar.className = 'progress-bar ' + color;
        strengthText.textContent = '密码强度：' + text;
        
        checkFormValid();
    }
    
    function checkPasswordMatch() {
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const matchText = document.getElementById('passwordMatchText');
        
        if (confirmPassword === '') {
            matchText.textContent = '';
            matchText.className = 'text-muted';
        } else if (newPassword === confirmPassword) {
            matchText.textContent = '✓ 密码匹配';
            matchText.className = 'text-success';
        } else {
            matchText.textContent = '✗ 密码不匹配';
            matchText.className = 'text-danger';
        }
        
        checkFormValid();
    }
    
    function checkFormValid() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const submitBtn = document.getElementById('submitBtn');
        
        const isValid = currentPassword.length > 0 && 
                       newPassword.length >= 6 && 
                       confirmPassword.length >= 6 && 
                       newPassword === confirmPassword;
        
        submitBtn.disabled = !isValid;
    }
    
    // 绑定事件
    document.getElementById('currentPassword').addEventListener('keyup', checkFormValid);
    document.getElementById('newPassword').addEventListener('keyup', function() {
        checkPasswordStrength();
        checkPasswordMatch();
    });
    document.getElementById('confirmPassword').addEventListener('keyup', checkPasswordMatch);
";

include __DIR__ . '/templates/footer.php';
?>
