<?php
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../includes/functions.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login.php');
    exit;
}

$db = Database::getInstance();

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        // 添加友情链接
        $name = trim($_POST['name'] ?? '');
        $url = trim($_POST['url'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $logo = trim($_POST['logo'] ?? '');
        $sort_order = intval($_POST['sort_order'] ?? 0);
        $status = intval($_POST['status'] ?? 1);
        
        if ($name && $url) {
            $db->insert('friendly_links', [
                'name' => $name,
                'url' => $url,
                'description' => $description,
                'logo' => $logo,
                'sort_order' => $sort_order,
                'status' => $status
            ]);
            $message = '友情链接添加成功';
        } else {
            $error = '请填写链接名称和地址';
        }
    } elseif ($action === 'edit') {
        // 编辑友情链接
        $id = intval($_POST['id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $url = trim($_POST['url'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $logo = trim($_POST['logo'] ?? '');
        $sort_order = intval($_POST['sort_order'] ?? 0);
        $status = intval($_POST['status'] ?? 1);
        
        if ($id && $name && $url) {
            $db->update('friendly_links', [
                'name' => $name,
                'url' => $url,
                'description' => $description,
                'logo' => $logo,
                'sort_order' => $sort_order,
                'status' => $status
            ], 'id = :id', ['id' => $id]);
            $message = '友情链接更新成功';
        } else {
            $error = '请填写完整信息';
        }
    } elseif ($action === 'delete') {
        // 删除友情链接
        $id = intval($_POST['id'] ?? 0);
        if ($id) {
            $db->delete('friendly_links', 'id = :id', ['id' => $id]);
            $message = '友情链接删除成功';
        }
    } elseif ($action === 'toggle_status') {
        // 切换状态
        $id = intval($_POST['id'] ?? 0);
        if ($id) {
            $link = $db->fetch("SELECT status FROM {$db->getPrefix()}friendly_links WHERE id = :id", ['id' => $id]);
            if ($link) {
                $newStatus = $link['status'] ? 0 : 1;
                $db->update('friendly_links', ['status' => $newStatus], 'id = :id', ['id' => $id]);
                $message = '状态更新成功';
            }
        }
    }
}

// 获取友情链接列表
$links = $db->fetchAll("
    SELECT * FROM {$db->getPrefix()}friendly_links 
    ORDER BY sort_order ASC, id DESC
");

// 获取编辑的链接信息
$editLink = null;
if (isset($_GET['edit'])) {
    $editId = intval($_GET['edit']);
    $editLink = $db->fetch("SELECT * FROM {$db->getPrefix()}friendly_links WHERE id = :id", ['id' => $editId]);
}

$pageTitle = '友情链接管理';
$currentPage = 'friendly-links';

// 包含头部模板
include __DIR__ . '/templates/header.php';
?>

                <?php if (isset($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 添加/编辑表单 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo $editLink ? '编辑友情链接' : '添加友情链接'; ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="<?php echo $editLink ? 'edit' : 'add'; ?>">
                            <?php if ($editLink): ?>
                            <input type="hidden" name="id" value="<?php echo $editLink['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">链接名称 *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo htmlspecialchars($editLink['name'] ?? ''); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="url" class="form-label">链接地址 *</label>
                                        <input type="url" class="form-control" id="url" name="url" 
                                               value="<?php echo htmlspecialchars($editLink['url'] ?? ''); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">链接描述</label>
                                        <input type="text" class="form-control" id="description" name="description" 
                                               value="<?php echo htmlspecialchars($editLink['description'] ?? ''); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="logo" class="form-label">链接图标</label>
                                        <input type="url" class="form-control" id="logo" name="logo" 
                                               value="<?php echo htmlspecialchars($editLink['logo'] ?? ''); ?>" 
                                               placeholder="图标URL（可选）">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sort_order" class="form-label">排序</label>
                                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                               value="<?php echo $editLink['sort_order'] ?? 0; ?>" min="0">
                                        <div class="form-text">数字越小排序越靠前</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">状态</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="1" <?php echo ($editLink['status'] ?? 1) == 1 ? 'selected' : ''; ?>>启用</option>
                                            <option value="0" <?php echo ($editLink['status'] ?? 1) == 0 ? 'selected' : ''; ?>>禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i><?php echo $editLink ? '更新' : '添加'; ?>
                                </button>
                                <?php if ($editLink): ?>
                                <a href="/admin/friendly-links.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>取消
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 友情链接列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">友情链接列表</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($links)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-link fa-3x text-muted mb-3"></i>
                            <p class="text-muted">暂无友情链接</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>名称</th>
                                        <th>地址</th>
                                        <th>描述</th>
                                        <th>排序</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($links as $link): ?>
                                    <tr>
                                        <td><?php echo $link['id']; ?></td>
                                        <td>
                                            <?php if ($link['logo']): ?>
                                            <img src="<?php echo htmlspecialchars($link['logo']); ?>" 
                                                 alt="<?php echo htmlspecialchars($link['name']); ?>" 
                                                 style="width: 20px; height: 20px; margin-right: 5px;">
                                            <?php endif; ?>
                                            <?php echo htmlspecialchars($link['name']); ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo htmlspecialchars($link['url']); ?>" target="_blank" class="text-decoration-none">
                                                <?php echo htmlspecialchars(mb_substr($link['url'], 0, 30)) . (mb_strlen($link['url']) > 30 ? '...' : ''); ?>
                                                <i class="fas fa-external-link-alt ms-1"></i>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($link['description'] ?: '-'); ?></td>
                                        <td><?php echo $link['sort_order']; ?></td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="id" value="<?php echo $link['id']; ?>">
                                                <button type="submit" class="btn btn-sm <?php echo $link['status'] ? 'btn-success' : 'btn-secondary'; ?>">
                                                    <?php echo $link['status'] ? '启用' : '禁用'; ?>
                                                </button>
                                            </form>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', strtotime($link['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?edit=<?php echo $link['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" onclick="deleteLink(<?php echo $link['id']; ?>)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

<?php
// 页面脚本
$pageScript = "
function deleteLink(id) {
    if (confirm('确定要删除这个友情链接吗？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = \`
            <input type=\"hidden\" name=\"action\" value=\"delete\">
            <input type=\"hidden\" name=\"id\" value=\"\${id}\">
        \`;
        document.body.appendChild(form);
        form.submit();
    }
}
";

// 包含底部模板
include __DIR__ . '/templates/footer.php';
?>
