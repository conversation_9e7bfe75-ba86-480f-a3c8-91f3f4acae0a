<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '分类管理';
$currentPage = 'categories';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'add':
            $data = [
                'name' => trim($_POST['name']),
                'slug' => trim($_POST['slug']),
                'parent_id' => intval($_POST['parent_id']),
                'description' => trim($_POST['description']),
                'sort' => intval($_POST['sort_order']),
                'status' => isset($_POST['status']) ? 1 : 0
            ];
            
            if (empty($data['name'])) {
                $message = '分类名称不能为空';
                $messageType = 'danger';
            } else {
                // 自动生成slug
                if (empty($data['slug'])) {
                    $data['slug'] = strtolower(preg_replace('/[^a-zA-Z0-9\-_]/', '-', $data['name']));
                }
                
                $result = $db->insert('categories', $data);
                if ($result) {
                    $message = '分类添加成功';
                    $messageType = 'success';
                } else {
                    $message = '分类添加失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'edit':
            $id = intval($_POST['id']);
            $data = [
                'name' => trim($_POST['name']),
                'slug' => trim($_POST['slug']),
                'parent_id' => intval($_POST['parent_id']),
                'description' => trim($_POST['description']),
                'sort' => intval($_POST['sort_order']),
                'status' => isset($_POST['status']) ? 1 : 0
            ];
            
            if (empty($data['name'])) {
                $message = '分类名称不能为空';
                $messageType = 'danger';
            } else {
                // 自动生成slug
                if (empty($data['slug'])) {
                    $data['slug'] = strtolower(preg_replace('/[^a-zA-Z0-9\-_]/', '-', $data['name']));
                }
                
                $result = $db->update('categories', $data, 'id = :id', ['id' => $id]);
                if ($result) {
                    $message = '分类更新成功';
                    $messageType = 'success';
                } else {
                    $message = '分类更新失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'delete':
            $id = intval($_POST['id']);
            if ($id > 0) {
                // 检查是否有子分类
                $childCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}categories WHERE parent_id = :id", ['id' => $id]);
                if ($childCount > 0) {
                    $message = '该分类下还有子分类，无法删除';
                    $messageType = 'danger';
                } else {
                    // 检查是否有套图使用此分类
                    $albumCount = $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE category_id = :id", ['id' => $id]);
                    if ($albumCount > 0) {
                        $message = '该分类下还有套图，无法删除';
                        $messageType = 'danger';
                    } else {
                        $result = $db->delete('categories', 'id = :id', ['id' => $id]);
                        if ($result) {
                            $message = '分类删除成功';
                            $messageType = 'success';
                        } else {
                            $message = '分类删除失败';
                            $messageType = 'danger';
                        }
                    }
                }
            }
            break;
    }
}

// 获取分类列表（树形结构）
function buildCategoryTree($categories, $parentId = 0, $level = 0) {
    $tree = [];
    foreach ($categories as $category) {
        if ($category['parent_id'] == $parentId) {
            $category['level'] = $level;
            $tree[] = $category;
            $children = buildCategoryTree($categories, $category['id'], $level + 1);
            $tree = array_merge($tree, $children);
        }
    }
    return $tree;
}

$allCategories = $db->fetchAll("SELECT c.*,
                                       COUNT(a.id) as album_count,
                                       (SELECT COUNT(*) FROM {$db->getPrefix()}categories cc WHERE cc.parent_id = c.id) as child_count
                                FROM {$db->getPrefix()}categories c
                                LEFT JOIN {$db->getPrefix()}albums a ON c.id = a.category_id
                                GROUP BY c.id
                                ORDER BY c.sort ASC, c.id ASC");

$categories = buildCategoryTree($allCategories);

// 获取编辑的分类
$editCategory = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $editId = intval($_GET['id']);
    $editCategory = $db->fetch("SELECT * FROM {$db->getPrefix()}categories WHERE id = :id", ['id' => $editId]);
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="row">
    <!-- 分类列表 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>分类列表
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($categories)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无分类数据</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>分类名称</th>
                                    <th>别名</th>
                                    <th>套图数</th>
                                    <th>子分类</th>
                                    <th>排序</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                    <tr>
                                        <td>
                                            <?php echo str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $category['level']); ?>
                                            <?php if ($category['level'] > 0): ?>
                                                <i class="fas fa-level-up-alt fa-rotate-90 text-muted me-1"></i>
                                            <?php endif; ?>
                                            <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($category['slug']); ?></code>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo number_format($category['album_count']); ?></span>
                                        </td>
                                        <td>
                                            <?php if ($category['child_count'] > 0): ?>
                                                <span class="badge bg-info"><?php echo $category['child_count']; ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $category['sort']; ?></td>
                                        <td>
                                            <?php if ($category['status']): ?>
                                                <span class="badge bg-success">启用</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">禁用</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="?action=edit&id=<?php echo $category['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($category['child_count'] == 0 && $category['album_count'] == 0): ?>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteCategory(<?php echo $category['id']; ?>)" title="删除">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 添加/编辑分类 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo $editCategory ? 'edit' : 'plus'; ?> me-2"></i>
                    <?php echo $editCategory ? '编辑分类' : '添加分类'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="<?php echo $editCategory ? 'edit' : 'add'; ?>">
                    <?php if ($editCategory): ?>
                        <input type="hidden" name="id" value="<?php echo $editCategory['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label class="form-label">分类名称 *</label>
                        <input type="text" class="form-control" name="name" 
                               value="<?php echo htmlspecialchars($editCategory['name'] ?? ''); ?>" 
                               placeholder="如：美女写真" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">别名</label>
                        <input type="text" class="form-control" name="slug" 
                               value="<?php echo htmlspecialchars($editCategory['slug'] ?? ''); ?>" 
                               placeholder="如：beauty-photos（留空自动生成）">
                        <small class="text-muted">用于URL，只能包含字母、数字、连字符</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">父分类</label>
                        <select class="form-select" name="parent_id">
                            <option value="0">顶级分类</option>
                            <?php foreach ($allCategories as $cat): ?>
                                <?php if (!$editCategory || $cat['id'] != $editCategory['id']): ?>
                                    <option value="<?php echo $cat['id']; ?>" 
                                            <?php echo ($editCategory['parent_id'] ?? 0) == $cat['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($cat['name']); ?>
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" name="description" rows="3" 
                                  placeholder="分类描述（可选）"><?php echo htmlspecialchars($editCategory['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">排序</label>
                        <input type="number" class="form-control" name="sort_order"
                               value="<?php echo $editCategory['sort'] ?? 0; ?>">
                        <small class="text-muted">数字越小排序越靠前</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="status" value="1" 
                                   <?php echo ($editCategory['status'] ?? 1) ? 'checked' : ''; ?>>
                            <label class="form-check-label">启用状态</label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i><?php echo $editCategory ? '更新' : '添加'; ?>
                        </button>
                        <?php if ($editCategory): ?>
                            <a href="/admin/categories.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>取消
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$pageScript = "
    function deleteCategory(categoryId) {
        if (confirmDelete('确定要删除这个分类吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"id\" value=\"' + categoryId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
";

include __DIR__ . '/templates/footer.php';
?>
