<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = 'success';

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'update_status':
                $id = intval($_POST['id']);
                $status = $_POST['status'];
                $statusNote = trim($_POST['status_note'] ?? '');
                $adminId = $_SESSION['admin_id'];

                $validStatuses = ['pending', 'processing', 'resolved', 'closed'];
                if (!in_array($status, $validStatuses)) {
                    throw new Exception('无效的状态');
                }

                // 获取当前状态
                $currentFeedback = $db->fetch("SELECT status FROM {$db->getPrefix()}feedback WHERE id = :id", ['id' => $id]);
                if (!$currentFeedback) {
                    throw new Exception('工单不存在');
                }

                $oldStatus = $currentFeedback['status'];

                // 开始事务
                $db->beginTransaction();

                try {
                    // 更新工单状态
                    $result = $db->update('feedback', ['status' => $status], 'id = :id', ['id' => $id]);
                    if (!$result) {
                        throw new Exception('状态更新失败');
                    }

                    // 记录状态变更日志
                    if ($oldStatus !== $status) {
                        $db->insert('feedback_status_logs', [
                            'feedback_id' => $id,
                            'admin_id' => $adminId,
                            'old_status' => $oldStatus,
                            'new_status' => $status,
                            'note' => $statusNote ?: '管理员手动更新状态'
                        ]);
                    }

                    $db->commit();
                    $message = '工单状态更新成功';

                } catch (Exception $e) {
                    $db->rollback();
                    throw $e;
                }
                break;
                
            case 'delete':
                $id = intval($_POST['id']);
                $result = $db->query("DELETE FROM {$db->getPrefix()}feedback WHERE id = :id", ['id' => $id]);
                if ($result) {
                    $message = '工单删除成功';
                } else {
                    throw new Exception('删除失败');
                }
                break;
                
            case 'batch_delete':
                $ids = $_POST['ids'] ?? [];
                if (!empty($ids)) {
                    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
                    $result = $db->query("DELETE FROM {$db->getPrefix()}feedback WHERE id IN ($placeholders)", $ids);
                    if ($result) {
                        $message = '批量删除成功';
                    } else {
                        throw new Exception('批量删除失败');
                    }
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
    }
}

// 获取筛选参数
$type = $_GET['type'] ?? 'all'; // all, feedback, support
$status = $_GET['status'] ?? '';
$priority = $_GET['priority'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = [];
$params = [];

// 根据类型筛选
if ($type === 'feedback') {
    $where[] = 'user_id IS NULL'; // 意见反馈（未登录用户）
} elseif ($type === 'support') {
    $where[] = 'user_id IS NOT NULL'; // 客服工单（登录用户）
}

if (!empty($status)) {
    $where[] = 'status = :status';
    $params['status'] = $status;
}

if (!empty($priority)) {
    $where[] = 'priority = :priority';
    $params['priority'] = $priority;
}

$whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

// 获取总数
$totalSql = "SELECT COUNT(*) FROM {$db->getPrefix()}feedback $whereClause";
$total = $db->fetchColumn($totalSql, $params);

// 获取工单列表
$sql = "SELECT f.*, u.username, u.nickname,
               (SELECT COUNT(*) FROM {$db->getPrefix()}feedback_replies r WHERE r.feedback_id = f.id) as reply_count,
               (SELECT MAX(r.created_at) FROM {$db->getPrefix()}feedback_replies r WHERE r.feedback_id = f.id) as last_reply_time
        FROM {$db->getPrefix()}feedback f
        LEFT JOIN {$db->getPrefix()}users u ON f.user_id = u.id
        $whereClause
        ORDER BY f.created_at DESC
        LIMIT $limit OFFSET $offset";

$feedbacks = $db->fetchAll($sql, $params);

// 计算分页
$totalPages = ceil($total / $limit);

$pageTitle = '工单管理';
$currentPage = 'feedback';

// 包含头部模板
include __DIR__ . '/templates/header.php';
?>

<!-- 主内容 -->
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">工单管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="?type=all" class="btn btn-sm <?php echo $type === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                全部 (<?php echo $total; ?>)
            </a>
            <a href="?type=feedback" class="btn btn-sm <?php echo $type === 'feedback' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                意见反馈
            </a>
            <a href="?type=support" class="btn btn-sm <?php echo $type === 'support' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                客服工单
            </a>
        </div>
    </div>
</div>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- 筛选器 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="type" value="<?php echo htmlspecialchars($type); ?>">
            
            <div class="col-md-3">
                <label class="form-label">状态</label>
                <select name="status" class="form-select">
                    <option value="">全部状态</option>
                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>待处理</option>
                    <option value="processing" <?php echo $status === 'processing' ? 'selected' : ''; ?>>处理中</option>
                    <option value="resolved" <?php echo $status === 'resolved' ? 'selected' : ''; ?>>已解决</option>
                    <option value="closed" <?php echo $status === 'closed' ? 'selected' : ''; ?>>已关闭</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label class="form-label">优先级</label>
                <select name="priority" class="form-select">
                    <option value="">全部优先级</option>
                    <option value="low" <?php echo $priority === 'low' ? 'selected' : ''; ?>>低</option>
                    <option value="medium" <?php echo $priority === 'medium' ? 'selected' : ''; ?>>中</option>
                    <option value="high" <?php echo $priority === 'high' ? 'selected' : ''; ?>>高</option>
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">筛选</button>
                <a href="?type=<?php echo htmlspecialchars($type); ?>" class="btn btn-outline-secondary">重置</a>
            </div>
        </form>
    </div>
</div>

<!-- 工单列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">工单列表</h5>
        <button type="button" class="btn btn-danger btn-sm" onclick="batchDelete()" id="batchDeleteBtn" style="display: none;">
            <i class="fas fa-trash me-1"></i>批量删除
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($feedbacks)): ?>
        <div class="text-center py-4">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <p class="text-muted">暂无工单</p>
        </div>
        <?php else: ?>
        <form id="batchForm">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>ID</th>
                            <th>类型</th>
                            <th>用户</th>
                            <th>主题</th>
                            <th>优先级</th>
                            <th>状态</th>
                            <th>回复数</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($feedbacks as $feedback): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="ids[]" value="<?php echo $feedback['id']; ?>" onchange="updateBatchButton()">
                            </td>
                            <td><?php echo $feedback['id']; ?></td>
                            <td>
                                <?php if ($feedback['user_id']): ?>
                                    <span class="badge bg-info">客服工单</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">意见反馈</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($feedback['user_id']): ?>
                                    <strong><?php echo htmlspecialchars($feedback['nickname'] ?: $feedback['username']); ?></strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($feedback['email']); ?></small>
                                <?php else: ?>
                                    <strong><?php echo htmlspecialchars($feedback['name']); ?></strong><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($feedback['email']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo ucfirst($feedback['subject']); ?></strong><br>
                                <small class="text-muted"><?php echo htmlspecialchars(mb_substr($feedback['message'], 0, 50) . '...'); ?></small>
                            </td>
                            <td>
                                <?php
                                $priorityClass = [
                                    'low' => 'success',
                                    'medium' => 'warning',
                                    'high' => 'danger'
                                ];
                                $priorityText = [
                                    'low' => '低',
                                    'medium' => '中',
                                    'high' => '高'
                                ];
                                ?>
                                <span class="badge bg-<?php echo $priorityClass[$feedback['priority']]; ?>">
                                    <?php echo $priorityText[$feedback['priority']]; ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $statusClass = [
                                    'pending' => 'warning',
                                    'processing' => 'info',
                                    'resolved' => 'success',
                                    'closed' => 'secondary'
                                ];
                                $statusText = [
                                    'pending' => '待处理',
                                    'processing' => '处理中',
                                    'resolved' => '已解决',
                                    'closed' => '已关闭'
                                ];
                                ?>
                                <span class="badge bg-<?php echo $statusClass[$feedback['status']]; ?>">
                                    <?php echo $statusText[$feedback['status']]; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($feedback['reply_count'] > 0): ?>
                                    <span class="badge bg-primary"><?php echo $feedback['reply_count']; ?></span>
                                <?php else: ?>
                                    <span class="text-muted">0</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $lastTime = $feedback['last_reply_time'] ?: $feedback['created_at'];
                                echo date('Y-m-d H:i', strtotime($lastTime));
                                ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" onclick="viewFeedback(<?php echo $feedback['id']; ?>)" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="editFeedback(<?php echo $feedback['id']; ?>)" title="处理">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteFeedback(<?php echo $feedback['id']; ?>)" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </form>
        
        <!-- 分页 -->
        <?php if ($totalPages > 1): ?>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $type; ?>&status=<?php echo $status; ?>&priority=<?php echo $priority; ?>&page=<?php echo $page - 1; ?>">上一页</a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?type=<?php echo $type; ?>&status=<?php echo $status; ?>&priority=<?php echo $priority; ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?type=<?php echo $type; ?>&status=<?php echo $status; ?>&priority=<?php echo $priority; ?>&page=<?php echo $page + 1; ?>">下一页</a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<?php
// 页面脚本
$pageScript = '
// 全选功能
function toggleSelectAll() {
    var selectAll = document.getElementById("selectAll");
    var checkboxes = document.querySelectorAll("input[name=\\"ids[]\\"]");
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].checked = selectAll.checked;
    }
    updateBatchButton();
}

// 更新批量操作按钮
function updateBatchButton() {
    var checked = document.querySelectorAll("input[name=\\"ids[]\\"]:checked");
    var batchBtn = document.getElementById("batchDeleteBtn");
    if (batchBtn) {
        batchBtn.style.display = checked.length > 0 ? "block" : "none";
    }
}

// 批量删除
function batchDelete() {
    var checked = document.querySelectorAll("input[name=\\"ids[]\\"]:checked");
    if (checked.length === 0) return;

    if (confirm("确定要删除选中的 " + checked.length + " 个工单吗？")) {
        var form = document.createElement("form");
        form.method = "POST";
        form.innerHTML = "<input type=\\"hidden\\" name=\\"action\\" value=\\"batch_delete\\">";

        for (var i = 0; i < checked.length; i++) {
            var input = document.createElement("input");
            input.type = "hidden";
            input.name = "ids[]";
            input.value = checked[i].value;
            form.appendChild(input);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// 查看工单详情
function viewFeedback(id) {
    console.log("查看工单详情: " + id);
    window.open("feedback-detail.php?id=" + id, "_blank", "width=800,height=600");
}

// 编辑工单
function editFeedback(id) {
    console.log("编辑工单: " + id);
    window.location.href = "feedback-edit.php?id=" + id;
}

// 删除工单
function deleteFeedback(id) {
    console.log("删除工单: " + id);
    if (confirm("确定要删除这个工单吗？")) {
        var form = document.createElement("form");
        form.method = "POST";
        form.innerHTML = "<input type=\\"hidden\\" name=\\"action\\" value=\\"delete\\"><input type=\\"hidden\\" name=\\"id\\" value=\\"" + id + "\\">";
        document.body.appendChild(form);
        form.submit();
    }
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function() {
    console.log("工单管理页面JavaScript已加载");
});
';

// 包含底部模板
include __DIR__ . '/templates/footer.php';
?>
