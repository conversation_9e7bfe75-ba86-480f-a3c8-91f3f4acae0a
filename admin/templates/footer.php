    </div> <!-- 结束 main-content -->
    
    <!-- JavaScript文件 -->
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    
    <!-- 后台公共JavaScript -->
    <script>
        // 侧边栏切换
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }
        
        // 确认删除对话框
        function confirmDelete(message = '确定要删除这条记录吗？') {
            return confirm(message);
        }
        
        // 批量操作确认
        function confirmBatchAction(action, count) {
            return confirm(`确定要${action} ${count} 条记录吗？`);
        }
        
        // 显示加载状态
        function showLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
            button.disabled = true;
            return originalText;
        }
        
        // 隐藏加载状态
        function hideLoading(button, originalText) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
        
        // 显示成功消息
        function showSuccess(message) {
            showAlert(message, 'success');
        }
        
        // 显示错误消息
        function showError(message) {
            showAlert(message, 'danger');
        }
        
        // 显示警告消息
        function showWarning(message) {
            showAlert(message, 'warning');
        }
        
        // 显示信息消息
        function showInfo(message) {
            showAlert(message, 'info');
        }
        
        // 通用消息显示函数
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 查找消息容器
            let container = document.getElementById('message-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'message-container';
                container.className = 'position-fixed top-0 end-0 p-3';
                container.style.zIndex = '9999';
                document.body.appendChild(container);
            }
            
            container.innerHTML = alertHtml;
            
            // 3秒后自动隐藏
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        container.innerHTML = '';
                    }, 150);
                }
            }, 3000);
        }
        
        // 获取警告图标
        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
        
        // AJAX请求封装
        function ajaxRequest(url, data = {}, method = 'POST') {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: url,
                    method: method,
                    data: data,
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            resolve(response);
                        } else {
                            reject(response.message || '操作失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject('网络错误：' + error);
                    }
                });
            });
        }
        
        // 表格行选择
        function initTableSelection() {
            // 全选/取消全选
            $('#selectAll').change(function() {
                $('.row-select').prop('checked', this.checked);
                updateBatchButtons();
            });
            
            // 单行选择
            $(document).on('change', '.row-select', function() {
                updateBatchButtons();
                
                // 更新全选状态
                const total = $('.row-select').length;
                const checked = $('.row-select:checked').length;
                $('#selectAll').prop('indeterminate', checked > 0 && checked < total);
                $('#selectAll').prop('checked', checked === total);
            });
        }
        
        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const selectedCount = $('.row-select:checked').length;
            $('.batch-action').prop('disabled', selectedCount === 0);
            $('#selected-count').text(selectedCount);
        }
        
        // 获取选中的ID列表
        function getSelectedIds() {
            const ids = [];
            $('.row-select:checked').each(function() {
                ids.push($(this).val());
            });
            return ids;
        }
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            // 初始化表格选择功能
            initTableSelection();
            
            // 初始化工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // 初始化弹出框
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // 自动隐藏成功/错误消息
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        });
        
        // 数字格式化
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        
        // 文件大小格式化
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 时间格式化
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleString('zh-CN');
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showSuccess('已复制到剪贴板');
            }, function() {
                showError('复制失败');
            });
        }
    </script>
    
    <!-- 页面特定JavaScript -->
    <?php if (isset($pageScript)): ?>
    <script>
        <?php echo $pageScript; ?>
    </script>
    <?php endif; ?>
    
</body>
</html>
