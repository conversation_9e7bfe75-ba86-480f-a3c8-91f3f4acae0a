<?php
// 检查是否已登录
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin');
    exit;
}

require_once __DIR__ . '/../../core/Config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? '管理后台'; ?> - <?php echo Config::getSiteName(); ?></title>
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .sidebar {
            background-color: #343a40;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            transition: all 0.3s;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* 自定义滚动条样式 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #495057;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #adb5bd;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 8px 20px;
            border-radius: 0;
            transition: all 0.3s;
            font-size: 0.9rem;
        }
        .sidebar .nav-link:hover {
            color: #fff;
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #ffc107;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .sidebar .nav-item h6 {
            color: #ced4da !important;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.7rem;
            letter-spacing: 0.5px;
            padding: 8px 20px 4px 20px;
            margin-bottom: 0;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: all 0.3s;
        }
        .top-navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .stat-card {
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .col-md-3:nth-child(1) .stat-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .col-md-3:nth-child(2) .stat-card {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .col-md-3:nth-child(3) .stat-card {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .col-md-3:nth-child(4) .stat-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-card p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            border-radius: 10px 10px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
            font-weight: 500;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
            color: #000;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        .sidebar-toggle {
            display: none;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
            }
            .sidebar.show {
                margin-left: 0;
            }
            .main-content {
                margin-left: 0;
            }
            .sidebar-toggle {
                display: block;
            }
        }
        
        /* 自定义滚动条 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        .sidebar::-webkit-scrollbar-track {
            background: #2c3034;
        }
        .sidebar::-webkit-scrollbar-thumb {
            background: #495057;
            border-radius: 3px;
        }
        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar" id="sidebar">
        <div class="p-3 border-bottom border-secondary">
            <h5 class="text-white mb-0">
                <i class="fas fa-crown text-warning me-2"></i><?php echo Config::getSiteName(); ?>管理
            </h5>
            <small class="text-light">管理后台系统</small>
        </div>
        
        <nav class="nav flex-column p-2">
            <a class="nav-link <?php echo ($currentPage ?? '') == 'dashboard' ? 'active' : ''; ?>" href="/admin/dashboard.php">
                <i class="fas fa-tachometer-alt"></i>仪表盘
            </a>
            
            <div class="nav-item">
                <h6 class="px-3 py-2 mb-0 small">内容管理</h6>
            </div>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'albums' ? 'active' : ''; ?>" href="/admin/albums.php">
                <i class="fas fa-images"></i>套图管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'videos' ? 'active' : ''; ?>" href="/admin/videos.php">
                <i class="fas fa-video"></i>视频管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'categories' ? 'active' : ''; ?>" href="/admin/categories.php">
                <i class="fas fa-tags"></i>分类管理
            </a>
            
            <div class="nav-item">
                <h6 class="px-3 py-2 mb-0 small">用户管理</h6>
            </div>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'users' ? 'active' : ''; ?>" href="/admin/users.php">
                <i class="fas fa-users"></i>用户管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'user-groups' ? 'active' : ''; ?>" href="/admin/user-groups.php">
                <i class="fas fa-user-tag"></i>用户组管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'recharge-cards' ? 'active' : ''; ?>" href="/admin/recharge-cards.php">
                <i class="fas fa-credit-card"></i>充值卡管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'feedback' ? 'active' : ''; ?>" href="/admin/feedback.php">
                <i class="fas fa-headset"></i>工单管理
            </a>

            <div class="nav-item">
                <h6 class="px-3 py-2 mb-0 small">邀请系统</h6>
            </div>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'invite_stats' ? 'active' : ''; ?>" href="/admin/invite-stats.php">
                <i class="fas fa-chart-line"></i>邀请统计
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'withdraw' ? 'active' : ''; ?>" href="/admin/withdraw-manager.php">
                <i class="fas fa-money-bill-wave"></i>提现管理
            </a>
            
            <div class="nav-item">
                <h6 class="px-3 py-2 mb-0 small">采集功能</h6>
            </div>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'spider' ? 'active' : ''; ?>" href="/admin/spider.php">
                <i class="fas fa-spider"></i>采集管理
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'train-publish' ? 'active' : ''; ?>" href="/admin/train-publish.php">
                <i class="fas fa-train"></i>火车头发布
            </a>
            
            <div class="nav-item">
                <h6 class="px-3 py-2 mb-0 small">系统设置</h6>
            </div>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'settings' ? 'active' : ''; ?>" href="/admin/settings.php">
                <i class="fas fa-cog"></i>系统设置
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'friendly-links' ? 'active' : ''; ?>" href="/admin/friendly-links.php">
                <i class="fas fa-link"></i>友情链接
            </a>
            <a class="nav-link <?php echo ($currentPage ?? '') == 'statistics' ? 'active' : ''; ?>" href="/admin/statistics.php">
                <i class="fas fa-chart-bar"></i>数据统计
            </a>
            
            <hr class="border-secondary my-3">
            <a class="nav-link" href="/admin/logout.php">
                <i class="fas fa-sign-out-alt"></i>退出登录
            </a>
        </nav>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn btn-outline-secondary sidebar-toggle me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h4 class="mb-0"><?php echo $pageTitle ?? '管理后台'; ?></h4>
            </div>
            <div class="d-flex align-items-center">
                <span class="text-muted me-3">欢迎，<?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i><?php echo htmlspecialchars($_SESSION['admin_role']); ?>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/admin/profile.php"><i class="fas fa-user me-2"></i>个人资料</a></li>
                        <li><a class="dropdown-item" href="/admin/change-password.php"><i class="fas fa-key me-2"></i>修改密码</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/admin/logout.php"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
