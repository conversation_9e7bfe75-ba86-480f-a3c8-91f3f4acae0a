<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login.php');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '邀请统计';
$currentPage = 'invite_stats';

// 获取时间范围参数
$dateFrom = $_GET['date_from'] ?? date('Y-m-01'); // 默认本月开始
$dateTo = $_GET['date_to'] ?? date('Y-m-d');       // 默认今天
$period = $_GET['period'] ?? 'month';              // 统计周期

// 构建时间条件
$timeCondition = "DATE(created_at) BETWEEN :date_from AND :date_to";
$timeParams = ['date_from' => $dateFrom, 'date_to' => $dateTo];

// 获取总体统计
$overallStats = $db->fetch("
    SELECT 
        COUNT(DISTINCT inviter_id) as active_inviters,
        COUNT(DISTINCT invitee_id) as total_invitees,
        SUM(points_earned) as total_points_earned,
        SUM(CASE WHEN type IN ('register', 'level2_register') THEN points_earned ELSE 0 END) as register_points,
        SUM(CASE WHEN type IN ('recharge', 'level2_recharge') THEN points_earned ELSE 0 END) as recharge_points,
        SUM(CASE WHEN level = 1 THEN points_earned ELSE 0 END) as level1_points,
        SUM(CASE WHEN level = 2 THEN points_earned ELSE 0 END) as level2_points
    FROM {$db->getPrefix()}invite_earnings 
    WHERE {$timeCondition}
", $timeParams);

$overallStats = $overallStats ?: [
    'active_inviters' => 0, 'total_invitees' => 0, 'total_points_earned' => 0,
    'register_points' => 0, 'recharge_points' => 0, 'level1_points' => 0, 'level2_points' => 0
];

// 获取邀请排行榜
$topInviters = $db->fetchAll("
    SELECT 
        ie.inviter_id,
        u.username,
        u.nickname,
        COUNT(DISTINCT ie.invitee_id) as total_invitees,
        SUM(ie.points_earned) as total_earnings,
        SUM(CASE WHEN ie.type IN ('register', 'level2_register') THEN 1 ELSE 0 END) as register_count,
        SUM(CASE WHEN ie.type IN ('recharge', 'level2_recharge') THEN ie.points_earned ELSE 0 END) as recharge_earnings
    FROM {$db->getPrefix()}invite_earnings ie
    LEFT JOIN {$db->getPrefix()}users u ON ie.inviter_id = u.id
    WHERE {$timeCondition}
    GROUP BY ie.inviter_id
    ORDER BY total_earnings DESC
    LIMIT 20
", $timeParams);

// 获取最近邀请记录
$recentInvites = $db->fetchAll("
    SELECT 
        ie.*,
        inviter.username as inviter_username,
        inviter.nickname as inviter_nickname,
        invitee.username as invitee_username,
        invitee.nickname as invitee_nickname
    FROM {$db->getPrefix()}invite_earnings ie
    LEFT JOIN {$db->getPrefix()}users inviter ON ie.inviter_id = inviter.id
    LEFT JOIN {$db->getPrefix()}users invitee ON ie.invitee_id = invitee.id
    WHERE {$timeCondition}
    ORDER BY ie.created_at DESC
    LIMIT 50
", $timeParams);

// 获取每日统计数据（用于图表）
$dailyStats = $db->fetchAll("
    SELECT 
        DATE(created_at) as date,
        COUNT(DISTINCT invitee_id) as new_invitees,
        SUM(points_earned) as daily_points,
        SUM(CASE WHEN type IN ('register', 'level2_register') THEN points_earned ELSE 0 END) as register_points,
        SUM(CASE WHEN type IN ('recharge', 'level2_recharge') THEN points_earned ELSE 0 END) as recharge_points
    FROM {$db->getPrefix()}invite_earnings
    WHERE {$timeCondition}
    GROUP BY DATE(created_at)
    ORDER BY date ASC
", $timeParams);

// 获取提现统计
$withdrawStats = $db->fetch("
    SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'completed' THEN actual_amount ELSE 0 END) as total_paid,
        SUM(CASE WHEN status = 'pending' THEN actual_amount ELSE 0 END) as pending_amount,
        AVG(CASE WHEN status = 'completed' THEN actual_amount ELSE NULL END) as avg_amount
    FROM {$db->getPrefix()}withdraw_requests
    WHERE DATE(created_at) BETWEEN :date_from AND :date_to
", $timeParams);

$withdrawStats = $withdrawStats ?: [
    'total_requests' => 0, 'total_paid' => 0, 'pending_amount' => 0, 'avg_amount' => 0
];

include __DIR__ . '/templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">邀请统计分析</h3>
                </div>
                <div class="card-body">
                    <!-- 时间筛选 -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">开始日期</label>
                                    <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($dateFrom); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">结束日期</label>
                                    <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($dateTo); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">统计周期</label>
                                    <select name="period" class="form-select">
                                        <option value="day" <?php echo $period === 'day' ? 'selected' : ''; ?>>按天</option>
                                        <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>按周</option>
                                        <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>按月</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">查询</button>
                                        <button type="button" class="btn btn-success" onclick="exportData()">导出</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 总体统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $overallStats['active_inviters']; ?></h3>
                                    <p class="mb-0">活跃邀请人</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $overallStats['total_invitees']; ?></h3>
                                    <p class="mb-0">总邀请用户</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo number_format($overallStats['total_points_earned']); ?></h3>
                                    <p class="mb-0">总奖励积分</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>¥<?php echo number_format($withdrawStats['total_paid'], 2); ?></h3>
                                    <p class="mb-0">已提现金额</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 详细统计 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">积分分布</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-primary"><?php echo number_format($overallStats['register_points']); ?></h4>
                                                <p class="text-muted mb-0">注册奖励</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-success"><?php echo number_format($overallStats['recharge_points']); ?></h4>
                                                <p class="text-muted mb-0">充值分成</p>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-info"><?php echo number_format($overallStats['level1_points']); ?></h4>
                                                <p class="text-muted mb-0">一级邀请</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-warning"><?php echo number_format($overallStats['level2_points']); ?></h4>
                                                <p class="text-muted mb-0">二级邀请</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">提现统计</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-primary"><?php echo $withdrawStats['total_requests']; ?></h4>
                                                <p class="text-muted mb-0">提现申请</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="text-center">
                                                <h4 class="text-warning">¥<?php echo number_format($withdrawStats['pending_amount'], 2); ?></h4>
                                                <p class="text-muted mb-0">待处理金额</p>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <h4 class="text-success">¥<?php echo number_format($withdrawStats['avg_amount'], 2); ?></h4>
                                        <p class="text-muted mb-0">平均提现金额</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">每日趋势图</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="dailyChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 邀请排行榜 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">邀请排行榜 (TOP 20)</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>排名</th>
                                                    <th>用户</th>
                                                    <th>邀请数</th>
                                                    <th>总收益</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if (empty($topInviters)): ?>
                                                    <tr>
                                                        <td colspan="4" class="text-center text-muted">暂无数据</td>
                                                    </tr>
                                                <?php else: ?>
                                                    <?php foreach ($topInviters as $index => $inviter): ?>
                                                        <tr>
                                                            <td>
                                                                <?php if ($index < 3): ?>
                                                                    <span class="badge bg-<?php echo ['warning', 'secondary', 'dark'][$index]; ?>">
                                                                        <?php echo $index + 1; ?>
                                                                    </span>
                                                                <?php else: ?>
                                                                    <?php echo $index + 1; ?>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($inviter['username']); ?></strong>
                                                                <?php if ($inviter['nickname']): ?>
                                                                    <br><small class="text-muted"><?php echo htmlspecialchars($inviter['nickname']); ?></small>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td><?php echo $inviter['total_invitees']; ?></td>
                                                            <td>
                                                                <strong><?php echo number_format($inviter['total_earnings']); ?></strong>
                                                                <br><small class="text-success">¥<?php echo number_format($inviter['total_earnings'] / 100, 2); ?></small>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 最近邀请记录 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">最近邀请记录</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>类型</th>
                                                    <th>邀请人</th>
                                                    <th>被邀请人</th>
                                                    <th>积分</th>
                                                    <th>时间</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if (empty($recentInvites)): ?>
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted">暂无数据</td>
                                                    </tr>
                                                <?php else: ?>
                                                    <?php foreach (array_slice($recentInvites, 0, 15) as $invite): ?>
                                                        <tr>
                                                            <td>
                                                                <span class="badge bg-<?php 
                                                                    echo match($invite['type']) {
                                                                        'register' => 'primary',
                                                                        'recharge' => 'success',
                                                                        'level2_register' => 'info',
                                                                        'level2_recharge' => 'warning',
                                                                        default => 'secondary'
                                                                    };
                                                                ?>">
                                                                    <?php 
                                                                        echo match($invite['type']) {
                                                                            'register' => '注册',
                                                                            'recharge' => '充值',
                                                                            'level2_register' => '二级注册',
                                                                            'level2_recharge' => '二级充值',
                                                                            default => $invite['type']
                                                                        };
                                                                    ?>
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <small><?php echo htmlspecialchars($invite['inviter_username'] ?: '未知'); ?></small>
                                                            </td>
                                                            <td>
                                                                <small><?php echo htmlspecialchars($invite['invitee_username'] ?: '未知'); ?></small>
                                                            </td>
                                                            <td>
                                                                <strong>+<?php echo $invite['points_earned']; ?></strong>
                                                            </td>
                                                            <td>
                                                                <small><?php echo date('m-d H:i', strtotime($invite['created_at'])); ?></small>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// 每日趋势图
const dailyData = <?php echo json_encode($dailyStats); ?>;
const ctx = document.getElementById('dailyChart').getContext('2d');

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: dailyData.map(item => item.date),
        datasets: [
            {
                label: '新邀请用户',
                data: dailyData.map(item => item.new_invitees),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            },
            {
                label: '注册奖励积分',
                data: dailyData.map(item => item.register_points),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            },
            {
                label: '充值分成积分',
                data: dailyData.map(item => item.recharge_points),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            }
        ]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: '日期'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: '邀请用户数'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: '积分'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        },
        plugins: {
            title: {
                display: true,
                text: '邀请系统每日数据趋势'
            },
            legend: {
                display: true,
                position: 'top'
            }
        }
    }
});

// 导出数据
function exportData() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.open('invite-stats-export.php?' + params.toString(), '_blank');
}
</script>

<?php include __DIR__ . '/templates/footer.php'; ?>
