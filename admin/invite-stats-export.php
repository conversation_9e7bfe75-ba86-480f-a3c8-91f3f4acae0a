<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/login.php');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();

// 获取参数
$dateFrom = $_GET['date_from'] ?? date('Y-m-01');
$dateTo = $_GET['date_to'] ?? date('Y-m-d');
$format = $_GET['format'] ?? 'csv'; // csv 或 excel

// 构建时间条件
$timeCondition = "DATE(ie.created_at) BETWEEN :date_from AND :date_to";
$timeParams = ['date_from' => $dateFrom, 'date_to' => $dateTo];

// 获取详细数据
$data = $db->fetchAll("
    SELECT 
        ie.id,
        ie.type,
        ie.level,
        ie.points_earned,
        ie.recharge_amount,
        ie.commission_rate,
        ie.created_at,
        inviter.username as inviter_username,
        inviter.nickname as inviter_nickname,
        invitee.username as invitee_username,
        invitee.nickname as invitee_nickname
    FROM {$db->getPrefix()}invite_earnings ie
    LEFT JOIN {$db->getPrefix()}users inviter ON ie.inviter_id = inviter.id
    LEFT JOIN {$db->getPrefix()}users invitee ON ie.invitee_id = invitee.id
    WHERE {$timeCondition}
    ORDER BY ie.created_at DESC
", $timeParams);

// 设置文件名
$filename = "invite_stats_{$dateFrom}_to_{$dateTo}";

if ($format === 'csv') {
    // CSV导出
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // 写入表头
    fputcsv($output, [
        'ID',
        '邀请人用户名',
        '邀请人昵称',
        '被邀请人用户名',
        '被邀请人昵称',
        '类型',
        '层级',
        '获得积分',
        '充值金额',
        '分成比例',
        '创建时间'
    ]);
    
    // 写入数据
    foreach ($data as $row) {
        $typeNames = [
            'register' => '注册奖励',
            'recharge' => '充值分成',
            'level2_register' => '二级注册奖励',
            'level2_recharge' => '二级充值分成'
        ];
        
        fputcsv($output, [
            $row['id'],
            $row['inviter_username'] ?: '',
            $row['inviter_nickname'] ?: '',
            $row['invitee_username'] ?: '',
            $row['invitee_nickname'] ?: '',
            $typeNames[$row['type']] ?? $row['type'],
            $row['level'] == 1 ? '一级' : '二级',
            $row['points_earned'],
            $row['recharge_amount'] ?: '',
            $row['commission_rate'] ? $row['commission_rate'] . '%' : '',
            $row['created_at']
        ]);
    }
    
    fclose($output);
    
} else {
    // Excel导出 (HTML格式，Excel可以打开)
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    
    echo "\xEF\xBB\xBF"; // BOM
    
    echo '<table border="1">';
    echo '<tr>';
    echo '<th>ID</th>';
    echo '<th>邀请人用户名</th>';
    echo '<th>邀请人昵称</th>';
    echo '<th>被邀请人用户名</th>';
    echo '<th>被邀请人昵称</th>';
    echo '<th>类型</th>';
    echo '<th>层级</th>';
    echo '<th>获得积分</th>';
    echo '<th>充值金额</th>';
    echo '<th>分成比例</th>';
    echo '<th>创建时间</th>';
    echo '</tr>';
    
    $typeNames = [
        'register' => '注册奖励',
        'recharge' => '充值分成',
        'level2_register' => '二级注册奖励',
        'level2_recharge' => '二级充值分成'
    ];
    
    foreach ($data as $row) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($row['id']) . '</td>';
        echo '<td>' . htmlspecialchars($row['inviter_username'] ?: '') . '</td>';
        echo '<td>' . htmlspecialchars($row['inviter_nickname'] ?: '') . '</td>';
        echo '<td>' . htmlspecialchars($row['invitee_username'] ?: '') . '</td>';
        echo '<td>' . htmlspecialchars($row['invitee_nickname'] ?: '') . '</td>';
        echo '<td>' . htmlspecialchars($typeNames[$row['type']] ?? $row['type']) . '</td>';
        echo '<td>' . ($row['level'] == 1 ? '一级' : '二级') . '</td>';
        echo '<td>' . htmlspecialchars($row['points_earned']) . '</td>';
        echo '<td>' . htmlspecialchars($row['recharge_amount'] ?: '') . '</td>';
        echo '<td>' . htmlspecialchars($row['commission_rate'] ? $row['commission_rate'] . '%' : '') . '</td>';
        echo '<td>' . htmlspecialchars($row['created_at']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
}

exit;
?>
