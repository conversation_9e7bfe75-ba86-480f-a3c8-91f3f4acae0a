<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin');
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$pageTitle = '数据统计';
$currentPage = 'statistics';

// 获取统计数据
$stats = [
    'users' => [
        'total' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users") ?: 0,
        'today' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE DATE(created_at) = CURDATE()") ?: 0,
        'week' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)") ?: 0,
        'month' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0
    ],
    'albums' => [
        'total' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums") ?: 0,
        'today' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE DATE(created_at) = CURDATE()") ?: 0,
        'week' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)") ?: 0,
        'month' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}albums WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0
    ],
    'videos' => [
        'total' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}videos") ?: 0,
        'today' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}videos WHERE DATE(created_at) = CURDATE()") ?: 0,
        'week' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}videos WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)") ?: 0,
        'month' => $db->fetchColumn("SELECT COUNT(*) FROM {$db->getPrefix()}videos WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)") ?: 0
    ]
];

// 获取用户组统计
$userGroupStats = $db->fetchAll("
    SELECT ug.group_name, COUNT(u.id) as user_count 
    FROM {$db->getPrefix()}user_groups ug 
    LEFT JOIN {$db->getPrefix()}users u ON ug.group_key = u.user_group 
    GROUP BY ug.id 
    ORDER BY user_count DESC
");

// 获取分类统计
$categoryStats = $db->fetchAll("
    SELECT c.name, COUNT(a.id) as album_count 
    FROM {$db->getPrefix()}categories c 
    LEFT JOIN {$db->getPrefix()}albums a ON c.id = a.category_id 
    GROUP BY c.id 
    ORDER BY album_count DESC 
    LIMIT 10
");

// 获取最近7天的注册统计
$dailyStats = $db->fetchAll("
    SELECT DATE(created_at) as date, COUNT(*) as count 
    FROM {$db->getPrefix()}users 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
    GROUP BY DATE(created_at) 
    ORDER BY date ASC
");

// 获取热门套图
$popularAlbums = $db->fetchAll("
    SELECT title, view_count, created_at 
    FROM {$db->getPrefix()}albums 
    ORDER BY view_count DESC 
    LIMIT 10
");

// 获取系统信息
$systemInfo = [
    'php_version' => PHP_VERSION,
    'mysql_version' => $db->fetchColumn("SELECT VERSION()"),
    'server_time' => date('Y-m-d H:i:s'),
    'disk_usage' => getDiskUsage(),
    'memory_usage' => getMemoryUsage()
];

function getDiskUsage() {
    try {
        $path = '/www/wwwroot/www.liapian.com';
        $total = disk_total_space($path);
        $free = disk_free_space($path);

        if ($total === false || $free === false) {
            return [
                'total' => '未知',
                'used' => '未知',
                'free' => '未知',
                'percent' => 0
            ];
        }

        $used = $total - $free;
        return [
            'total' => formatBytes($total),
            'used' => formatBytes($used),
            'free' => formatBytes($free),
            'percent' => $total > 0 ? round(($used / $total) * 100, 2) : 0
        ];
    } catch (Exception $e) {
        return [
            'total' => '未知',
            'used' => '未知',
            'free' => '未知',
            'percent' => 0
        ];
    }
}

function getMemoryUsage() {
    $memory = memory_get_usage(true);
    $peak = memory_get_peak_usage(true);
    return [
        'current' => formatBytes($memory),
        'peak' => formatBytes($peak)
    ];
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

include __DIR__ . '/templates/header.php';
?>

<!-- 概览统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['users']['total']); ?></h4>
                        <p class="mb-0">总用户数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <hr class="my-2">
                <small>今日新增: <?php echo $stats['users']['today']; ?> | 本周: <?php echo $stats['users']['week']; ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['albums']['total']); ?></h4>
                        <p class="mb-0">总套图数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-images fa-2x"></i>
                    </div>
                </div>
                <hr class="my-2">
                <small>今日新增: <?php echo $stats['albums']['today']; ?> | 本周: <?php echo $stats['albums']['week']; ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo number_format($stats['videos']['total']); ?></h4>
                        <p class="mb-0">总视频数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-video fa-2x"></i>
                    </div>
                </div>
                <hr class="my-2">
                <small>今日新增: <?php echo $stats['videos']['today']; ?> | 本周: <?php echo $stats['videos']['week']; ?></small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?php echo $systemInfo['disk_usage']['percent']; ?>%</h4>
                        <p class="mb-0">磁盘使用率</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
                <hr class="my-2">
                <small>已用: <?php echo $systemInfo['disk_usage']['used']; ?> / <?php echo $systemInfo['disk_usage']['total']; ?></small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 用户组统计 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-tag me-2"></i>用户组分布</h5>
            </div>
            <div class="card-body">
                <?php if (empty($userGroupStats)): ?>
                    <p class="text-muted">暂无数据</p>
                <?php else: ?>
                    <?php foreach ($userGroupStats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo htmlspecialchars($stat['group_name']); ?></span>
                            <span class="badge bg-primary"><?php echo number_format($stat['user_count']); ?></span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 分类统计 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>热门分类</h5>
            </div>
            <div class="card-body">
                <?php if (empty($categoryStats)): ?>
                    <p class="text-muted">暂无数据</p>
                <?php else: ?>
                    <?php foreach ($categoryStats as $stat): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><?php echo htmlspecialchars($stat['name']); ?></span>
                            <span class="badge bg-info"><?php echo number_format($stat['album_count']); ?></span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <!-- 热门套图 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-fire me-2"></i>热门套图</h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($popularAlbums)): ?>
                    <div class="p-3">
                        <p class="text-muted mb-0">暂无数据</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>标题</th>
                                    <th>浏览量</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($popularAlbums as $album): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars(mb_substr($album['title'], 0, 30)); ?></td>
                                        <td><span class="badge bg-danger"><?php echo number_format($album['view_count']); ?></span></td>
                                        <td><?php echo date('m-d', strtotime($album['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- 系统信息 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-server me-2"></i>系统信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td>PHP版本</td>
                        <td><?php echo $systemInfo['php_version']; ?></td>
                    </tr>
                    <tr>
                        <td>MySQL版本</td>
                        <td><?php echo $systemInfo['mysql_version']; ?></td>
                    </tr>
                    <tr>
                        <td>服务器时间</td>
                        <td><?php echo $systemInfo['server_time']; ?></td>
                    </tr>
                    <tr>
                        <td>内存使用</td>
                        <td><?php echo $systemInfo['memory_usage']['current']; ?> (峰值: <?php echo $systemInfo['memory_usage']['peak']; ?>)</td>
                    </tr>
                    <tr>
                        <td>磁盘空间</td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" style="width: <?php echo $systemInfo['disk_usage']['percent']; ?>%">
                                    <?php echo $systemInfo['disk_usage']['percent']; ?>%
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 最近7天注册趋势 -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>最近7天注册趋势</h5>
            </div>
            <div class="card-body">
                <?php if (empty($dailyStats)): ?>
                    <p class="text-muted">暂无数据</p>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($dailyStats as $stat): ?>
                            <div class="col-md text-center">
                                <div class="mb-2">
                                    <div class="bg-primary text-white rounded p-2">
                                        <strong><?php echo $stat['count']; ?></strong>
                                    </div>
                                </div>
                                <small class="text-muted"><?php echo date('m-d', strtotime($stat['date'])); ?></small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/templates/footer.php'; ?>
