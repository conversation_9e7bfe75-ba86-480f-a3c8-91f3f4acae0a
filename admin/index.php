<?php
session_start();

require_once __DIR__ . '/../core/Database.php';

$error = '';
$success = '';

// 处理登录
if (isset($_POST['action']) && $_POST['action'] === 'login') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = '请输入用户名和密码';
    } else {
        $db = Database::getInstance();
        
        // 查询管理员
        $admin = $db->fetch(
            "SELECT * FROM {$db->getPrefix()}admin_users WHERE username = :username AND status = 1",
            ['username' => $username]
        );
        
        if (!$admin) {
            $error = '管理员账号不存在';
        } elseif (!password_verify($password, $admin['password'])) {
            $error = '密码错误';
        } else {
            // 登录成功
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_role'] = $admin['role'];
            
            // 更新登录信息
            $db->update('admin_users', [
                'last_login_time' => date('Y-m-d H:i:s'),
                'last_login_ip' => $_SERVER['REMOTE_ADDR'] ?? ''
            ], 'id = :id', ['id' => $admin['id']]);
            
            header('Location: /admin/dashboard.php');
            exit;
        }
    }
}

// 检查是否已登录
if (isset($_SESSION['admin_id'])) {
    // 如果是通过 /admin 访问的，重定向到 dashboard
    if ($_SERVER['REQUEST_URI'] === '/admin' || $_SERVER['REQUEST_URI'] === '/admin/') {
        header('Location: /admin/dashboard.php');
        exit;
    }
}
?>
<?php
require_once __DIR__ . '/../core/Config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台登录 - <?php echo Config::getSiteName(); ?></title>
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: #e9ecef;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }
        .card {
            background-color: #212529;
            border: 1px solid #495057;
        }
        .card-header {
            background-color: #343a40;
            border-bottom: 1px solid #495057;
        }
        .form-control {
            background-color: #212529;
            border-color: #495057;
            color: #e9ecef;
        }
        .form-control:focus {
            background-color: #212529;
            border-color: #ffc107;
            color: #e9ecef;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }
        .text-warning {
            color: #ffc107 !important;
        }
        .input-group-text {
            background-color: #343a40;
            border-color: #495057;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="text-center mb-4">
                <h2 class="text-warning">
                    <i class="fas fa-shield-alt me-2"></i><?php echo Config::getSiteName(); ?>
                </h2>
                <p class="text-muted">管理后台</p>
            </div>
            
            <div class="card">
                <div class="card-header text-center">
                    <h5 class="mb-0 text-warning">管理员登录</h5>
                </div>
                <div class="card-body">
                    <?php if ($error): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                    <?php endif; ?>
                    
                    <form method="post">
                        <input type="hidden" name="action" value="login">
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" 
                                       placeholder="请输入管理员用户名" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>登录后台
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            默认账号：admin / admin123<br>
                            <strong class="text-warning">登录后请及时修改密码！</strong>
                        </small>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="/" class="text-muted text-decoration-none">
                            <i class="fas fa-home me-1"></i>返回网站首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>
