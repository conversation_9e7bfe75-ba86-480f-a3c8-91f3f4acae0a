<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /admin/');
    exit;
}

require_once __DIR__ . '/../../core/Database.php';

$db = Database::getInstance();
$message = '';
$messageType = 'success';

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $feedbackId = intval($_POST['feedback_id'] ?? 0);
        $content = trim($_POST['content'] ?? '');
        $isInternal = isset($_POST['is_internal']) ? 1 : 0;
        $updateStatus = isset($_POST['update_status']);
        $newStatus = $_POST['new_status'] ?? '';
        $adminId = $_SESSION['admin_id'];
        
        // 验证必填字段
        if (!$feedbackId || empty($content)) {
            throw new Exception('请填写所有必填字段');
        }
        
        // 验证工单是否存在
        $feedback = $db->fetch("
            SELECT * FROM {$db->getPrefix()}feedback 
            WHERE id = :id
        ", ['id' => $feedbackId]);
        
        if (!$feedback) {
            throw new Exception('工单不存在');
        }
        
        // 检查工单状态是否允许回复
        if (in_array($feedback['status'], ['resolved', 'closed'])) {
            throw new Exception('此工单已' . ($feedback['status'] === 'resolved' ? '解决' : '关闭') . '，无法继续回复');
        }
        
        // 开始事务
        $db->beginTransaction();
        
        try {
            // 插入回复记录
            $result = $db->insert('feedback_replies', [
                'feedback_id' => $feedbackId,
                'admin_id' => $adminId,
                'content' => $content,
                'is_internal' => $isInternal
            ]);
            
            if (!$result) {
                throw new Exception('回复插入失败');
            }
            
            // 如果需要更新状态
            if ($updateStatus && !empty($newStatus)) {
                $validStatuses = ['pending', 'processing', 'resolved', 'closed'];
                if (!in_array($newStatus, $validStatuses)) {
                    throw new Exception('无效的状态');
                }
                
                $oldStatus = $feedback['status'];
                
                // 更新工单状态
                $statusResult = $db->update('feedback', 
                    ['status' => $newStatus], 
                    'id = :id', 
                    ['id' => $feedbackId]
                );
                
                if (!$statusResult) {
                    throw new Exception('状态更新失败');
                }
                
                // 记录状态变更日志
                $db->insert('feedback_status_logs', [
                    'feedback_id' => $feedbackId,
                    'admin_id' => $adminId,
                    'old_status' => $oldStatus,
                    'new_status' => $newStatus,
                    'note' => '管理员回复时更新状态'
                ]);
            }
            
            // 提交事务
            $db->commit();
            
            $message = '回复提交成功';
            if ($updateStatus && !empty($newStatus)) {
                $message .= '，状态已更新';
            }
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } else {
        throw new Exception('不支持的请求方法');
    }
    
} catch (Exception $e) {
    $message = $e->getMessage();
    $messageType = 'danger';
    error_log("后台工单回复API错误: " . $e->getMessage());
}

// 重定向回工单详情页
$feedbackId = intval($_POST['feedback_id'] ?? 0);
if ($feedbackId > 0) {
    $redirectUrl = "/admin/feedback-detail.php?id=" . $feedbackId;
    if ($messageType === 'success') {
        $redirectUrl .= "&success=" . urlencode($message);
    } else {
        $redirectUrl .= "&error=" . urlencode($message);
    }
    header('Location: ' . $redirectUrl);
} else {
    header('Location: /admin/feedback.php');
}
exit;
?>
