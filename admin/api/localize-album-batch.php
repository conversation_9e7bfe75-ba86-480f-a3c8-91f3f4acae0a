<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 设置JSON响应头
header('Content-Type: application/json');

// 设置执行时间限制
set_time_limit(120); // 2分钟内完成每批
ini_set('memory_limit', '256M');

require_once __DIR__ . '/../../core/Database.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }

    $albumId = intval($_POST['album_id'] ?? 0);
    $batchIndex = intval($_POST['batch_index'] ?? 0);
    $batchSize = intval($_POST['batch_size'] ?? 10); // 每批处理10张图片

    if ($albumId <= 0) {
        throw new Exception('无效的套图ID');
    }

    $db = Database::getInstance();

    // 获取套图信息
    $album = $db->fetch("SELECT * FROM {$db->getPrefix()}albums WHERE id = :id", ['id' => $albumId]);
    if (!$album) {
        throw new Exception('套图不存在');
    }

    // 获取未本地化的图片列表
    $offset = $batchIndex * $batchSize;
    $images = $db->fetchAll("
        SELECT * FROM {$db->getPrefix()}album_images 
        WHERE album_id = :album_id 
        AND (local_file_url IS NULL OR local_file_url = '')
        ORDER BY sort ASC, id ASC
        LIMIT :limit OFFSET :offset
    ", [
        'album_id' => $albumId,
        'limit' => $batchSize,
        'offset' => $offset
    ]);

    // 获取总的未本地化图片数量
    $totalUnlocalized = $db->fetchColumn("
        SELECT COUNT(*) FROM {$db->getPrefix()}album_images 
        WHERE album_id = :album_id 
        AND (local_file_url IS NULL OR local_file_url = '')
    ", ['album_id' => $albumId]);

    if (empty($images)) {
        echo json_encode([
            'success' => true,
            'completed' => true,
            'message' => '没有需要本地化的图片',
            'batch_index' => $batchIndex,
            'processed' => 0,
            'remaining' => 0
        ]);
        exit;
    }

    // 处理这批图片
    require_once __DIR__ . '/../../core/FTPUploadManager.php';

    try {
        $ftpUploader = new FTPUploadManager();
    } catch (Exception $e) {
        throw new Exception('FTP配置错误: ' . $e->getMessage());
    }

    $successCount = 0;
    $failedCount = 0;
    $processedImages = [];

    foreach ($images as $index => $image) {
        $globalIndex = $offset + $index + 1;
        
        try {
            error_log("开始处理图片 {$globalIndex}: {$image['file_url']}");
            
            // 下载图片
            $tempFile = downloadImageWithRetry($image['file_url']);
            if (!$tempFile) {
                $failedCount++;
                $processedImages[] = [
                    'id' => $image['id'],
                    'index' => $globalIndex,
                    'success' => false,
                    'error' => '下载失败'
                ];
                error_log("图片 {$globalIndex} 下载失败");
                continue;
            }

            // 验证图片
            if (!$ftpUploader->validateImage($tempFile)) {
                unlink($tempFile);
                $failedCount++;
                $processedImages[] = [
                    'id' => $image['id'],
                    'index' => $globalIndex,
                    'success' => false,
                    'error' => '图片文件无效'
                ];
                error_log("图片 {$globalIndex} 验证失败");
                continue;
            }

            // WebP转换（如果启用）
            $webpEnabled = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'enable_webp_convert'");
            if ($webpEnabled && $webpEnabled['value'] == '1') {
                $webpFile = convertToWebP($tempFile);
                if ($webpFile) {
                    unlink($tempFile);
                    $tempFile = $webpFile;
                    error_log("图片 {$globalIndex} WebP转换成功");
                }
            }

            // 上传到FTP（带重试）
            $remotePath = null;
            $uploadRetries = 3;
            
            for ($retry = 0; $retry < $uploadRetries; $retry++) {
                try {
                    $remotePath = $ftpUploader->uploadFile($tempFile, 'album', [
                        'album_id' => $albumId,
                        'index' => $globalIndex
                    ]);
                    
                    if ($remotePath) {
                        break; // 上传成功，跳出重试循环
                    }
                } catch (Exception $e) {
                    error_log("图片 {$globalIndex} FTP上传重试 " . ($retry + 1) . " 失败: " . $e->getMessage());
                    if ($retry < $uploadRetries - 1) {
                        sleep(2); // 等待2秒后重试
                        $ftpUploader->reconnect(); // 重新连接FTP
                    }
                }
            }

            // 清理临时文件
            unlink($tempFile);

            if ($remotePath) {
                // 更新数据库
                $result = $db->update('album_images', 
                    ['local_file_url' => $remotePath], 
                    'id = :id', 
                    ['id' => $image['id']]
                );

                if ($result) {
                    $successCount++;
                    $processedImages[] = [
                        'id' => $image['id'],
                        'index' => $globalIndex,
                        'success' => true,
                        'path' => $remotePath
                    ];
                    error_log("图片 {$globalIndex} 本地化成功: {$remotePath}");
                } else {
                    $failedCount++;
                    $processedImages[] = [
                        'id' => $image['id'],
                        'index' => $globalIndex,
                        'success' => false,
                        'error' => '数据库更新失败'
                    ];
                    error_log("图片 {$globalIndex} 数据库更新失败");
                }
            } else {
                $failedCount++;
                $processedImages[] = [
                    'id' => $image['id'],
                    'index' => $globalIndex,
                    'success' => false,
                    'error' => 'FTP上传失败'
                ];
                error_log("图片 {$globalIndex} FTP上传最终失败");
            }

        } catch (Exception $e) {
            $failedCount++;
            $processedImages[] = [
                'id' => $image['id'],
                'index' => $globalIndex,
                'success' => false,
                'error' => $e->getMessage()
            ];
            error_log("图片 {$globalIndex} 处理异常: " . $e->getMessage());
        }

        // 每张图片处理后暂停1秒，避免服务器压力
        sleep(1);
        
        // 强制垃圾回收
        if (($index + 1) % 3 == 0) {
            gc_collect_cycles();
        }
    }

    $remainingCount = $totalUnlocalized - ($offset + count($images));
    $isCompleted = $remainingCount <= 0;

    // 如果完成了，更新套图状态
    if ($isCompleted) {
        $totalLocalizedCount = $db->fetchColumn("
            SELECT COUNT(*) FROM {$db->getPrefix()}album_images 
            WHERE album_id = :album_id 
            AND local_file_url IS NOT NULL AND local_file_url != ''
        ", ['album_id' => $albumId]);

        $db->update('albums', [
            'localization_status' => 'completed',
            'localized_count' => $totalLocalizedCount
        ], 'id = :id', ['id' => $albumId]);
    }

    echo json_encode([
        'success' => true,
        'completed' => $isCompleted,
        'batch_index' => $batchIndex,
        'processed' => count($images),
        'success_count' => $successCount,
        'failed_count' => $failedCount,
        'remaining' => max(0, $remainingCount),
        'images' => $processedImages,
        'message' => "批次 " . ($batchIndex + 1) . " 处理完成，成功: {$successCount}，失败: {$failedCount}"
    ]);

} catch (Exception $e) {
    error_log("分批本地化错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// 辅助函数
function downloadImageWithRetry($imageUrl, $maxRetries = 3) {
    for ($i = 0; $i < $maxRetries; $i++) {
        $tempFile = downloadImage($imageUrl);
        if ($tempFile) {
            return $tempFile;
        }
        if ($i < $maxRetries - 1) {
            sleep(2);
        }
    }
    return false;
}

function downloadImage($imageUrl) {
    $extension = getImageExtensionFromUrl($imageUrl);
    $tempFile = tempnam(sys_get_temp_dir(), 'album_img_') . '.' . $extension;

    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'method' => 'GET',
            'header' => [
                'Connection: close',
                'Accept: image/*'
            ]
        ]
    ]);

    try {
        $imageData = @file_get_contents($imageUrl, false, $context);
        if ($imageData === false) {
            if (file_exists($tempFile)) unlink($tempFile);
            return false;
        }

        if (file_put_contents($tempFile, $imageData) === false) {
            if (file_exists($tempFile)) unlink($tempFile);
            return false;
        }

        unset($imageData);
        return $tempFile;
        
    } catch (Exception $e) {
        if (file_exists($tempFile)) unlink($tempFile);
        return false;
    }
}

function getImageExtensionFromUrl($imageUrl) {
    $parsedUrl = parse_url($imageUrl);
    $path = $parsedUrl['path'] ?? '';
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
    
    $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
    if (in_array($extension, $validExtensions)) {
        return $extension;
    }
    
    return 'jpg';
}

function convertToWebP($sourceFile) {
    if (!function_exists('imagewebp')) {
        return false;
    }

    try {
        $imageInfo = getimagesize($sourceFile);
        if (!$imageInfo) return false;

        $mimeType = $imageInfo['mime'];
        $image = null;

        switch ($mimeType) {
            case 'image/jpeg':
                $image = @imagecreatefromjpeg($sourceFile);
                break;
            case 'image/png':
                $image = @imagecreatefrompng($sourceFile);
                if ($image) {
                    imagealphablending($image, false);
                    imagesavealpha($image, true);
                }
                break;
            case 'image/gif':
                $image = @imagecreatefromgif($sourceFile);
                break;
            case 'image/webp':
                return $sourceFile;
            default:
                return false;
        }

        if (!$image) return false;

        $webpFile = preg_replace('/\.[^.]+$/', '.webp', $sourceFile);
        $success = imagewebp($image, $webpFile, 85);
        imagedestroy($image);

        return $success ? $webpFile : false;
        
    } catch (Exception $e) {
        return false;
    }
}
?>
