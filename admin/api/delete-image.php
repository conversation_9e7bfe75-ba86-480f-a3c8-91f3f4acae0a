<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';

$db = Database::getInstance();

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$imageId = intval($input['image_id'] ?? 0);
$albumId = intval($input['album_id'] ?? 0);

if (!$imageId || !$albumId) {
    echo json_encode(['success' => false, 'error' => '参数无效']);
    exit;
}

try {
    // 获取图片信息
    $image = $db->fetch("
        SELECT * FROM {$db->getPrefix()}album_images 
        WHERE id = :id AND album_id = :album_id
    ", ['id' => $imageId, 'album_id' => $albumId]);
    
    if (!$image) {
        echo json_encode(['success' => false, 'error' => '图片不存在']);
        exit;
    }
    
    // 删除数据库记录
    $result = $db->delete('album_images', 'id = :id', ['id' => $imageId]);
    
    if ($result) {
        // 删除物理文件（如果存在）
        if (!empty($image['file_path']) && file_exists(__DIR__ . '/../../' . $image['file_path'])) {
            unlink(__DIR__ . '/../../' . $image['file_path']);
        }
        
        // 更新套图图片数量
        $imageCount = $db->fetchColumn("
            SELECT COUNT(*) FROM {$db->getPrefix()}album_images 
            WHERE album_id = :album_id
        ", ['album_id' => $albumId]);
        
        $db->update('albums', 
            ['image_count' => $imageCount], 
            'id = :id', 
            ['id' => $albumId]
        );
        
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '删除失败']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
