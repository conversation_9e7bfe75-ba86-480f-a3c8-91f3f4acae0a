<?php
session_start();

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误
ini_set('log_errors', 1);

// 检查是否需要流式输出
$streamOutput = isset($_POST['stream']) && $_POST['stream'] == '1';

if ($streamOutput) {
    // 设置流式输出头部
    header('Content-Type: text/plain; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Pragma: no-cache');
    header('Expires: 0');
    header('X-Accel-Buffering: no'); // 禁用nginx缓冲

    // 彻底禁用输出缓冲
    if (ob_get_level()) {
        ob_end_clean();
    }
    ob_implicit_flush(true);

    // 发送初始数据确保连接建立
    echo "data: " . json_encode(['type' => 'init', 'message' => 'Connection established']) . "\n\n";
    flush();
} else {
    header('Content-Type: application/json');
}

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 设置执行时间限制
set_time_limit(600); // 10分钟
ini_set('memory_limit', '512M');

// 禁用输出缓冲，允许实时输出
if (ob_get_level()) {
    ob_end_flush();
}

// 发送keep-alive头，防止连接超时
header('Connection: keep-alive');

require_once __DIR__ . '/../../core/AlbumLocalizationManager.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }

    $albumId = intval($_POST['album_id'] ?? 0);
    if ($albumId <= 0) {
        throw new Exception('无效的套图ID');
    }

    // 检查是否使用队列模式
    $useQueue = isset($_POST['use_queue']) && $_POST['use_queue'] == '1';

    // 记录开始日志
    error_log("开始本地化套图: {$albumId}, 队列模式: " . ($useQueue ? '是' : '否'));

    try {
        $localizationManager = new AlbumLocalizationManager();
        error_log("AlbumLocalizationManager创建成功");

        if ($useQueue) {
            // 队列模式 - 添加到队列
            $result = $localizationManager->localizeAlbum($albumId, null, true);
            echo json_encode($result);

        } else if ($streamOutput) {
            // 流式输出模式
            error_log("开始流式输出模式");

            // 发送初始调试信息
            echo "data: " . json_encode(['type' => 'debug', 'message' => 'API开始处理']) . "\n\n";
            flush();

            $result = $localizationManager->localizeAlbumWithProgress($albumId, function($progress) {
                // 只记录关键进度，减少日志量
                if ($progress['type'] === 'start' || $progress['type'] === 'progress') {
                    error_log("进度: " . $progress['type'] . " - " . ($progress['current'] ?? 0) . "/" . ($progress['total'] ?? 0));
                }

                // 使用统一的数据格式
                echo "data: " . json_encode($progress, JSON_UNESCAPED_UNICODE) . "\n\n";

                // 强制刷新输出
                flush();

                // 减少延迟，提高响应速度
                usleep(10000); // 0.01秒
            });

            error_log("localizeAlbumWithProgress执行完成，结果: " . json_encode($result));
        } else {
            // 普通模式
            $result = $localizationManager->localizeAlbum($albumId);
        }

        error_log("localizeAlbum方法执行完成");

        // 记录结果日志
        error_log("本地化结果: " . json_encode($result));
    } catch (Exception $e) {
        error_log("AlbumLocalizationManager执行错误: " . $e->getMessage());
        error_log("错误位置: " . $e->getFile() . ":" . $e->getLine());
        throw $e;
    }

    // 确保输出结果
    if (!$result) {
        throw new Exception('本地化管理器返回空结果');
    }

    if ($streamOutput) {
        // 流式输出最终结果
        echo "data: " . json_encode($result) . "\n\n";
        flush();
    } else {
        echo json_encode($result);
    }

} catch (Exception $e) {
    error_log("套图本地化API错误: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
} catch (Error $e) {
    error_log("套图本地化API致命错误: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '系统错误: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
