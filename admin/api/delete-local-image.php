<?php
session_start();

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误
ini_set('log_errors', 1);

header('Content-Type: application/json');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';
require_once __DIR__ . '/../../core/FTPUploadManager.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }
    
    $imageId = intval($_POST['image_id'] ?? 0);
    if ($imageId <= 0) {
        throw new Exception('无效的图片ID');
    }
    
    $db = Database::getInstance();
    
    // 获取图片信息
    $image = $db->fetch("
        SELECT * FROM {$db->getPrefix()}album_images 
        WHERE id = :id
    ", ['id' => $imageId]);
    
    if (!$image) {
        throw new Exception('图片不存在');
    }
    
    if (empty($image['local_file_url'])) {
        throw new Exception('图片未本地化');
    }
    
    // 删除FTP上的文件
    try {
        $ftpUploader = new FTPUploadManager();
        $ftpUploader->deleteFile($image['local_file_url']);
    } catch (Exception $e) {
        // 忽略FTP删除失败的错误，继续清理数据库
        error_log("删除FTP文件失败: " . $e->getMessage());
    }
    
    // 清空数据库中的本地化URL
    $result = $db->update('album_images', 
        ['local_file_url' => null], 
        'id = :id', 
        ['id' => $imageId]
    );
    
    if (!$result) {
        throw new Exception('数据库更新失败');
    }
    
    echo json_encode([
        'success' => true,
        'message' => '本地化图片删除成功'
    ]);
    
} catch (Exception $e) {
    error_log("删除本地化图片API错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
