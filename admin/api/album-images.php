<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';

$db = Database::getInstance();

// 处理POST请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $albumId = intval($input['album_id'] ?? 0);

    if (!$albumId) {
        echo json_encode(['success' => false, 'message' => '套图ID无效']);
        exit;
    }

    try {
        switch ($action) {
            case 'add_url':
                $imageUrl = trim($input['image_url'] ?? '');
                if (!$imageUrl) {
                    echo json_encode(['success' => false, 'message' => '图片链接不能为空']);
                    exit;
                }

                // 验证URL格式
                if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    echo json_encode(['success' => false, 'message' => '图片链接格式无效']);
                    exit;
                }

                // 获取当前最大排序值
                $maxSort = $db->fetchColumn("SELECT MAX(sort) FROM {$db->getPrefix()}album_images WHERE album_id = :album_id", ['album_id' => $albumId]) ?: 0;

                // 添加图片记录
                $result = $db->insert('album_images', [
                    'album_id' => $albumId,
                    'image_url' => $imageUrl,
                    'sort' => $maxSort + 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '图片添加成功']);
                } else {
                    echo json_encode(['success' => false, 'message' => '图片添加失败']);
                }
                break;

            case 'add_batch_urls':
                $imageUrls = $input['image_urls'] ?? [];
                if (empty($imageUrls)) {
                    echo json_encode(['success' => false, 'message' => '图片链接列表不能为空']);
                    exit;
                }

                // 验证所有URL格式
                foreach ($imageUrls as $url) {
                    if (!filter_var($url, FILTER_VALIDATE_URL)) {
                        echo json_encode(['success' => false, 'message' => '包含无效的图片链接：' . $url]);
                        exit;
                    }
                }

                // 获取当前最大排序值
                $maxSort = $db->fetchColumn("SELECT MAX(sort) FROM {$db->getPrefix()}album_images WHERE album_id = :album_id", ['album_id' => $albumId]) ?: 0;

                $count = 0;
                foreach ($imageUrls as $imageUrl) {
                    $maxSort++;
                    $result = $db->insert('album_images', [
                        'album_id' => $albumId,
                        'image_url' => $imageUrl,
                        'sort' => $maxSort,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                    if ($result) {
                        $count++;
                    }
                }

                echo json_encode(['success' => true, 'message' => '批量添加完成', 'count' => $count]);
                break;

            case 'localize_remote':
                // 获取所有远程图片
                $remoteImages = $db->fetchAll("
                    SELECT * FROM {$db->getPrefix()}album_images
                    WHERE album_id = :album_id
                    AND (image_url LIKE 'http://%' OR image_url LIKE 'https://%')
                    AND image_url NOT LIKE '%{$_SERVER['HTTP_HOST']}%'
                ", ['album_id' => $albumId]);

                $count = 0;
                foreach ($remoteImages as $image) {
                    $localPath = downloadAndSaveImage($image['image_url']);
                    if ($localPath) {
                        $db->update('album_images',
                            ['image_url' => $localPath],
                            'id = :id',
                            ['id' => $image['id']]
                        );
                        $count++;
                    }
                }

                echo json_encode(['success' => true, 'message' => '本地化完成', 'count' => $count]);
                break;

            default:
                echo json_encode(['success' => false, 'message' => '未知操作']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 处理GET请求 - 获取图片列表
$albumId = intval($_GET['album_id'] ?? 0);

if (!$albumId) {
    echo json_encode(['success' => false, 'error' => '套图ID无效']);
    exit;
}

try {
    // 获取套图图片列表
    $images = $db->fetchAll("
        SELECT *, file_url as image_url FROM {$db->getPrefix()}album_images
        WHERE album_id = :album_id
        ORDER BY sort ASC, id ASC
    ", ['album_id' => $albumId]);

    echo json_encode([
        'success' => true,
        'images' => $images
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// 下载并保存远程图片的函数
function downloadAndSaveImage($url) {
    try {
        // 创建上传目录
        $uploadDir = __DIR__ . '/../../uploads/albums/' . date('Y/m/');
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 获取文件扩展名
        $pathInfo = pathinfo(parse_url($url, PHP_URL_PATH));
        $extension = $pathInfo['extension'] ?? 'jpg';

        // 生成文件名
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // 下载图片
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);

        $imageData = file_get_contents($url, false, $context);
        if ($imageData === false) {
            return false;
        }

        // 保存文件
        if (file_put_contents($filepath, $imageData)) {
            return '/uploads/albums/' . date('Y/m/') . $filename;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}
?>
