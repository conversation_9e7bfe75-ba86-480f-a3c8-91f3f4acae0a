<?php
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';

$db = Database::getInstance();

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$imageId = intval($input['image_id'] ?? 0);
$sort = intval($input['sort'] ?? 0);

if (!$imageId || $sort < 1) {
    echo json_encode(['success' => false, 'error' => '参数无效']);
    exit;
}

try {
    // 更新图片排序
    $result = $db->update('album_images', 
        ['sort' => $sort], 
        'id = :id', 
        ['id' => $imageId]
    );
    
    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
