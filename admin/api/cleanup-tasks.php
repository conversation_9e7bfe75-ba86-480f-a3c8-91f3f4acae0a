<?php
session_start();

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

header('Content-Type: application/json');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }
    
    $db = Database::getInstance();
    
    // 获取所有未完成的任务
    $pendingTasks = $db->fetchAll("
        SELECT * FROM {$db->getPrefix()}localization_tasks 
        WHERE status IN ('pending', 'processing')
    ");
    
    $cleanedCount = 0;
    
    foreach ($pendingTasks as $task) {
        // 检查任务创建时间，如果超过1小时则认为是僵尸任务
        $createdTime = strtotime($task['created_at']);
        $currentTime = time();
        $timeDiff = $currentTime - $createdTime;
        
        if ($timeDiff > 3600) { // 1小时 = 3600秒
            // 更新任务状态为失败
            $db->update('localization_tasks', 
                [
                    'status' => 'failed',
                    'error_log' => '任务超时自动清理',
                    'updated_at' => date('Y-m-d H:i:s')
                ], 
                'id = :id', 
                ['id' => $task['id']]
            );
            $cleanedCount++;
        }
    }
    
    // 如果有指定的套图ID，强制清理该套图的任务
    $albumId = intval($_POST['album_id'] ?? 0);
    if ($albumId > 0) {
        $result = $db->update('localization_tasks',
            [
                'status' => 'failed',
                'error_log' => '手动强制清理',
                'updated_at' => date('Y-m-d H:i:s')
            ],
            'album_id = :album_id AND status IN (\'pending\', \'processing\')',
            ['album_id' => $albumId]
        );
        
        if ($result) {
            $cleanedCount++;
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => "清理完成，共清理了 {$cleanedCount} 个任务",
        'cleaned_count' => $cleanedCount
    ]);
    
} catch (Exception $e) {
    error_log("清理任务API错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
