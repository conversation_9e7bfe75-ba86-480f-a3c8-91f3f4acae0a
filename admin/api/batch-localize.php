<?php
session_start();

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误
ini_set('log_errors', 1);

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 设置输出格式
header('Content-Type: text/plain; charset=utf-8');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

// 设置执行时间限制
set_time_limit(0); // 无限制
ini_set('memory_limit', '512M');

// 禁用输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

require_once __DIR__ . '/../../core/AlbumLocalizationManager.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }
    
    $localizationManager = new AlbumLocalizationManager();
    
    // 开始批量本地化
    $localizationManager->batchLocalizeAll();
    
} catch (Exception $e) {
    error_log("批量本地化API错误: " . $e->getMessage());
    
    echo json_encode([
        'type' => 'error',
        'error' => $e->getMessage()
    ]) . "\n";
}
?>
