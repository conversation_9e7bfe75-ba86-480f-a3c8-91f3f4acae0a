<?php
session_start();

// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误处理
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误
ini_set('log_errors', 1);

header('Content-Type: application/json');

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once __DIR__ . '/../../core/Database.php';
require_once __DIR__ . '/../../core/FTPUploadManager.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('不支持的请求方法');
    }
    
    $imageId = intval($_POST['image_id'] ?? 0);
    if ($imageId <= 0) {
        throw new Exception('无效的图片ID');
    }
    
    $db = Database::getInstance();
    
    // 获取图片信息
    $image = $db->fetch("
        SELECT * FROM {$db->getPrefix()}album_images 
        WHERE id = :id
    ", ['id' => $imageId]);
    
    if (!$image) {
        throw new Exception('图片不存在');
    }
    
    if (!empty($image['local_file_url'])) {
        throw new Exception('图片已经本地化');
    }
    
    // 下载图片
    $tempFile = tempnam(sys_get_temp_dir(), 'single_img_');
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $imageData = @file_get_contents($image['file_url'], false, $context);
    if ($imageData === false) {
        throw new Exception('下载图片失败');
    }
    
    if (file_put_contents($tempFile, $imageData) === false) {
        throw new Exception('保存临时文件失败');
    }
    
    // 验证图片
    $ftpUploader = new FTPUploadManager();
    if (!$ftpUploader->validateImage($tempFile)) {
        unlink($tempFile);
        throw new Exception('图片文件无效');
    }
    
    // 上传到FTP
    $remotePath = $ftpUploader->uploadFile($tempFile, 'album', [
        'album_id' => $image['album_id'],
        'index' => $image['sort'] ?: $imageId
    ]);
    
    // 清理临时文件
    unlink($tempFile);
    
    if (!$remotePath) {
        throw new Exception('FTP上传失败');
    }
    
    // 更新数据库
    $result = $db->update('album_images', 
        ['local_file_url' => $remotePath], 
        'id = :id', 
        ['id' => $imageId]
    );
    
    if (!$result) {
        throw new Exception('数据库更新失败');
    }
    
    echo json_encode([
        'success' => true,
        'message' => '图片本地化成功',
        'local_url' => $remotePath
    ]);
    
} catch (Exception $e) {
    error_log("单张图片本地化API错误: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
