<?php
session_start();

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Cache.php';

$db = Database::getInstance();
$pageTitle = '视频管理';
$currentPage = 'videos';

// 处理操作
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create':
            // 处理文件大小（从MB转换为字节）
            $fileSizeMB = floatval($_POST['file_size_mb'] ?? 0);
            $fileSizeBytes = $fileSizeMB * 1024 * 1024;

            $data = [
                'title' => trim($_POST['title'] ?? ''),
                'video_url' => trim($_POST['video_url'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'category_id' => intval($_POST['category_id'] ?? 0),
                'tags' => trim($_POST['tags'] ?? ''),
                'duration' => intval($_POST['duration'] ?? 0),
                'file_size' => intval($fileSizeBytes),
                'view_count' => intval($_POST['view_count'] ?? 0),
                'is_free' => intval($_POST['is_free'] ?? 0),
                'status' => intval($_POST['status'] ?? 0),
                'slug' => generateSlug($_POST['title'] ?? ''),
                'created_at' => date('Y-m-d H:i:s')
            ];

            if (empty($data['title'])) {
                $message = '视频标题不能为空';
                $messageType = 'danger';
            } elseif (empty($data['video_url'])) {
                $message = '视频URL不能为空';
                $messageType = 'danger';
            } else {
                // 处理缩略图
                $thumbnailUrl = '';
                $thumbnailType = $_POST['thumbnail_type'] ?? 'upload';
                if ($thumbnailType === 'url' && !empty($_POST['thumbnail_url'])) {
                    $thumbnailUrl = trim($_POST['thumbnail_url']);
                } elseif ($thumbnailType === 'upload' && isset($_FILES['thumbnail_file']) && $_FILES['thumbnail_file']['error'] === 0) {
                    // 这里应该处理文件上传，暂时留空
                    // $thumbnailUrl = handleFileUpload($_FILES['thumbnail_file']);
                }

                if ($thumbnailUrl) {
                    $data['cover'] = $thumbnailUrl;
                }

                $videoId = $db->insert('videos', $data);
                if ($videoId) {
                    // 清除相关缓存（如果有的话）
                    try {
                        $cache = Cache::getInstance();
                        if (!empty($data['slug'])) {
                            $cache->delete("video_detail_{$data['slug']}");
                        }
                    } catch (Exception $cacheError) {
                        error_log('Cache clear error on create: ' . $cacheError->getMessage());
                    }

                    $message = '视频创建成功';
                    $messageType = 'success';
                    header("Location: /admin/videos.php?action=edit&id=$videoId");
                    exit;
                } else {
                    $message = '视频创建失败';
                    $messageType = 'danger';
                }
            }
            break;

        case 'update':
            $videoId = intval($_POST['video_id']);
            if ($videoId > 0) {
                // 处理文件大小（从MB转换为字节）
                $fileSizeMB = floatval($_POST['file_size_mb'] ?? 0);
                $fileSizeBytes = $fileSizeMB * 1024 * 1024;

                $data = [
                    'title' => trim($_POST['title'] ?? ''),
                    'video_url' => trim($_POST['video_url'] ?? ''),
                    'description' => trim($_POST['description'] ?? ''),
                    'category_id' => intval($_POST['category_id'] ?? 0),
                    'tags' => trim($_POST['tags'] ?? ''),
                    'duration' => intval($_POST['duration'] ?? 0),
                    'file_size' => intval($fileSizeBytes),
                    'view_count' => intval($_POST['view_count'] ?? 0),
                    'is_free' => intval($_POST['is_free'] ?? 0),
                    'status' => intval($_POST['status'] ?? 0),
                    'slug' => generateSlug($_POST['title'] ?? ''),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($data['title'])) {
                    $message = '视频标题不能为空';
                    $messageType = 'danger';
                } elseif (empty($data['video_url'])) {
                    $message = '视频URL不能为空';
                    $messageType = 'danger';
                } else {
                    // 处理缩略图
                    $thumbnailType = $_POST['thumbnail_type'] ?? 'upload';
                    if ($thumbnailType === 'url' && !empty($_POST['thumbnail_url'])) {
                        $data['cover'] = trim($_POST['thumbnail_url']);
                    } elseif ($thumbnailType === 'upload' && isset($_FILES['thumbnail_file']) && $_FILES['thumbnail_file']['error'] === 0) {
                        // 这里应该处理文件上传，暂时留空
                        // $data['cover'] = handleFileUpload($_FILES['thumbnail_file']);
                    }

                    try {
                        // 调试信息
                        error_log('Updating video ID: ' . $videoId);
                        error_log('Update data: ' . json_encode($data));

                        $stmt = $db->update('videos', $data, 'id = :id', ['id' => $videoId]);
                        if ($stmt && $stmt->rowCount() >= 0) {
                            // 清除视频缓存
                            try {
                                $cache = Cache::getInstance();
                                $cache->delete("video_detail_{$videoId}");
                                // 如果有slug，也清除slug缓存
                                if (!empty($data['slug'])) {
                                    $cache->delete("video_detail_{$data['slug']}");
                                }
                                error_log('Video cache cleared for ID: ' . $videoId);
                            } catch (Exception $cacheError) {
                                error_log('Cache clear error: ' . $cacheError->getMessage());
                            }

                            $message = '视频更新成功';
                            $messageType = 'success';
                            error_log('Video update successful, affected rows: ' . $stmt->rowCount());
                        } else {
                            $message = '视频更新失败：未找到要更新的记录';
                            $messageType = 'danger';
                            error_log('Video update failed: no rows affected');
                        }
                    } catch (Exception $e) {
                        $message = '视频更新失败：' . $e->getMessage();
                        $messageType = 'danger';
                        error_log('Video update error: ' . $e->getMessage());
                    }
                }
            }
            break;

        case 'delete':
            $videoId = intval($_POST['video_id']);
            if ($videoId > 0) {
                // 在删除前获取视频信息以清除缓存
                $videoInfo = $db->fetch("SELECT slug FROM {$db->getPrefix()}videos WHERE id = :id", ['id' => $videoId]);

                $result = $db->delete('videos', 'id = :id', ['id' => $videoId]);
                if ($result) {
                    // 清除视频缓存
                    try {
                        $cache = Cache::getInstance();
                        $cache->delete("video_detail_{$videoId}");
                        if ($videoInfo && !empty($videoInfo['slug'])) {
                            $cache->delete("video_detail_{$videoInfo['slug']}");
                        }
                        error_log('Video cache cleared for deleted video ID: ' . $videoId);
                    } catch (Exception $cacheError) {
                        error_log('Cache clear error on delete: ' . $cacheError->getMessage());
                    }

                    $message = '视频删除成功';
                    $messageType = 'success';
                } else {
                    $message = '视频删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'batch_delete':
            $videoIds = $_POST['video_ids'] ?? [];
            if (!empty($videoIds)) {
                $placeholders = str_repeat('?,', count($videoIds) - 1) . '?';
                $result = $db->query("DELETE FROM {$db->getPrefix()}videos WHERE id IN ($placeholders)", $videoIds);
                if ($result) {
                    $message = '批量删除成功，共删除 ' . count($videoIds) . ' 个视频';
                    $messageType = 'success';
                } else {
                    $message = '批量删除失败';
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'update_status':
            $videoId = intval($_POST['video_id']);
            $status = intval($_POST['status']);
            if ($videoId > 0) {
                $result = $db->update('videos', ['status' => $status], 'id = :id', ['id' => $videoId]);
                if ($result) {
                    $message = '视频状态更新成功';
                    $messageType = 'success';
                } else {
                    $message = '视频状态更新失败';
                    $messageType = 'danger';
                }
            }
            break;
    }
}

// 获取搜索参数
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';
$isFree = $_GET['is_free'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$where = '1=1';
$params = [];

if ($search) {
    $where .= ' AND (v.title LIKE :search OR v.description LIKE :search OR v.tags LIKE :search)';
    $params['search'] = "%$search%";
}

if ($category) {
    $where .= ' AND v.category_id = :category';
    $params['category'] = $category;
}

if ($status !== '') {
    $where .= ' AND v.status = :status';
    $params['status'] = $status;
}

if ($isFree !== '') {
    $where .= ' AND v.is_free = :is_free';
    $params['is_free'] = $isFree;
}

// 获取视频列表
$sql = "SELECT v.*, c.name as category_name
        FROM {$db->getPrefix()}videos v 
        LEFT JOIN {$db->getPrefix()}categories c ON v.category_id = c.id 
        WHERE $where 
        ORDER BY v.created_at DESC 
        LIMIT $limit OFFSET $offset";

$videos = $db->fetchAll($sql, $params);

// 获取总数
$countSql = "SELECT COUNT(*) FROM {$db->getPrefix()}videos v WHERE $where";
$total = $db->fetchColumn($countSql, $params);
$totalPages = ceil($total / $limit);

// 获取分类列表 - 修复字段名
$categories = $db->fetchAll("SELECT * FROM {$db->getPrefix()}categories WHERE status = 1 AND type = 'video' ORDER BY sort ASC");

// 如果没有视频分类，获取所有分类
if (empty($categories)) {
    $categories = $db->fetchAll("SELECT * FROM {$db->getPrefix()}categories WHERE status = 1 ORDER BY sort ASC");
}

// 处理添加和编辑页面
$action = $_GET['action'] ?? 'list';
$editVideo = null;

if ($action === 'edit') {
    $videoId = intval($_GET['id'] ?? 0);
    if ($videoId > 0) {
        $editVideo = $db->fetch("SELECT * FROM {$db->getPrefix()}videos WHERE id = :id", ['id' => $videoId]);
        if (!$editVideo) {
            header('Location: /admin/videos.php');
            exit;
        }
    }
}

include __DIR__ . '/templates/header.php';
?>

<?php if ($message): ?>
<div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
    <?php echo htmlspecialchars($message); ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- 添加/编辑视频页面 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?php echo $action === 'add' ? '添加视频' : '编辑视频'; ?></h1>
        <a href="/admin/videos.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>返回列表
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="<?php echo $action === 'add' ? 'create' : 'update'; ?>">
                <?php if ($editVideo): ?>
                    <input type="hidden" name="video_id" value="<?php echo $editVideo['id']; ?>">
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label class="form-label">视频标题 <span class="text-danger">*</span></label>
                            <input type="text" name="title" class="form-control"
                                   value="<?php echo htmlspecialchars($editVideo['title'] ?? ''); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">视频URL <span class="text-danger">*</span></label>
                            <input type="url" name="video_url" class="form-control"
                                   value="<?php echo htmlspecialchars($editVideo['video_url'] ?? ''); ?>"
                                   placeholder="https://example.com/video.mp4" required>
                            <small class="text-muted">支持MP4、M3U8等格式的视频链接</small>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">视频描述</label>
                            <textarea name="description" class="form-control" rows="4"><?php echo htmlspecialchars($editVideo['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">请选择分类</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo $category['id']; ?>"
                                                    <?php echo ($editVideo['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($category['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">标签</label>
                                    <input type="text" name="tags" class="form-control"
                                           value="<?php echo htmlspecialchars($editVideo['tags'] ?? ''); ?>"
                                           placeholder="用逗号分隔多个标签">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">视频时长（秒）</label>
                                    <input type="number" name="duration" class="form-control" min="0"
                                           value="<?php echo htmlspecialchars($editVideo['duration'] ?? '0'); ?>"
                                           placeholder="视频时长（秒）">
                                    <small class="text-muted">视频总时长，单位：秒</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">文件大小（MB）</label>
                                    <input type="number" name="file_size_mb" class="form-control" min="0" step="0.01"
                                           value="<?php echo $editVideo ? round(($editVideo['file_size'] ?? 0) / 1024 / 1024, 2) : ''; ?>"
                                           placeholder="文件大小（MB）">
                                    <small class="text-muted">视频文件大小，单位：MB</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">浏览量</label>
                                    <input type="number" name="view_count" class="form-control" min="0"
                                           value="<?php echo htmlspecialchars($editVideo['view_count'] ?? '0'); ?>"
                                           placeholder="浏览量">
                                    <small class="text-muted">视频浏览次数</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_free" value="1" class="form-check-input" id="is_free"
                                               <?php echo ($editVideo['is_free'] ?? 0) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_free">免费视频</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" name="status" value="1" class="form-check-input" id="status"
                                               <?php echo ($editVideo['status'] ?? 0) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="status">发布视频</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label class="form-label">视频缩略图</label>
                            <div class="border rounded p-3">
                                <div class="text-center mb-3">
                                    <?php if ($editVideo && isset($editVideo['cover']) && $editVideo['cover']): ?>
                                        <img src="<?php echo htmlspecialchars($editVideo['cover']); ?>"
                                             class="img-fluid rounded mb-2" style="max-height: 200px;" id="thumbnailPreview">
                                    <?php else: ?>
                                        <div id="thumbnailPreview">
                                            <i class="fas fa-video fa-3x text-muted mb-2"></i>
                                            <p class="text-muted mb-2">暂无缩略图</p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- 缩略图选择方式 -->
                                <div class="mb-3">
                                    <?php $hasExistingThumbnail = $editVideo && isset($editVideo['cover']) && $editVideo['cover']; ?>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="thumbnail_type" id="thumbnail_upload" value="upload" <?php echo !$hasExistingThumbnail ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="thumbnail_upload">上传图片</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="thumbnail_type" id="thumbnail_url" value="url" <?php echo $hasExistingThumbnail ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="thumbnail_url">图片链接</label>
                                    </div>
                                </div>

                                <!-- 上传文件 -->
                                <div id="upload_section">
                                    <input type="file" name="thumbnail_file" class="form-control mb-2" accept="image/*" onchange="previewThumbnail(this)">
                                    <small class="text-muted">支持 JPG、PNG 格式，建议尺寸 16:9</small>
                                </div>

                                <!-- URL输入 -->
                                <div id="url_section" style="display: none;">
                                    <input type="url" name="thumbnail_url" class="form-control mb-2"
                                           placeholder="https://example.com/image.jpg"
                                           value="<?php echo htmlspecialchars($editVideo['cover'] ?? ''); ?>"
                                           onchange="previewThumbnailUrl(this)">
                                    <small class="text-muted">输入图片链接地址</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i><?php echo $action === 'add' ? '创建视频' : '保存修改'; ?>
                    </button>
                    <a href="/admin/videos.php" class="btn btn-outline-secondary">取消</a>
                </div>
            </form>
        </div>
    </div>

<?php else: ?>
    <!-- 视频列表页面 -->
<!-- 搜索和筛选 -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">搜索视频</label>
                <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="标题/描述/标签">
            </div>
            <div class="col-md-2">
                <label class="form-label">分类</label>
                <select class="form-select" name="category">
                    <option value="">全部分类</option>
                    <?php foreach ($categories as $cat): ?>
                        <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($cat['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">状态</label>
                <select class="form-select" name="status">
                    <option value="">全部状态</option>
                    <option value="1" <?php echo $status === '1' ? 'selected' : ''; ?>>已发布</option>
                    <option value="0" <?php echo $status === '0' ? 'selected' : ''; ?>>未发布</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">收费状态</label>
                <select class="form-select" name="is_free">
                    <option value="">全部</option>
                    <option value="1" <?php echo $isFree === '1' ? 'selected' : ''; ?>>免费</option>
                    <option value="0" <?php echo $isFree === '0' ? 'selected' : ''; ?>>收费</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                    <a href="/admin/videos.php?action=add" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>添加视频
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 视频列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-video me-2"></i>视频列表 
            <span class="badge bg-secondary"><?php echo number_format($total); ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-danger btn-sm batch-action" disabled onclick="batchDelete()">
                <i class="fas fa-trash me-1"></i>批量删除
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($videos)): ?>
            <div class="text-center py-5">
                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                <p class="text-muted">暂无视频数据</p>
                <a href="/admin/videos.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>添加第一个视频
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>视频信息</th>
                            <th>分类</th>
                            <th>时长</th>
                            <th>大小</th>
                            <th>浏览量</th>
                            <th>收费状态</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th width="150">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($videos as $video): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input row-select" value="<?php echo $video['id']; ?>">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php if (isset($video['cover']) && $video['cover']): ?>
                                            <img src="<?php echo htmlspecialchars($video['cover']); ?>"
                                                 class="rounded me-3" width="80" height="60" style="object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center"
                                                 style="width: 80px; height: 60px;">
                                                <i class="fas fa-video text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($video['title']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars(mb_substr($video['description'], 0, 50)); ?>...</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($video['category_name']): ?>
                                        <span class="badge bg-info"><?php echo htmlspecialchars($video['category_name']); ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($video['duration']): ?>
                                        <?php echo gmdate('H:i:s', $video['duration']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($video['file_size']): ?>
                                        <?php echo formatFileSize($video['file_size']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($video['view_count']); ?></td>
                                <td>
                                    <?php if ($video['is_free']): ?>
                                        <span class="badge bg-success">免费</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">收费</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($video['status']): ?>
                                        <span class="badge bg-success">已发布</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">未发布</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($video['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="/admin/videos.php?action=edit&id=<?php echo $video['id']; ?>" class="btn btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="/video/<?php echo htmlspecialchars($video['slug']); ?>" target="_blank" class="btn btn-outline-info" title="预览">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-<?php echo $video['status'] ? 'warning' : 'success'; ?>" 
                                                onclick="toggleStatus(<?php echo $video['id']; ?>, <?php echo $video['status'] ? 0 : 1; ?>)" 
                                                title="<?php echo $video['status'] ? '下架' : '发布'; ?>">
                                            <i class="fas fa-<?php echo $video['status'] ? 'eye-slash' : 'eye'; ?>"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteVideo(<?php echo $video['id']; ?>)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 分页 -->
<?php if ($totalPages > 1): ?>
<nav class="mt-4">
    <ul class="pagination justify-content-center">
        <?php if ($page > 1): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>">上一页</a>
            </li>
        <?php endif; ?>
        
        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>"><?php echo $i; ?></a>
            </li>
        <?php endfor; ?>
        
        <?php if ($page < $totalPages): ?>
            <li class="page-item">
                <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>&is_free=<?php echo $isFree; ?>">下一页</a>
            </li>
        <?php endif; ?>
    </ul>
</nav>
<?php endif; ?>

<?php endif; ?>

<?php
// 文件大小格式化函数
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

// 生成URL友好的slug
function generateSlug($title) {
    // 检查输入参数
    if (empty($title)) {
        return 'video-' . time();
    }

    // 简单的slug生成，实际项目中可能需要更复杂的处理
    $slug = strtolower(trim($title));

    // 移除特殊字符，保留字母、数字、中文和连字符
    // 使用mb_string函数处理中文字符
    $slug = preg_replace('/[^\w\-\x{4e00}-\x{9fa5}]/u', '-', $slug);

    // 合并多个连续的连字符
    $slug = preg_replace('/-+/', '-', $slug);

    // 移除首尾的连字符
    $slug = trim($slug, '-');

    // 如果结果为空，返回默认值
    return $slug ?: 'video-' . time();
}

// 通用JavaScript代码（所有页面都需要）
$commonScript = "
    // 缩略图相关功能
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Video admin JavaScript loaded');
        // 缩略图选择方式切换
        const thumbnailRadios = document.querySelectorAll('input[name=\"thumbnail_type\"]');
        const uploadSection = document.getElementById('upload_section');
        const urlSection = document.getElementById('url_section');

        if (thumbnailRadios.length > 0 && uploadSection && urlSection) {
            // 初始化显示状态
            function updateThumbnailSections() {
                const selectedType = document.querySelector('input[name=\"thumbnail_type\"]:checked');
                if (selectedType && selectedType.value === 'url') {
                    uploadSection.style.display = 'none';
                    urlSection.style.display = 'block';
                } else {
                    uploadSection.style.display = 'block';
                    urlSection.style.display = 'none';
                }
            }

            // 页面加载时初始化
            updateThumbnailSections();

            thumbnailRadios.forEach(radio => {
                radio.addEventListener('change', updateThumbnailSections);
            });
        }
    });

    // 预览上传的缩略图
    function previewThumbnail(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('thumbnailPreview').innerHTML =
                    '<img src=\"' + e.target.result + '\" class=\"img-fluid rounded mb-2\" style=\"max-height: 200px;\">';
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // 预览URL缩略图
    function previewThumbnailUrl(input) {
        if (input.value) {
            const img = document.createElement('img');
            img.src = input.value;
            img.className = 'img-fluid rounded mb-2';
            img.style.maxHeight = '200px';
            img.onerror = function() {
                document.getElementById('thumbnailPreview').innerHTML =
                    '<i class=\"fas fa-video fa-3x text-muted mb-2\"></i><p class=\"text-muted mb-2\">图片加载失败</p>';
            };
            img.onload = function() {
                document.getElementById('thumbnailPreview').innerHTML = '';
                document.getElementById('thumbnailPreview').appendChild(img);
            };
        }
    }
";

// 只有在列表页面才需要的JavaScript
if ($action === 'list') {
    $pageScript = $commonScript . "
        function deleteVideo(videoId) {
        if (confirmDelete('确定要删除这个视频吗？删除后无法恢复！')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete\"><input type=\"hidden\" name=\"video_id\" value=\"' + videoId + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function toggleStatus(videoId, status) {
        const action = status ? '发布' : '下架';
        if (confirm('确定要' + action + '这个视频吗？')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"update_status\"><input type=\"hidden\" name=\"video_id\" value=\"' + videoId + '\"><input type=\"hidden\" name=\"status\" value=\"' + status + '\">';
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function batchDelete() {
        const selectedIds = getSelectedIds();
        if (selectedIds.length === 0) {
            showWarning('请选择要删除的视频');
            return;
        }
        
        if (confirmBatchAction('删除', selectedIds.length)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"batch_delete\">';
            selectedIds.forEach(id => {
                form.innerHTML += '<input type=\"hidden\" name=\"video_ids[]\" value=\"' + id + '\">';
            });
            document.body.appendChild(form);
            form.submit();
        }
    }


    // 修复分类下拉框显示问题
    document.addEventListener('DOMContentLoaded', function() {
        // 确保分类下拉框正常显示
        const categorySelect = document.querySelector('select[name=\"category\"]');
        if (categorySelect && categorySelect.options.length <= 1) {
            console.warn('分类数据可能未正确加载');
        }

    });
";
} else {
    // 添加/编辑页面只需要通用JavaScript
    $pageScript = $commonScript;
}

include __DIR__ . '/templates/footer.php';
?>
