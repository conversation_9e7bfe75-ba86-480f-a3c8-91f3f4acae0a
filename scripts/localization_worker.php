<?php
/**
 * 本地化队列工作进程
 * 用法: php scripts/localization_worker.php [worker_name]
 */

// 设置为CLI模式
if (php_sapi_name() !== 'cli') {
    die('此脚本只能在命令行模式下运行');
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内存和时间限制
ini_set('memory_limit', '256M');
set_time_limit(0);

// 引入必要的文件
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/FTPUploadManager.php';
require_once __DIR__ . '/../core/LocalizationQueueManager.php';

class LocalizationWorker {
    private $workerName;
    private $queueManager;
    private $ftpUploader;
    private $running = true;
    private $processedCount = 0;
    private $enableWebP = false;
    
    public function __construct($workerName = null) {
        $this->workerName = $workerName ?: 'worker_' . getmypid();
        $this->queueManager = new LocalizationQueueManager();
        $this->ftpUploader = new FTPUploadManager();
        
        // 检查WebP设置
        $db = Database::getInstance();
        $setting = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'enable_webp_convert'");
        $this->enableWebP = $setting && $setting['value'] == '1';
        
        // 注册信号处理器
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
        }
        
        echo "[" . date('Y-m-d H:i:s') . "] 工作进程 {$this->workerName} 启动\n";
        echo "[" . date('Y-m-d H:i:s') . "] WebP转换: " . ($this->enableWebP ? '启用' : '禁用') . "\n";
    }
    
    public function run() {
        while ($this->running) {
            try {
                // 获取下一个任务
                $task = $this->queueManager->getNextTask($this->workerName);
                
                if (!$task) {
                    // 没有任务，等待5秒
                    echo "[" . date('Y-m-d H:i:s') . "] 没有待处理任务，等待中...\n";
                    sleep(5);
                    continue;
                }
                
                echo "[" . date('Y-m-d H:i:s') . "] 开始处理任务 #{$task['id']} - 图片ID: {$task['image_id']}\n";
                
                // 处理任务
                $result = $this->processTask($task);
                
                if ($result['success']) {
                    $this->queueManager->markTaskCompleted(
                        $task['id'], 
                        $result['local_file_url'], 
                        $result['file_size'] ?? null
                    );
                    $this->processedCount++;
                    echo "[" . date('Y-m-d H:i:s') . "] 任务 #{$task['id']} 完成 - {$result['local_file_url']}\n";
                } else {
                    $this->queueManager->markTaskFailed($task['id'], $result['error']);
                    echo "[" . date('Y-m-d H:i:s') . "] 任务 #{$task['id']} 失败 - {$result['error']}\n";
                }
                
                // 短暂休息，避免过度占用资源
                usleep(100000); // 0.1秒
                
            } catch (Exception $e) {
                echo "[" . date('Y-m-d H:i:s') . "] 处理异常: " . $e->getMessage() . "\n";
                sleep(1);
            }
            
            // 处理信号
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }
        }
        
        echo "[" . date('Y-m-d H:i:s') . "] 工作进程 {$this->workerName} 停止，共处理 {$this->processedCount} 个任务\n";
    }
    
    private function processTask($task) {
        try {
            // 1. 下载图片
            $tempFile = $this->downloadImage($task['image_url']);
            if (!$tempFile) {
                return ['success' => false, 'error' => '图片下载失败'];
            }
            
            // 2. 验证图片
            if (!$this->ftpUploader->validateImage($tempFile)) {
                unlink($tempFile);
                return ['success' => false, 'error' => '图片文件无效'];
            }
            
            // 3. WebP转换（如果启用）
            if ($this->enableWebP) {
                $webpFile = $this->convertToWebP($tempFile);
                if ($webpFile) {
                    unlink($tempFile);
                    $tempFile = $webpFile;
                }
            }
            
            // 4. 上传到FTP
            $uploadResult = $this->ftpUploader->uploadFile($tempFile);
            unlink($tempFile);
            
            if (!$uploadResult['success']) {
                return ['success' => false, 'error' => $uploadResult['error']];
            }
            
            return [
                'success' => true,
                'local_file_url' => $uploadResult['path'],
                'file_size' => $uploadResult['size'] ?? null
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private function downloadImage($imageUrl) {
        $extension = $this->getImageExtensionFromUrl($imageUrl);
        $tempFile = tempnam(sys_get_temp_dir(), 'queue_img_') . '.' . $extension;
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $imageData = @file_get_contents($imageUrl, false, $context);
        
        if ($imageData === false) {
            return false;
        }
        
        if (file_put_contents($tempFile, $imageData) === false) {
            return false;
        }
        
        return $tempFile;
    }
    
    private function getImageExtensionFromUrl($imageUrl) {
        $parsedUrl = parse_url($imageUrl);
        $path = $parsedUrl['path'] ?? '';
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        if (in_array($extension, $validExtensions)) {
            return $extension;
        }
        
        return 'jpg';
    }
    
    private function convertToWebP($sourceFile) {
        if (!function_exists('imagewebp')) {
            return false;
        }
        
        $imageInfo = getimagesize($sourceFile);
        if (!$imageInfo) {
            return false;
        }
        
        $mimeType = $imageInfo['mime'];
        $image = null;
        
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($sourceFile);
                break;
            case 'image/png':
                $image = imagecreatefrompng($sourceFile);
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($sourceFile);
                break;
            case 'image/webp':
                return $sourceFile;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        $webpFile = preg_replace('/\.[^.]+$/', '.webp', $sourceFile);
        $success = imagewebp($image, $webpFile, 85);
        imagedestroy($image);
        
        return $success ? $webpFile : false;
    }
    
    public function handleSignal($signal) {
        echo "[" . date('Y-m-d H:i:s') . "] 收到信号 {$signal}，准备停止...\n";
        $this->running = false;
    }
}

// 获取工作进程名称
$workerName = $argv[1] ?? null;

// 创建并运行工作进程
$worker = new LocalizationWorker($workerName);
$worker->run();
?>
