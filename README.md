# 丽片网 - 美女套图+视频网站

一个基于PHP+MySQL开发的专业美女套图和视频网站系统，支持多级会员体系、积分充值、内容采集、火车头发布等功能。

## 系统特性

### 核心功能
- ✅ 美女套图和视频管理
- ✅ 多级会员权限体系
- ✅ 积分充值和消费系统
- ✅ 用户收藏和浏览记录
- ✅ 内容分类和标签管理
- ✅ 响应式前端设计（支持PC/平板/手机）
- ✅ 火车头采集器发布接口
- ✅ 后台内容采集功能
- ✅ 图片自动转WebP格式
- ✅ 远程FTP存储支持
- ✅ Redis缓存支持

### 会员体系
- **游客**: 可浏览3张免费图片
- **注册会员**: 可浏览10张图片
- **天费会员**: 1天全站图片浏览权限
- **周费会员**: 7天全站图片浏览权限
- **月费会员**: 30天全站图片浏览权限
- **年费会员**: 365天全站图片浏览权限
- **三年会员**: 3年全站图片+视频+批量下载权限
- **永久会员**: 永久全站图片+视频+批量下载权限

### 技术栈
- **后端**: PHP 8.3
- **数据库**: MySQL 5.7
- **缓存**: Redis 7.4
- **前端**: Bootstrap 5 + jQuery
- **服务器**: Debian 12 + 宝塔面板 + Nginx

## 安装说明

### 环境要求
- PHP 8.3+ (需要GD扩展、Redis扩展、FTP扩展)
- MySQL 5.7+
- Redis 7.4+
- Nginx/Apache
- 支持URL重写

### 安装步骤

1. **上传代码**
   ```bash
   # 将所有文件上传到网站根目录
   # 确保文件权限正确
   chmod -R 755 /www/wwwroot/www.liapian.com
   ```

2. **配置数据库**
   ```bash
   # 编辑数据库配置文件
   vim config/database.php
   
   # 确认数据库信息正确：
   # 数据库名: www_liapian_com
   # 用户名: www_liapian_com
   # 密码: LF4NFSiMy36eWim3
   ```

3. **安装数据库**
   ```bash
   # 访问安装页面
   https://www.liapian.com/install
   
   # 点击"开始安装数据库"按钮
   # 安装完成后会显示默认管理员账号
   ```

4. **配置Nginx**
   ```nginx
   server {
       listen 443 ssl http2;
       server_name www.liapian.com;
       root /www/wwwroot/www.liapian.com;
       index index.php index.html;
       
       # SSL配置...
       
       location / {
           try_files $uri $uri/ @rewrite;
       }
       
       location @rewrite {
           rewrite ^/(.*)$ /index.php?_url=/$1;
       }
       
       location ~ \.php$ {
           fastcgi_pass unix:/tmp/php-cgi-83.sock;
           fastcgi_index index.php;
           include fastcgi.conf;
       }
       
       # 禁止访问敏感目录
       location ~ ^/(config|core|models|database)/ {
           deny all;
       }
   }
   ```

5. **配置权限**
   ```bash
   # 设置上传目录权限
   mkdir -p uploads/albums
   chmod -R 777 uploads/
   
   # 设置缓存目录权限
   mkdir -p cache
   chmod -R 777 cache/
   ```

## 配置说明

### 基础配置
访问管理后台进行以下配置：

1. **网站信息**
   - 网站名称
   - 网站描述
   - 网站关键词

2. **会员设置**
   - 注册奖励积分
   - 邀请奖励积分
   - 各级会员价格

3. **FTP配置**
   - FTP服务器地址
   - FTP用户名密码
   - CDN访问地址

### 火车头发布接口

**接口地址**: `https://www.liapian.com/api/train_publish.php`

**请求方式**: POST

**请求格式**: JSON

#### 发布套图
```json
{
    "type": "album",
    "title": "套图标题",
    "description": "套图描述",
    "content": "套图内容",
    "category_id": 1,
    "tags": "美女,写真",
    "is_free": 1,
    "enable_webp": true,
    "images": [
        "http://example.com/image1.jpg",
        "http://example.com/image2.jpg"
    ]
}
```

#### 发布视频
```json
{
    "type": "video",
    "title": "视频标题",
    "description": "视频描述",
    "category_id": 2,
    "video_url": "http://example.com/video.mp4",
    "cover_url": "http://example.com/cover.jpg",
    "duration": 300,
    "is_free": 0
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 123,
        "type": "album",
        "message": "套图发布成功"
    }
}
```

## 功能使用

### 管理后台
- **访问地址**: `https://www.liapian.com/admin`
- **默认账号**: admin / 123456
- **主要功能**:
  - 内容管理（套图/视频）
  - 用户管理
  - 充值卡管理
  - 系统配置
  - 数据统计

### 内容采集
1. 进入管理后台 → 内容采集
2. 设置采集规则（目标站点、选择器等）
3. 配置图片处理选项（是否转WebP）
4. 开始自动采集

### 充值卡系统
1. 管理后台批量生成充值卡
2. 用户使用卡号卡密充值积分
3. 积分购买会员等级
4. 自动开通对应权限

### 权限控制
- 游客：浏览3张免费图片后显示注册提示
- 注册会员：浏览10张图片后提示升级
- 付费会员：根据等级享受对应权限
- 视频观看：仅三年会员及以上可观看

## 开发说明

### 目录结构
```
/www/wwwroot/www.liapian.com/
├── api/                    # API接口
├── admin/                  # 管理后台
├── assets/                 # 静态资源
│   ├── css/               # CSS样式
│   ├── js/                # JavaScript
│   └── images/            # 图片资源
├── config/                 # 配置文件
├── core/                   # 核心类库
├── database/               # 数据库文件
├── models/                 # 数据模型
├── pages/                  # 页面文件
├── templates/              # 模板文件
├── uploads/                # 上传目录
├── index.php              # 网站首页
├── install.php            # 安装脚本
└── .htaccess              # URL重写规则
```

### 二次开发
- 所有核心类都有详细注释
- 采用MVC架构，易于扩展
- 支持插件机制
- 完整的缓存体系

## 注意事项

### 安全建议
1. 安装完成后立即修改默认管理员密码
2. 定期备份数据库和重要文件
3. 保持系统更新
4. 配置防火墙和安全策略

### 性能优化
1. 启用Redis缓存
2. 配置CDN加速
3. 开启Gzip压缩
4. 优化数据库索引

### 法律合规
1. 确保所有内容符合当地法律法规
2. 设置合适的用户协议和隐私政策
3. 建立内容审核机制
4. 保护用户隐私数据

## 技术支持

如有问题，请检查以下项目：

1. **数据库连接问题**
   - 检查数据库配置信息
   - 确认数据库服务正常运行
   - 检查用户权限

2. **图片上传问题**
   - 检查目录权限
   - 确认FTP配置正确
   - 检查PHP扩展

3. **缓存问题**
   - 检查Redis服务状态
   - 确认Redis配置正确
   - 清除缓存重试

4. **权限问题**
   - 检查文件权限设置
   - 确认目录可写
   - 检查PHP权限

## 版权说明

本系统仅供学习和研究使用，请确保您的使用符合当地法律法规。使用本系统所产生的任何法律后果由使用者自行承担。

---

**丽片网 © 2025** - 专业的美女套图视频网站系统
