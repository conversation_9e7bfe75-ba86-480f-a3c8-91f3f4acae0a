<?php
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Cache.php';

/**
 * 套图模型类
 */
class Album {
    private $db;
    private $cache;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();
    }
    
    /**
     * 创建套图
     */
    public function create($data) {
        // 生成slug
        if (empty($data['slug'])) {
            $data['slug'] = $this->generateSlug($data['title']);
        }
        
        // 设置发布时间
        if ($data['status'] == 1 && empty($data['published_at'])) {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert('albums', $data);
    }
    
    /**
     * 更新套图
     */
    public function update($id, $data) {
        // 如果状态改为已发布且没有发布时间，设置发布时间
        if (isset($data['status']) && $data['status'] == 1) {
            $album = $this->getById($id);
            if ($album && !$album['published_at']) {
                $data['published_at'] = date('Y-m-d H:i:s');
            }
        }
        
        $result = $this->db->update('albums', $data, 'id = :id', ['id' => $id]);
        
        if ($result) {
            // 清除缓存
            $this->cache->delete('album:' . $id);
            $this->cache->deletePattern('albums:*');
        }
        
        return $result;
    }
    
    /**
     * 根据ID获取套图
     */
    public function getById($id) {
        $cacheKey = 'album:' . $id;
        $album = $this->cache->get($cacheKey);
        
        if ($album === false) {
            $album = $this->db->fetch(
                "SELECT a.*, c.name as category_name, c.slug as category_slug 
                 FROM {$this->db->getPrefix()}albums a 
                 LEFT JOIN {$this->db->getPrefix()}categories c ON a.category_id = c.id 
                 WHERE a.id = :id",
                ['id' => $id]
            );
            
            if ($album) {
                $this->cache->set($cacheKey, $album, 1800); // 缓存30分钟
            }
        }
        
        return $album;
    }
    
    /**
     * 根据slug获取套图
     */
    public function getBySlug($slug) {
        return $this->db->fetch(
            "SELECT a.*, c.name as category_name, c.slug as category_slug 
             FROM {$this->db->getPrefix()}albums a 
             LEFT JOIN {$this->db->getPrefix()}categories c ON a.category_id = c.id 
             WHERE a.slug = :slug AND a.status = 1",
            ['slug' => $slug]
        );
    }
    
    /**
     * 获取套图列表
     */
    public function getList($params = []) {
        $where = ['a.status = 1'];
        $bindings = [];
        
        // 分类筛选
        if (!empty($params['category_id'])) {
            $where[] = 'a.category_id = :category_id';
            $bindings['category_id'] = $params['category_id'];
        }
        
        // 搜索
        if (!empty($params['keyword'])) {
            $where[] = '(a.title LIKE :keyword1 OR a.description LIKE :keyword2 OR a.tags LIKE :keyword3)';
            $keyword = '%' . $params['keyword'] . '%';
            $bindings['keyword1'] = $keyword;
            $bindings['keyword2'] = $keyword;
            $bindings['keyword3'] = $keyword;
        }
        
        // 免费/付费筛选
        if (isset($params['is_paid'])) {
            $where[] = 'a.is_paid = :is_paid';
            $bindings['is_paid'] = $params['is_paid'];
        }
        
        // 排序
        $orderBy = 'a.created_at DESC, a.id DESC';
        if (!empty($params['order'])) {
            switch ($params['order']) {
                case 'view':
                    $orderBy = 'a.view_count DESC, a.id DESC';
                    break;
                case 'like':
                    $orderBy = 'a.like_count DESC, a.id DESC';
                    break;
                case 'favorite':
                    $orderBy = 'a.favorite_count DESC, a.id DESC';
                    break;
                case 'download':
                    $orderBy = 'a.download_count DESC, a.id DESC';
                    break;
                case 'latest':
                default:
                    $orderBy = 'a.created_at DESC, a.id DESC';
                    break;
            }
        }
        
        // 分页
        $page = max(1, intval($params['page'] ?? 1));
        $pageSize = max(1, min(100, intval($params['page_size'] ?? 20)));
        $offset = ($page - 1) * $pageSize;
        
        $whereClause = implode(' AND ', $where);
        
        // 获取总数
        $totalSql = "SELECT COUNT(*) as total FROM {$this->db->getPrefix()}albums a WHERE {$whereClause}";
        $totalResult = $this->db->fetch($totalSql, $bindings);
        $total = $totalResult ? $totalResult['total'] : 0;
        
        // 获取列表
        $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug,
                       (SELECT COUNT(*) FROM {$this->db->getPrefix()}album_images ai WHERE ai.album_id = a.id) as image_count
                FROM {$this->db->getPrefix()}albums a
                LEFT JOIN {$this->db->getPrefix()}categories c ON a.category_id = c.id
                WHERE {$whereClause}
                ORDER BY {$orderBy}
                LIMIT {$offset}, {$pageSize}";
        
        $list = $this->db->fetchAll($sql, $bindings);
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => ceil($total / $pageSize)
        ];
    }
    
    /**
     * 获取套图的图片列表
     */
    public function getImages($albumId, $page = 1, $pageSize = 20) {
        $offset = ($page - 1) * $pageSize;

        $sql = "SELECT *, file_url as url FROM {$this->db->getPrefix()}album_images
                WHERE album_id = :album_id
                ORDER BY sort ASC, id ASC
                LIMIT {$offset}, {$pageSize}";

        return $this->db->fetchAll($sql, ['album_id' => $albumId]);
    }
    
    /**
     * 获取套图图片总数
     */
    public function getImageCount($albumId) {
        $result = $this->db->fetch(
            "SELECT COUNT(*) as count FROM {$this->db->getPrefix()}album_images WHERE album_id = :album_id",
            ['album_id' => $albumId]
        );
        
        return $result['count'];
    }
    
    /**
     * 更新套图图片数量
     */
    public function updateImageCount($albumId) {
        $count = $this->getImageCount($albumId);

        $this->db->update('albums', [
            'image_count' => $count
        ], 'id = :id', ['id' => $albumId]);

        // 清除缓存
        $this->cache->delete('album:' . $albumId);
    }

    /**
     * 检查用户是否可以查看套图
     */
    public function canView($albumId, $userId = 0) {
        $album = $this->getById($albumId);
        if (!$album) {
            return false;
        }

        // 如果是免费套图，所有人都可以查看
        if ($album['is_free']) {
            return true;
        }

        // 如果未登录，不能查看付费内容
        if (!$userId) {
            return false;
        }

        // 获取用户信息
        $user = $this->db->fetch("SELECT * FROM {$this->db->getPrefix()}users WHERE id = :id", ['id' => $userId]);
        if (!$user) {
            return false;
        }

        // 检查用户是否是VIP
        if (!empty($user['vip_expire_date']) && strtotime($user['vip_expire_date']) > time()) {
            return true;
        }

        // 检查用户积分是否足够（假设每张图片需要1积分）
        $requiredPoints = max(1, $album['image_count']);
        if ($user['points'] >= $requiredPoints) {
            return true;
        }

        return false;
    }

    /**
     * 增加浏览量
     */
    public function increaseViewCount($albumId) {
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET view_count = view_count + 1 WHERE id = :id",
            ['id' => $albumId]
        );

        // 清除缓存
        $this->cache->delete('album:' . $albumId);
    }

    /**
     * 获取用户互动状态
     */
    public function getUserInteraction($albumId, $userId) {
        $interaction = [
            'is_liked' => false,
            'is_favorited' => false,
        ];

        if (!$userId) {
            return $interaction;
        }

        // 检查是否已收藏
        $favorited = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM {$this->db->getPrefix()}favorites
             WHERE user_id = :user_id AND content_type = 'album' AND content_id = :content_id",
            ['user_id' => $userId, 'content_id' => $albumId]
        );

        $interaction['is_favorited'] = $favorited > 0;

        // 检查是否已点赞
        $liked = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM {$this->db->getPrefix()}user_likes
             WHERE user_id = :user_id AND content_type = 'album' AND content_id = :content_id",
            ['user_id' => $userId, 'content_id' => $albumId]
        );

        $interaction['is_liked'] = $liked > 0;

        return $interaction;
    }

    /**
     * 获取相关套图推荐
     */
    public function getRelated($albumId, $limit = 8) {
        $album = $this->getById($albumId);
        if (!$album) {
            return [];
        }

        // 优先推荐同分类的套图
        $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
                FROM {$this->db->getPrefix()}albums a
                LEFT JOIN {$this->db->getPrefix()}categories c ON a.category_id = c.id
                WHERE a.id != :album_id AND a.status = 1 AND a.category_id = :category_id
                ORDER BY a.view_count DESC, a.created_at DESC
                LIMIT :limit";

        $related = $this->db->fetchAll($sql, [
            'album_id' => $albumId,
            'category_id' => $album['category_id'],
            'limit' => $limit
        ]);

        // 如果同分类的套图不够，补充其他套图
        if (count($related) < $limit) {
            $remaining = $limit - count($related);
            $excludeIds = array_column($related, 'id');
            $excludeIds[] = $albumId;

            $placeholders = str_repeat('?,', count($excludeIds) - 1) . '?';

            $sql = "SELECT a.*, c.name as category_name, c.slug as category_slug
                    FROM {$this->db->getPrefix()}albums a
                    LEFT JOIN {$this->db->getPrefix()}categories c ON a.category_id = c.id
                    WHERE a.id NOT IN ({$placeholders}) AND a.status = 1
                    ORDER BY a.view_count DESC, a.created_at DESC
                    LIMIT {$remaining}";

            $additional = $this->db->fetchAll($sql, $excludeIds);
            $related = array_merge($related, $additional);
        }

        return $related;
    }
    
    /**
     * 增加浏览次数
     */
    public function incrementViewCount($albumId, $userId = null) {
        // 检查是否已经浏览过（防止重复统计）
        $today = date('Y-m-d');
        $cacheKey = "view_check:{$albumId}:{$userId}:{$today}";
        
        if ($this->cache->exists($cacheKey)) {
            return false;
        }
        
        // 更新浏览次数
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET view_count = view_count + 1 WHERE id = :id",
            ['id' => $albumId]
        );
        
        // 记录浏览日志
        $this->db->insert('view_logs', [
            'user_id' => $userId,
            'target_type' => 'album',
            'target_id' => $albumId,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // 设置缓存标记（24小时）
        $this->cache->set($cacheKey, 1, 86400);
        
        // 清除缓存
        $this->cache->delete('album:' . $albumId);
        
        return true;
    }
    
    /**
     * 增加点赞数
     */
    public function incrementLikeCount($albumId) {
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET like_count = like_count + 1 WHERE id = :id",
            ['id' => $albumId]
        );
        
        $this->cache->delete('album:' . $albumId);
    }
    
    /**
     * 增加收藏数
     */
    public function incrementFavoriteCount($albumId) {
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET favorite_count = favorite_count + 1 WHERE id = :id",
            ['id' => $albumId]
        );
        
        $this->cache->delete('album:' . $albumId);
    }
    
    /**
     * 减少收藏数
     */
    public function decrementFavoriteCount($albumId) {
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET favorite_count = favorite_count - 1 WHERE id = :id AND favorite_count > 0",
            ['id' => $albumId]
        );
        
        $this->cache->delete('album:' . $albumId);
    }
    
    /**
     * 增加下载数
     */
    public function incrementDownloadCount($albumId) {
        $this->db->query(
            "UPDATE {$this->db->getPrefix()}albums SET download_count = download_count + 1 WHERE id = :id",
            ['id' => $albumId]
        );
        
        $this->cache->delete('album:' . $albumId);
    }
    
    /**
     * 批量设置收费状态
     */
    public function batchSetFreeStatus($isFree, $categoryId = null) {
        $where = '1 = 1';
        $bindings = ['is_free' => $isFree];
        
        if ($categoryId) {
            $where .= ' AND category_id = :category_id';
            $bindings['category_id'] = $categoryId;
        }
        
        $this->db->update('albums', ['is_free' => $isFree], $where, $bindings);
        
        // 清除相关缓存
        $this->cache->deletePattern('albums:*');
        $this->cache->deletePattern('album:*');
    }
    
    /**
     * 删除套图
     */
    public function delete($id) {
        $this->db->beginTransaction();
        
        try {
            // 删除套图图片
            $this->db->delete('album_images', 'album_id = :album_id', ['album_id' => $id]);
            
            // 删除收藏记录
            $this->db->delete('favorites', 'target_type = :type AND target_id = :id', [
                'type' => 'album',
                'id' => $id
            ]);
            
            // 删除浏览记录
            $this->db->delete('view_logs', 'target_type = :type AND target_id = :id', [
                'type' => 'album',
                'id' => $id
            ]);
            
            // 删除套图
            $this->db->delete('albums', 'id = :id', ['id' => $id]);
            
            $this->db->commit();
            
            // 清除缓存
            $this->cache->delete('album:' . $id);
            $this->cache->deletePattern('albums:*');
            
            return true;
        } catch (Exception $e) {
            $this->db->rollback();
            return false;
        }
    }
    
    /**
     * 生成slug
     */
    private function generateSlug($title) {
        // 移除特殊字符，生成简单的slug
        $slug = preg_replace('/[^a-zA-Z0-9\u4e00-\u9fa5]/', '-', $title);
        $slug = trim($slug, '-');
        
        // 如果为空，使用时间戳
        if (empty($slug)) {
            $slug = 'album-' . time();
        }
        
        // 检查是否重复
        $originalSlug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * 检查slug是否存在
     */
    private function slugExists($slug) {
        $result = $this->db->fetch(
            "SELECT id FROM {$this->db->getPrefix()}albums WHERE slug = :slug",
            ['slug' => $slug]
        );
        
        return !empty($result);
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '';
        }
    }
}
