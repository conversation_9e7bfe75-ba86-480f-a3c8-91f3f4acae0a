<?php
/**
 * 视频模型类
 */

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Cache.php';

class Video {
    private $db;
    private $cache;
    private $table;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();
        $this->table = $this->db->getPrefix() . 'videos';
    }
    
    /**
     * 获取视频列表
     */
    public function getList($params = []) {
        $page = intval($params['page'] ?? 1);
        $pageSize = intval($params['page_size'] ?? 20);
        $categoryId = intval($params['category_id'] ?? 0);
        $order = $params['order'] ?? 'latest';
        $keyword = trim($params['keyword'] ?? '');
        $isPaid = $params['is_paid'] ?? null;
        
        $offset = ($page - 1) * $pageSize;
        
        // 构建查询条件
        $where = ['v.status = 1'];
        $bindings = [];
        
        if ($categoryId > 0) {
            $where[] = 'v.category_id = :category_id';
            $bindings['category_id'] = $categoryId;
        }
        
        if ($keyword) {
            $where[] = '(v.title LIKE :keyword1 OR v.description LIKE :keyword2 OR v.tags LIKE :keyword3)';
            $keywordParam = '%' . $keyword . '%';
            $bindings['keyword1'] = $keywordParam;
            $bindings['keyword2'] = $keywordParam;
            $bindings['keyword3'] = $keywordParam;
        }
        
        if ($isPaid !== null) {
            $where[] = 'v.is_paid = :is_paid';
            $bindings['is_paid'] = $isPaid ? 1 : 0;
        }
        
        $whereClause = implode(' AND ', $where);
        
        // 排序
        $orderClause = match($order) {
            'view' => 'v.view_count DESC',
            'favorite' => 'v.favorite_count DESC',
            'like' => 'v.like_count DESC',
            'download' => 'v.download_count DESC',
            default => 'v.created_at DESC'
        };
        
        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$this->table} v 
                     LEFT JOIN {$this->db->getPrefix()}categories c ON v.category_id = c.id 
                     WHERE {$whereClause}";
        
        $totalResult = $this->db->fetch($countSql, $bindings);
        $total = $totalResult['total'] ?? 0;
        
        // 查询列表
        $sql = "SELECT v.*, c.name as category_name, c.slug as category_slug
                FROM {$this->table} v 
                LEFT JOIN {$this->db->getPrefix()}categories c ON v.category_id = c.id 
                WHERE {$whereClause}
                ORDER BY {$orderClause}
                LIMIT {$offset}, {$pageSize}";
        
        $list = $this->db->fetchAll($sql, $bindings);
        
        // 处理数据
        foreach ($list as $index => $item) {
            $list[$index]['cover'] = $this->getCoverUrl($item['cover']);
            $list[$index]['video_url'] = $this->getVideoUrl($item['video_url']);
            $list[$index]['is_free'] = $item['is_free'];
            $list[$index]['duration_formatted'] = $this->formatDuration($item['duration']);
            $list[$index]['size_formatted'] = $this->formatFileSize($item['file_size']);
            $list[$index]['tags_array'] = $item['tags'] ? explode(',', $item['tags']) : [];
        }
        unset($item); // 清除引用
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => ceil($total / $pageSize)
        ];
    }
    
    /**
     * 根据ID获取视频详情
     */
    public function getDetailById($id, $userId = null) {
        return $this->getDetail($id, $userId);
    }

    /**
     * 根据ID或slug获取视频详情
     */
    public function getDetail($identifier, $userId = null) {
        $cacheKey = "video_detail_{$identifier}";
        $video = $this->cache->get($cacheKey);

        if ($video === false) {
            if (is_numeric($identifier)) {
                $sql = "SELECT v.*, c.name as category_name, c.slug as category_slug
                        FROM {$this->table} v
                        LEFT JOIN {$this->db->getPrefix()}categories c ON v.category_id = c.id
                        WHERE v.id = :id AND v.status = 1";
                $video = $this->db->fetch($sql, ['id' => $identifier]);
            } else {
                $sql = "SELECT v.*, c.name as category_name, c.slug as category_slug
                        FROM {$this->table} v
                        LEFT JOIN {$this->db->getPrefix()}categories c ON v.category_id = c.id
                        WHERE v.slug = :slug AND v.status = 1";
                $video = $this->db->fetch($sql, ['slug' => $identifier]);
            }

            if ($video) {
                $this->cache->set($cacheKey, $video, 3600);
            }
        }

        if (!$video) {
            return null;
        }

        // 处理数据
        $video['cover'] = $this->getCoverUrl($video['cover']);
        $video['video_url'] = $this->getVideoUrl($video['video_url']);
        $video['is_free'] = $video['is_free'];
        $video['duration_formatted'] = $this->formatDuration($video['duration']);
        $video['size_formatted'] = $this->formatFileSize($video['file_size']);
        $video['tags_array'] = $video['tags'] ? explode(',', $video['tags']) : [];

        // 获取分集信息
        $video['episodes'] = $this->getEpisodes($video['id']);
        $video['current_episode'] = $video['episodes'][0] ?? null;

        // 获取用户相关信息
        if ($userId) {
            $video['is_favorited'] = $this->isFavorited($video['id'], $userId);
            $video['is_liked'] = $this->isLiked($video['id'], $userId);
            $video['watch_history'] = $this->getWatchHistory($video['id'], $userId);
        }

        return $video;
    }

    /**
     * 获取视频分集列表
     */
    public function getEpisodes($videoId) {
        $sql = "SELECT * FROM {$this->db->getPrefix()}video_episodes
                WHERE video_id = :video_id AND status = 1
                ORDER BY sort_order ASC, episode_number ASC";

        $episodes = $this->db->fetchAll($sql, ['video_id' => $videoId]);

        foreach ($episodes as &$episode) {
            $episode['cover'] = $this->getCoverUrl($episode['cover']);
            $episode['video_url'] = $this->getVideoUrl($episode['video_url']);
            $episode['duration_formatted'] = $this->formatDuration($episode['duration']);
            $episode['size_formatted'] = $this->formatFileSize($episode['file_size']);
        }

        return $episodes;
    }

    /**
     * 获取指定分集详情
     */
    public function getEpisodeDetail($videoId, $episodeNumber, $userId = null) {
        $sql = "SELECT e.*, v.title as video_title, v.is_paid, v.category_id
                FROM {$this->db->getPrefix()}video_episodes e
                LEFT JOIN {$this->table} v ON e.video_id = v.id
                WHERE e.video_id = :video_id AND e.episode_number = :episode_number AND e.status = 1";

        $episode = $this->db->fetch($sql, [
            'video_id' => $videoId,
            'episode_number' => $episodeNumber
        ]);

        if (!$episode) {
            return null;
        }

        $episode['cover'] = $this->getCoverUrl($episode['cover']);
        $episode['video_url'] = $this->getVideoUrl($episode['video_url']);
        $episode['duration_formatted'] = $this->formatDuration($episode['duration']);
        $episode['size_formatted'] = $this->formatFileSize($episode['file_size']);
        $episode['is_free'] = $episode['is_free'];

        // 获取用户观看记录
        if ($userId) {
            $episode['watch_progress'] = $this->getWatchProgress($episode['id'], $userId);
        }

        return $episode;
    }

    /**
     * 获取用户观看历史
     */
    public function getWatchHistory($videoId, $userId) {
        $sql = "SELECT * FROM {$this->db->getPrefix()}video_watch_history
                WHERE video_id = :video_id AND user_id = :user_id
                ORDER BY last_watch_time DESC";

        return $this->db->fetchAll($sql, [
            'video_id' => $videoId,
            'user_id' => $userId
        ]);
    }

    /**
     * 获取用户观看进度
     */
    public function getWatchProgress($episodeId, $userId) {
        $sql = "SELECT watch_progress, watch_duration, is_completed
                FROM {$this->db->getPrefix()}video_watch_history
                WHERE episode_id = :episode_id AND user_id = :user_id";

        return $this->db->fetch($sql, [
            'episode_id' => $episodeId,
            'user_id' => $userId
        ]);
    }

    /**
     * 更新观看进度
     */
    public function updateWatchProgress($episodeId, $userId, $progress, $duration = 0) {
        $videoId = $this->db->fetch(
            "SELECT video_id FROM {$this->db->getPrefix()}video_episodes WHERE id = :id",
            ['id' => $episodeId]
        )['video_id'] ?? 0;

        if (!$videoId) {
            return false;
        }

        $data = [
            'user_id' => $userId,
            'video_id' => $videoId,
            'episode_id' => $episodeId,
            'watch_progress' => $progress,
            'watch_duration' => $duration,
            'is_completed' => $progress >= $duration * 0.9 ? 1 : 0, // 观看90%以上算完成
            'last_watch_time' => date('Y-m-d H:i:s')
        ];

        // 检查是否已存在记录
        $existing = $this->db->fetch(
            "SELECT id FROM {$this->db->getPrefix()}video_watch_history
             WHERE episode_id = :episode_id AND user_id = :user_id",
            ['episode_id' => $episodeId, 'user_id' => $userId]
        );

        if ($existing) {
            return $this->db->update(
                'video_watch_history',
                $data,
                'id = :id',
                ['id' => $existing['id']]
            );
        } else {
            return $this->db->insert('video_watch_history', $data);
        }
    }
    
    /**
     * 增加浏览次数
     */
    public function incrementViewCount($videoId, $userId = null, $ip = null) {
        // 防止重复计数（同一用户或IP在1小时内只计算一次）
        $cacheKey = "video_view_{$videoId}_" . ($userId ?: $ip);
        if ($this->cache->get($cacheKey)) {
            return false;
        }
        
        $this->db->query(
            "UPDATE {$this->table} SET view_count = view_count + 1 WHERE id = :id",
            ['id' => $videoId]
        );
        
        // 记录浏览日志
        $this->db->insert('view_logs', [
            'user_id' => $userId,
            'content_type' => 'video',
            'content_id' => $videoId,
            'ip_address' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'viewed_at' => date('Y-m-d H:i:s')
        ]);
        
        // 设置缓存，1小时内不重复计数
        $this->cache->set($cacheKey, true, 3600);
        
        return true;
    }
    
    /**
     * 获取封面图片URL
     */
    private function getCoverUrl($coverImage) {
        if (!$coverImage) {
            return '/assets/images/placeholder.svg';
        }
        
        if (strpos($coverImage, 'http') === 0) {
            return $coverImage;
        }
        
        // 从配置获取CDN前缀
        $cdnPrefix = $this->getConfig('cdn_url_prefix', '');
        return $cdnPrefix ? $cdnPrefix . '/' . ltrim($coverImage, '/') : $coverImage;
    }
    
    /**
     * 获取视频文件URL
     */
    private function getVideoUrl($videoPath) {
        if (!$videoPath) {
            return '';
        }
        
        if (strpos($videoPath, 'http') === 0) {
            return $videoPath;
        }
        
        // 从配置获取CDN前缀
        $cdnPrefix = $this->getConfig('cdn_url_prefix', '');
        return $cdnPrefix ? $cdnPrefix . '/' . ltrim($videoPath, '/') : $videoPath;
    }
    
    /**
     * 格式化时长
     */
    private function formatDuration($seconds) {
        if ($seconds < 60) {
            return sprintf('%02d秒', $seconds);
        } elseif ($seconds < 3600) {
            return sprintf('%02d:%02d', floor($seconds / 60), $seconds % 60);
        } else {
            return sprintf('%02d:%02d:%02d', 
                floor($seconds / 3600), 
                floor(($seconds % 3600) / 60), 
                $seconds % 60
            );
        }
    }
    
    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
    
    /**
     * 检查是否已收藏
     */
    private function isFavorited($videoId, $userId) {
        $result = $this->db->fetch(
            "SELECT id FROM {$this->db->getPrefix()}favorites 
             WHERE user_id = :user_id AND content_type = 'video' AND content_id = :content_id",
            ['user_id' => $userId, 'content_id' => $videoId]
        );
        return !empty($result);
    }
    
    /**
     * 检查是否已点赞
     */
    private function isLiked($videoId, $userId) {
        // 这里需要实现点赞表的逻辑，暂时返回false
        return false;
    }
    
    /**
     * 获取配置
     */
    private function getConfig($key, $default = '') {
        $config = $this->db->fetch(
            "SELECT config_value FROM {$this->db->getPrefix()}system_config WHERE config_key = :key",
            ['key' => $key]
        );
        return $config ? $config['config_value'] : $default;
    }
}
?>
