<?php
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Cache.php';

/**
 * 用户模型类
 */
class User {
    private $db;
    private $cache;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();
    }
    
    /**
     * 用户注册
     */
    public function register($username, $email, $password, $inviter_id = null) {
        // 检查用户名是否存在
        if ($this->getUserByUsername($username)) {
            return ['success' => false, 'message' => '用户名已存在'];
        }
        
        // 检查邮箱是否存在
        if ($this->getUserByEmail($email)) {
            return ['success' => false, 'message' => '邮箱已被注册'];
        }
        
        // 生成密码盐和哈希
        $salt = $this->generateSalt();
        $passwordHash = $this->hashPassword($password, $salt);
        
        // 生成邀请码
        $inviteCode = $this->generateInviteCode();
        
        // 获取注册奖励积分
        $registerPoints = $this->getConfig('register_points', 100);
        
        $userData = [
            'username' => $username,
            'email' => $email,
            'password' => $passwordHash,
            'salt' => $salt,
            'nickname' => $username,
            'group_id' => 2, // 注册会员
            'points' => $registerPoints,
            'invite_code' => $inviteCode,
            'inviter_id' => $inviter_id,
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $this->getClientIp()
        ];
        
        $this->db->beginTransaction();
        
        try {
            $userId = $this->db->insert('users', $userData);
            
            if (!$userId) {
                throw new Exception('用户创建失败');
            }
            
            // 记录积分获取日志
            $this->addPointsLog($userId, 'reward', 'register', $registerPoints, $registerPoints, '注册奖励');
            
            // 邀请奖励现在由InviteManager处理，这里不再处理
            
            $this->db->commit();
            
            return ['success' => true, 'user_id' => $userId, 'message' => '注册成功'];
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password) {
        $user = $this->getUserByUsername($username);

        if (!$user) {
            $user = $this->getUserByEmail($username);
        }

        if (!$user) {
            return ['success' => false, 'message' => '用户不存在'];
        }

        if ($user['status'] != 1) {
            return ['success' => false, 'message' => '账号已被禁用'];
        }

        // 验证密码
        if (!$this->verifyPassword($password, $user['password'], $user['salt'])) {
            return ['success' => false, 'message' => '密码错误'];
        }

        // 获取用户组信息
        if (!empty($user['group_id'])) {
            $userGroup = $this->db->fetch(
                "SELECT group_name FROM {$this->db->getPrefix()}user_groups WHERE id = :id",
                ['id' => $user['group_id']]
            );
            $user['group_name'] = $userGroup['group_name'] ?? '会员';
        } else {
            $user['group_name'] = '会员';
        }

        // 更新登录信息
        $this->db->update('users', [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $this->getClientIp()
        ], 'id = :id', ['id' => $user['id']]);

        // 清除缓存
        $this->cache->delete('user:' . $user['id']);

        return ['success' => true, 'user' => $user, 'message' => '登录成功'];
    }
    
    /**
     * 根据用户名获取用户
     */
    public function getUserByUsername($username) {
        return $this->db->fetch(
            "SELECT * FROM {$this->db->getPrefix()}users WHERE username = :username",
            ['username' => $username]
        );
    }
    
    /**
     * 根据邮箱获取用户
     */
    public function getUserByEmail($email) {
        return $this->db->fetch(
            "SELECT * FROM {$this->db->getPrefix()}users WHERE email = :email",
            ['email' => $email]
        );
    }
    
    /**
     * 根据ID获取用户信息
     */
    public function getUserById($userId) {
        // 先从缓存获取
        $cacheKey = 'user:' . $userId;
        $user = $this->cache->get($cacheKey);
        
        if ($user === false) {
            $user = $this->db->fetch(
                "SELECT u.*, g.group_name, g.group_key as group_slug, g.duration_days as group_days
                 FROM {$this->db->getPrefix()}users u
                 LEFT JOIN {$this->db->getPrefix()}user_groups g ON u.group_id = g.id
                 WHERE u.id = :id",
                ['id' => $userId]
            );
            
            if ($user) {
                // 缓存30分钟
                $this->cache->set($cacheKey, $user, 1800);
            }
        }
        
        return $user;
    }
    
    /**
     * 检查用户权限
     */
    public function checkPermission($userId, $permission) {
        $user = $this->getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        // 检查用户组是否过期
        if ($user['group_expire_time'] && strtotime($user['group_expire_time']) < time()) {
            // 降级为注册会员
            $this->db->update('users', [
                'group_id' => 2,
                'group_expire_time' => null
            ], 'id = :id', ['id' => $userId]);
            
            $user['group_id'] = 2;
            $this->cache->delete('user:' . $userId);
        }
        
        // 获取用户组权限
        $permissionValue = $this->db->fetch(
            "SELECT permission_value FROM {$this->db->getPrefix()}user_permissions 
             WHERE group_id = :group_id AND permission_key = :permission",
            ['group_id' => $user['group_id'], 'permission' => $permission]
        );
        
        return $permissionValue ? $permissionValue['permission_value'] : null;
    }
    
    /**
     * 添加积分
     */
    public function addPoints($userId, $points, $type = 'reward', $action = '', $description = '') {
        $user = $this->getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        $newBalance = $user['points'] + $points;
        
        // 更新用户积分
        $this->db->update('users', [
            'points' => $newBalance
        ], 'id = :id', ['id' => $userId]);
        
        // 记录积分日志
        $this->addPointsLog($userId, $type, $action, $points, $newBalance, $description);
        
        // 清除缓存
        $this->cache->delete('user:' . $userId);
        
        return true;
    }
    
    /**
     * 扣除积分
     */
    public function deductPoints($userId, $points, $action = '', $description = '') {
        $user = $this->getUserById($userId);
        
        if (!$user || $user['points'] < $points) {
            return false;
        }
        
        $newBalance = $user['points'] - $points;
        
        // 更新用户积分
        $this->db->update('users', [
            'points' => $newBalance
        ], 'id = :id', ['id' => $userId]);
        
        // 记录积分日志
        $this->addPointsLog($userId, 'consume', $action, -$points, $newBalance, $description);
        
        // 清除缓存
        $this->cache->delete('user:' . $userId);
        
        return true;
    }
    
    /**
     * 升级用户组
     */
    public function upgradeGroup($userId, $groupId, $days = null) {
        $user = $this->getUserById($userId);
        
        if (!$user) {
            return false;
        }
        
        $updateData = ['group_id' => $groupId];
        
        if ($days && $days > 0) {
            $expireTime = date('Y-m-d H:i:s', time() + $days * 86400);
            $updateData['group_expire_time'] = $expireTime;
        } else {
            $updateData['group_expire_time'] = null;
        }
        
        $this->db->update('users', $updateData, 'id = :id', ['id' => $userId]);
        
        // 清除缓存
        $this->cache->delete('user:' . $userId);
        
        return true;
    }
    
    /**
     * 记录积分日志
     */
    private function addPointsLog($userId, $type, $action, $points, $balance, $description = '', $relatedId = null) {
        return $this->db->insert('points_log', [
            'user_id' => $userId,
            'type' => $type,
            'action' => $action,
            'points' => $points,
            'balance' => $balance,
            'description' => $description,
            'related_id' => $relatedId
        ]);
    }
    
    /**
     * 生成密码盐
     */
    private function generateSalt($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * 哈希密码
     */
    private function hashPassword($password, $salt) {
        return hash('sha256', $password . $salt);
    }
    
    /**
     * 验证密码
     */
    private function verifyPassword($password, $hash, $salt) {
        return hash_equals($hash, $this->hashPassword($password, $salt));
    }
    
    /**
     * 生成邀请码
     */
    private function generateInviteCode() {
        do {
            $code = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
            $exists = $this->db->fetch(
                "SELECT id FROM {$this->db->getPrefix()}users WHERE invite_code = :code",
                ['code' => $code]
            );
        } while ($exists);
        
        return $code;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $ip = '';
        
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        return $ip;
    }
    
    /**
     * 获取配置
     */
    private function getConfig($key, $default = null) {
        $config = $this->db->fetch(
            "SELECT value FROM {$this->db->getPrefix()}system_config WHERE `key` = :key",
            ['key' => $key]
        );
        
        return $config ? $config['value'] : $default;
    }
}
