<?php
require_once __DIR__ . '/../core/Database.php';

/**
 * 友情链接模型
 */
class FriendlyLink {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 获取启用的友情链接列表
     */
    public function getActiveLinks() {
        return $this->db->fetchAll("
            SELECT * FROM {$this->db->getPrefix()}friendly_links 
            WHERE status = 1 
            ORDER BY sort_order ASC, id DESC
        ");
    }
    
    /**
     * 获取所有友情链接列表
     */
    public function getAllLinks() {
        return $this->db->fetchAll("
            SELECT * FROM {$this->db->getPrefix()}friendly_links 
            ORDER BY sort_order ASC, id DESC
        ");
    }
    
    /**
     * 根据ID获取友情链接
     */
    public function getById($id) {
        return $this->db->fetch("
            SELECT * FROM {$this->db->getPrefix()}friendly_links 
            WHERE id = :id
        ", ['id' => $id]);
    }
    
    /**
     * 添加友情链接
     */
    public function add($data) {
        return $this->db->insert('friendly_links', [
            'name' => $data['name'],
            'url' => $data['url'],
            'description' => $data['description'] ?? '',
            'logo' => $data['logo'] ?? '',
            'sort_order' => intval($data['sort_order'] ?? 0),
            'status' => intval($data['status'] ?? 1)
        ]);
    }
    
    /**
     * 更新友情链接
     */
    public function update($id, $data) {
        return $this->db->update('friendly_links', [
            'name' => $data['name'],
            'url' => $data['url'],
            'description' => $data['description'] ?? '',
            'logo' => $data['logo'] ?? '',
            'sort_order' => intval($data['sort_order'] ?? 0),
            'status' => intval($data['status'] ?? 1)
        ], 'id = :id', ['id' => $id]);
    }
    
    /**
     * 删除友情链接
     */
    public function delete($id) {
        return $this->db->delete('friendly_links', 'id = :id', ['id' => $id]);
    }
    
    /**
     * 切换状态
     */
    public function toggleStatus($id) {
        $link = $this->getById($id);
        if ($link) {
            $newStatus = $link['status'] ? 0 : 1;
            return $this->db->update('friendly_links', 
                ['status' => $newStatus], 
                'id = :id', 
                ['id' => $id]
            );
        }
        return false;
    }
    
    /**
     * 获取友情链接统计
     */
    public function getStats() {
        $total = $this->db->fetchColumn("
            SELECT COUNT(*) FROM {$this->db->getPrefix()}friendly_links
        ");
        
        $active = $this->db->fetchColumn("
            SELECT COUNT(*) FROM {$this->db->getPrefix()}friendly_links 
            WHERE status = 1
        ");
        
        return [
            'total' => $total,
            'active' => $active,
            'inactive' => $total - $active
        ];
    }
}
?>
