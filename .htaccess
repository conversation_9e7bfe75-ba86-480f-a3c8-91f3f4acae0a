RewriteEngine On

# 阻止访问敏感文件
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# 阻止访问配置文件
<FilesMatch "\.(json|lock|sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 阻止访问核心目录
RewriteRule ^(config|core|models|database)/ - [F,L]

# 静态资源直接访问
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_URI} \.(css|js|png|jpg|jpeg|gif|webp|ico|svg|woff|woff2|ttf|eot|mp4|avi|mov)$ [NC]
RewriteRule . - [L]

# API接口路由 - 允许直接访问
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^api/(.*)$ - [L]

# 火车头发布接口
RewriteRule ^train_publish/?$ api/train_publish.php [L]

# 安装页面
RewriteRule ^install/?$ install.php [L]

# 管理后台路由
RewriteCond %{REQUEST_URI} !^/admin/index\.php$
RewriteRule ^admin/?(.*)$ admin/index.php [L]

# 用户相关路由
RewriteRule ^login/?$ pages/login.php [L]
RewriteRule ^register/?$ pages/register.php [L]
RewriteRule ^logout/?$ pages/logout.php [L]
RewriteRule ^profile/?$ pages/profile.php [L]
RewriteRule ^user/(.*)$ pages/user/$1.php [L]

# 套图路由
RewriteRule ^albums/?$ pages/albums.php [L]
RewriteRule ^albums/cat/([^/]+)/?$ pages/albums.php?category=$1 [L]
RewriteRule ^album/([0-9]+)/?$ pages/album.php?id=$1 [L]

# 视频路由
RewriteRule ^videos/?$ pages/videos.php [L]
RewriteRule ^videos/cat/([^/]+)/?$ pages/videos.php?category=$1 [L]
RewriteRule ^video/([0-9]+)/?$ pages/video.php?id=$1 [L]

# 其他页面路由
RewriteRule ^ranking/?$ pages/ranking.php [L]
RewriteRule ^latest/?$ pages/latest.php [L,QSA]
RewriteRule ^search/?$ pages/search.php [L]

# 静态页面路由
RewriteRule ^about/?$ pages/about.php [L]
RewriteRule ^contact/?$ pages/contact.php [L]
RewriteRule ^privacy/?$ pages/privacy.php [L]
RewriteRule ^terms/?$ pages/terms.php [L]

# 首页路由
RewriteRule ^$ index.php [L]

# 404处理
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . pages/404.php [L]

# 安全设置
<IfModule mod_headers.c>
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 压缩设置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType font/woff "access plus 1 month"
    ExpiresByType font/woff2 "access plus 1 month"
</IfModule>
