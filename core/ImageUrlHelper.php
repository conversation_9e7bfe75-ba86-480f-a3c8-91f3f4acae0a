<?php

class ImageUrlHelper {
    private static $attachmentMode = null;
    private static $cdnUrl = null;
    private static $db = null;
    
    /**
     * 初始化配置
     */
    private static function init() {
        if (self::$attachmentMode === null) {
            require_once __DIR__ . '/Database.php';
            self::$db = Database::getInstance();
            
            // 获取附件模式配置
            $config = self::$db->fetch("
                SELECT value FROM " . self::$db->getPrefix() . "system_config
                WHERE `key` = 'attachment_mode'
            ");
            self::$attachmentMode = $config['value'] ?? 'remote';

            // 获取CDN地址配置
            $cdnConfig = self::$db->fetch("
                SELECT value FROM " . self::$db->getPrefix() . "system_config
                WHERE `key` = 'image_cdn_url'
            ");
            self::$cdnUrl = $cdnConfig['value'] ?? 'https://cdn.example.com/';
            
            // 确保CDN地址以斜杠结尾
            if (!empty(self::$cdnUrl) && substr(self::$cdnUrl, -1) !== '/') {
                self::$cdnUrl .= '/';
            }
        }
    }
    
    /**
     * 获取套图图片URL
     * @param array $image 图片数据（包含file_url和local_file_url）
     * @return string 图片URL
     */
    public static function getAlbumImageUrl($image) {
        self::init();
        
        // 如果是本地化模式且有本地化图片
        if (self::$attachmentMode === 'local' && !empty($image['local_file_url'])) {
            return self::$cdnUrl . ltrim($image['local_file_url'], '/');
        }
        
        // 默认返回原始图片URL
        return $image['file_url'] ?? '';
    }
    
    /**
     * 获取套图的所有图片URL列表
     * @param int $albumId 套图ID
     * @return array 图片URL数组
     */
    public static function getAlbumImageUrls($albumId) {
        self::init();
        
        $images = self::$db->fetchAll("
            SELECT file_url, local_file_url 
            FROM {self::$db->getPrefix()}album_images 
            WHERE album_id = :album_id 
            ORDER BY sort ASC, id ASC
        ", ['album_id' => $albumId]);
        
        $urls = [];
        foreach ($images as $image) {
            $urls[] = self::getAlbumImageUrl($image);
        }
        
        return $urls;
    }
    
    /**
     * 获取套图封面图片URL
     * @param int $albumId 套图ID
     * @return string 封面图片URL
     */
    public static function getAlbumCoverUrl($albumId) {
        self::init();
        
        $image = self::$db->fetch("
            SELECT file_url, local_file_url 
            FROM {self::$db->getPrefix()}album_images 
            WHERE album_id = :album_id 
            ORDER BY sort ASC, id ASC 
            LIMIT 1
        ", ['album_id' => $albumId]);
        
        if (!$image) {
            return '';
        }
        
        return self::getAlbumImageUrl($image);
    }
    
    /**
     * 批量获取多个套图的封面URL
     * @param array $albumIds 套图ID数组
     * @return array 套图ID => 封面URL的映射
     */
    public static function getBatchAlbumCovers($albumIds) {
        if (empty($albumIds)) {
            return [];
        }
        
        self::init();
        
        $placeholders = str_repeat('?,', count($albumIds) - 1) . '?';
        $images = self::$db->fetchAll("
            SELECT ai.album_id, ai.file_url, ai.local_file_url
            FROM {self::$db->getPrefix()}album_images ai
            INNER JOIN (
                SELECT album_id, MIN(id) as min_id
                FROM {self::$db->getPrefix()}album_images
                WHERE album_id IN ($placeholders)
                GROUP BY album_id
            ) first_images ON ai.id = first_images.min_id
        ", $albumIds);
        
        $covers = [];
        foreach ($images as $image) {
            $covers[$image['album_id']] = self::getAlbumImageUrl($image);
        }
        
        return $covers;
    }
    
    /**
     * 获取当前附件模式
     * @return string 'remote' 或 'local'
     */
    public static function getAttachmentMode() {
        self::init();
        return self::$attachmentMode;
    }
    
    /**
     * 获取CDN地址
     * @return string CDN地址
     */
    public static function getCdnUrl() {
        self::init();
        return self::$cdnUrl;
    }
    
    /**
     * 刷新配置缓存
     */
    public static function refreshConfig() {
        self::$attachmentMode = null;
        self::$cdnUrl = null;
    }
}
?>
