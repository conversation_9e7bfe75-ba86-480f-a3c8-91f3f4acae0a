<?php
/**
 * 系统配置管理类
 */

// 只在需要时引入Database类
if (!class_exists('Database') && file_exists(__DIR__ . '/Database.php')) {
    require_once __DIR__ . '/Database.php';
}

class Config {
    private static $instance = null;
    private static $configs = [];
    private static $loaded = false;
    
    /**
     * 获取单例实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载所有配置
     */
    private static function loadConfigs() {
        if (self::$loaded) {
            return;
        }
        
        try {
            // 检查Database类是否可用
            if (!class_exists('Database')) {
                throw new Exception('Database class not available');
            }

            $db = Database::getInstance();
            $configs = $db->fetchAll("SELECT `key`, `value` FROM {$db->getPrefix()}system_config");

            if ($configs) {
                foreach ($configs as $config) {
                    self::$configs[$config['key']] = $config['value'];
                }
            }

            self::$loaded = true;
        } catch (Exception $e) {
            // 如果数据库连接失败，使用默认配置
            error_log('Config load failed: ' . $e->getMessage());
            self::$configs = [
                'site_name' => '丽片网',
                'site_description' => '专业的美女套图视频网站',
                'site_keywords' => '美女,套图,视频,写真',
                'site_url' => 'https://www.liapian.com'
            ];
            self::$loaded = true;
        }
    }
    
    /**
     * 获取配置值
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @return mixed
     */
    public static function get($key, $default = null) {
        self::loadConfigs();
        return isset(self::$configs[$key]) ? self::$configs[$key] : $default;
    }
    
    /**
     * 设置配置值（仅内存中）
     * @param string $key 配置键
     * @param mixed $value 配置值
     */
    public static function set($key, $value) {
        self::loadConfigs();
        self::$configs[$key] = $value;
    }
    
    /**
     * 获取网站名称
     * @return string
     */
    public static function getSiteName() {
        return self::get('site_name', '丽片网');
    }
    
    /**
     * 获取网站描述
     * @return string
     */
    public static function getSiteDescription() {
        return self::get('site_description', '专业的美女套图视频网站');
    }
    
    /**
     * 获取网站关键词
     * @return string
     */
    public static function getSiteKeywords() {
        return self::get('site_keywords', '美女,套图,视频,写真');
    }
    
    /**
     * 获取网站URL
     * @return string
     */
    public static function getSiteUrl() {
        return self::get('site_url', 'https://www.liapian.com');
    }
    
    /**
     * 重新加载配置
     */
    public static function reload() {
        self::$loaded = false;
        self::$configs = [];
        self::loadConfigs();
    }
    
    /**
     * 获取所有配置
     * @return array
     */
    public static function all() {
        self::loadConfigs();
        return self::$configs;
    }
}
