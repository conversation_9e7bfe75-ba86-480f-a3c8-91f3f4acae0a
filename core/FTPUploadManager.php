<?php

class FTPUploadManager {
    private $ftpConfig;
    private $connection;
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/Database.php';
        $this->db = Database::getInstance();
        $this->ftpConfig = $this->getFTPConfig();
        // 延迟连接，在实际使用时再连接
    }
    
    /**
     * 获取FTP配置
     */
    private function getFTPConfig() {
        $config = [];
        $settings = $this->db->fetchAll("SELECT `key`, value FROM {$this->db->getPrefix()}system_config WHERE `key` LIKE 'ftp_%'");

        foreach ($settings as $setting) {
            $key = str_replace('ftp_', '', $setting['key']);
            $config[$key] = $setting['value'];
        }

        return $config;
    }
    
    /**
     * 确保FTP连接
     */
    private function ensureConnection() {
        if ($this->connection && @ftp_pwd($this->connection) !== false) {
            return; // 连接正常
        }
        $this->connect();
    }

    /**
     * 连接FTP服务器
     */
    private function connect() {
        if (empty($this->ftpConfig['host'])) {
            throw new Exception('FTP配置不完整：缺少主机地址');
        }

        if (empty($this->ftpConfig['username'])) {
            throw new Exception('FTP配置不完整：缺少用户名');
        }

        if (empty($this->ftpConfig['password'])) {
            throw new Exception('FTP配置不完整：缺少密码');
        }

        // 禁用FTP函数的错误输出
        $this->connection = @ftp_connect($this->ftpConfig['host'], $this->ftpConfig['port'] ?? 21);

        if (!$this->connection) {
            throw new Exception('无法连接到FTP服务器：' . $this->ftpConfig['host'] . ':' . ($this->ftpConfig['port'] ?? 21));
        }

        $login = @ftp_login($this->connection, $this->ftpConfig['username'], $this->ftpConfig['password']);

        if (!$login) {
            ftp_close($this->connection);
            throw new Exception('FTP登录失败：用户名或密码错误');
        }

        // 设置被动模式
        @ftp_pasv($this->connection, true);

        // 切换到根目录
        if (!empty($this->ftpConfig['path'])) {
            if (!@ftp_chdir($this->connection, $this->ftpConfig['path'])) {
                throw new Exception('无法切换到FTP目录：' . $this->ftpConfig['path']);
            }
        }
    }
    
    /**
     * 上传文件到FTP
     * @param string $localFile 本地文件路径
     * @param string $type 文件类型 (album/avatar/system/video)
     * @param array $options 额外选项
     * @return string|false 返回FTP相对路径或false
     */
    public function uploadFile($localFile, $type, $options = []) {
        // 确保FTP连接
        $this->ensureConnection();

        if (!file_exists($localFile)) {
            throw new Exception('本地文件不存在: ' . $localFile);
        }
        
        // 1. 生成目录路径
        $remotePath = $this->generatePath($type, $options);
        
        // 2. 创建远程目录
        $this->createRemoteDirectory($remotePath);
        
        // 3. 生成新文件名
        $newFileName = $this->generateFileName($localFile, $type, $options);
        
        // 4. 完整远程文件路径
        $remoteFile = $remotePath . '/' . $newFileName;
        
        // 5. 上传文件
        if (ftp_put($this->connection, $remoteFile, $localFile, FTP_BINARY)) {
            return $remoteFile; // 返回相对路径
        }
        
        throw new Exception('FTP上传失败: ' . $remoteFile);
    }
    
    /**
     * 删除FTP上的文件
     */
    public function deleteFile($remoteFile) {
        // 确保FTP连接
        $this->ensureConnection();

        return @ftp_delete($this->connection, $remoteFile);
    }
    
    /**
     * 生成目录路径
     */
    private function generatePath($type, $options) {
        $basePath = 'uploads';
        $date = date('Y/m/d');
        
        switch ($type) {
            case 'album':
                return "{$basePath}/albums/{$date}";
            case 'video':
                return "{$basePath}/videos/{$date}";
            case 'avatar':
                return "{$basePath}/avatars/" . date('Y/m');
            case 'system':
                $subType = $options['sub_type'] ?? 'misc';
                return "{$basePath}/system/{$subType}";
            default:
                return "{$basePath}/misc/{$date}";
        }
    }
    
    /**
     * 生成安全的随机字符串
     */
    private function generateRandomString($length = 8) {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $randomString;
    }
    
    /**
     * 生成文件名
     */
    private function generateFileName($localFile, $type, $options) {
        $extension = strtolower(pathinfo($localFile, PATHINFO_EXTENSION));
        $timestamp = time();
        
        switch ($type) {
            case 'album':
                $albumId = $options['album_id'] ?? 0;
                $index = $options['index'] ?? 1;
                $randomStr = $this->generateRandomString(8);
                return sprintf("album_%d_%03d_%s.%s", $albumId, $index, $randomStr, $extension);
                
            case 'video':
                $videoId = $options['video_id'] ?? 0;
                $randomStr = $this->generateRandomString(8);
                return sprintf("video_%d_%s.%s", $videoId, $randomStr, $extension);
                
            case 'avatar':
                $userId = $options['user_id'] ?? 0;
                return sprintf("user_%d_avatar_%d.%s", $userId, $timestamp, $extension);
                
            case 'system':
                $subType = $options['sub_type'] ?? 'misc';
                return sprintf("system_%s_%d.%s", $subType, $timestamp, $extension);
                
            default:
                return sprintf("file_%d.%s", $timestamp, $extension);
        }
    }
    
    /**
     * 创建远程目录
     */
    private function createRemoteDirectory($path) {
        $dirs = explode('/', $path);
        $currentPath = '';
        
        foreach ($dirs as $dir) {
            if (empty($dir)) continue;
            
            $currentPath .= '/' . $dir;
            
            // 尝试切换到目录，如果失败则创建
            if (!@ftp_chdir($this->connection, $currentPath)) {
                if (!ftp_mkdir($this->connection, $currentPath)) {
                    throw new Exception('无法创建FTP目录: ' . $currentPath);
                }
                ftp_chmod($this->connection, 0755, $currentPath);
            }
        }
        
        // 回到根目录
        if (!empty($this->ftpConfig['path'])) {
            ftp_chdir($this->connection, $this->ftpConfig['path']);
        } else {
            ftp_chdir($this->connection, '/');
        }
    }
    
    /**
     * 验证图片文件
     */
    public function validateImage($filePath) {
        if (!file_exists($filePath)) {
            return false;
        }
        
        $imageInfo = @getimagesize($filePath);
        if ($imageInfo === false) {
            return false;
        }
        
        // 检查文件类型
        $allowedTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF, IMAGETYPE_WEBP];
        if (!in_array($imageInfo[2], $allowedTypes)) {
            return false;
        }
        
        // 检查文件大小（最大10MB）
        $maxSize = 10 * 1024 * 1024;
        if (filesize($filePath) > $maxSize) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 关闭FTP连接
     */
    public function __destruct() {
        if ($this->connection) {
            ftp_close($this->connection);
        }
    }
}
?>
