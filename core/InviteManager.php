<?php

class InviteManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 生成用户邀请码
     */
    public function generateInviteCode($userId) {
        // 生成8位随机邀请码
        $code = strtoupper(substr(md5($userId . time() . rand(1000, 9999)), 0, 8));
        
        // 确保邀请码唯一
        while ($this->isInviteCodeExists($code)) {
            $code = strtoupper(substr(md5($userId . time() . rand(1000, 9999)), 0, 8));
        }
        
        // 更新用户邀请码
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}users 
            SET invite_code = :invite_code 
            WHERE id = :user_id
        ", [
            'invite_code' => $code,
            'user_id' => $userId
        ]);
        
        return $code;
    }
    
    /**
     * 检查邀请码是否存在
     */
    private function isInviteCodeExists($code) {
        $result = $this->db->fetch("
            SELECT id FROM {$this->db->getPrefix()}users 
            WHERE invite_code = :code
        ", ['code' => $code]);
        
        return !empty($result);
    }
    
    /**
     * 根据邀请码获取邀请人信息
     */
    public function getInviterByCode($inviteCode) {
        return $this->db->fetch("
            SELECT id, username, nickname 
            FROM {$this->db->getPrefix()}users 
            WHERE invite_code = :code AND status = 1
        ", ['code' => $inviteCode]);
    }
    
    /**
     * 建立邀请关系
     */
    public function createInviteRelation($inviteeId, $inviterCode) {
        $inviter = $this->getInviterByCode($inviterCode);
        if (!$inviter) {
            return false;
        }
        
        // 防止自己邀请自己
        if ($inviter['id'] == $inviteeId) {
            return false;
        }
        
        // 更新被邀请人的邀请人ID
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}users 
            SET inviter_id = :inviter_id 
            WHERE id = :invitee_id
        ", [
            'inviter_id' => $inviter['id'],
            'invitee_id' => $inviteeId
        ]);
        
        return true;
    }
    
    /**
     * 处理注册奖励
     */
    public function processRegisterReward($inviteeId, $inviterCode) {
        $inviter = $this->getInviterByCode($inviterCode);
        if (!$inviter) {
            return false;
        }
        
        // 建立邀请关系
        if (!$this->createInviteRelation($inviteeId, $inviterCode)) {
            return false;
        }
        
        // 获取注册奖励设置
        $registerReward = $this->getSetting('invite_register_reward', 100);
        
        if ($registerReward > 0) {
            // 给邀请人发放奖励
            $this->addPoints($inviter['id'], $registerReward, 'invite_register', '邀请注册奖励', $inviteeId);
            
            // 记录邀请收益
            $this->recordInviteEarning($inviter['id'], $inviteeId, 'register', $registerReward);
            
            // 处理二级邀请奖励
            $this->processLevel2RegisterReward($inviter['id'], $inviteeId, $registerReward);
        }
        
        return true;
    }
    
    /**
     * 处理充值分成奖励
     */
    public function processRechargeReward($userId, $rechargeAmount, $orderId = null) {
        // 获取用户的邀请人
        $user = $this->db->fetch("
            SELECT inviter_id FROM {$this->db->getPrefix()}users 
            WHERE id = :user_id
        ", ['user_id' => $userId]);
        
        if (!$user || !$user['inviter_id']) {
            return false;
        }
        
        $inviterId = $user['inviter_id'];
        
        // 获取分成比例设置
        $commissionRate = $this->getSetting('invite_recharge_rate', 30);
        $pointsRate = $this->getSetting('points_to_money_rate', 100);
        
        if ($commissionRate > 0) {
            // 计算分成积分
            $commissionAmount = $rechargeAmount * $commissionRate / 100;
            $commissionPoints = floor($commissionAmount * $pointsRate);
            
            if ($commissionPoints > 0) {
                // 给邀请人发放分成
                $this->addPoints($inviterId, $commissionPoints, 'invite_recharge', 
                    "邀请充值分成({$commissionRate}%)", $userId);
                
                // 记录邀请收益
                $this->recordInviteEarning($inviterId, $userId, 'recharge', $commissionPoints, 
                    $rechargeAmount, $commissionRate, $orderId);
                
                // 处理二级邀请分成
                $this->processLevel2RechargeReward($inviterId, $userId, $rechargeAmount, $orderId);
            }
        }
        
        return true;
    }
    
    /**
     * 处理二级注册奖励
     */
    private function processLevel2RegisterReward($level1InviterId, $inviteeId, $level1Reward) {
        if (!$this->getSetting('invite_level_enabled', true)) {
            return;
        }
        
        // 获取一级邀请人的邀请人（二级邀请人）
        $level1Inviter = $this->db->fetch("
            SELECT inviter_id FROM {$this->db->getPrefix()}users 
            WHERE id = :user_id
        ", ['user_id' => $level1InviterId]);
        
        if (!$level1Inviter || !$level1Inviter['inviter_id']) {
            return;
        }
        
        $level2InviterId = $level1Inviter['inviter_id'];
        $level2Rate = $this->getSetting('invite_level2_rate', 10);
        $level2Reward = floor($level1Reward * $level2Rate / 100);
        
        if ($level2Reward > 0) {
            // 给二级邀请人发放奖励
            $this->addPoints($level2InviterId, $level2Reward, 'invite_level2_register', 
                '二级邀请注册奖励', $inviteeId);
            
            // 记录二级邀请收益
            $this->recordInviteEarning($level2InviterId, $inviteeId, 'level2_register', 
                $level2Reward, null, null, null, 2);
        }
    }
    
    /**
     * 处理二级充值分成
     */
    private function processLevel2RechargeReward($level1InviterId, $inviteeId, $rechargeAmount, $orderId) {
        if (!$this->getSetting('invite_level_enabled', true)) {
            return;
        }
        
        // 获取一级邀请人的邀请人（二级邀请人）
        $level1Inviter = $this->db->fetch("
            SELECT inviter_id FROM {$this->db->getPrefix()}users 
            WHERE id = :user_id
        ", ['user_id' => $level1InviterId]);
        
        if (!$level1Inviter || !$level1Inviter['inviter_id']) {
            return;
        }
        
        $level2InviterId = $level1Inviter['inviter_id'];
        $level2Rate = $this->getSetting('invite_level2_rate', 10);
        $pointsRate = $this->getSetting('points_to_money_rate', 100);
        
        // 计算二级分成
        $level2CommissionAmount = $rechargeAmount * $level2Rate / 100;
        $level2CommissionPoints = floor($level2CommissionAmount * $pointsRate);
        
        if ($level2CommissionPoints > 0) {
            // 给二级邀请人发放分成
            $this->addPoints($level2InviterId, $level2CommissionPoints, 'invite_level2_recharge', 
                "二级邀请充值分成({$level2Rate}%)", $inviteeId);
            
            // 记录二级邀请收益
            $this->recordInviteEarning($level2InviterId, $inviteeId, 'level2_recharge', 
                $level2CommissionPoints, $rechargeAmount, $level2Rate, $orderId, 2);
        }
    }
    
    /**
     * 记录邀请收益
     */
    private function recordInviteEarning($inviterId, $inviteeId, $type, $pointsEarned, 
                                       $rechargeAmount = null, $commissionRate = null, 
                                       $orderId = null, $level = 1) {
        $this->db->execute("
            INSERT INTO {$this->db->getPrefix()}invite_earnings 
            (inviter_id, invitee_id, type, points_earned, recharge_amount, 
             commission_rate, order_id, level) 
            VALUES (:inviter_id, :invitee_id, :type, :points_earned, :recharge_amount, 
                    :commission_rate, :order_id, :level)
        ", [
            'inviter_id' => $inviterId,
            'invitee_id' => $inviteeId,
            'type' => $type,
            'points_earned' => $pointsEarned,
            'recharge_amount' => $rechargeAmount,
            'commission_rate' => $commissionRate,
            'order_id' => $orderId,
            'level' => $level
        ]);
    }
    
    /**
     * 添加积分并记录流水
     */
    private function addPoints($userId, $points, $source, $description, $relatedId = null) {
        // 获取当前积分
        $user = $this->db->fetch("
            SELECT points FROM {$this->db->getPrefix()}users 
            WHERE id = :user_id
        ", ['user_id' => $userId]);
        
        if (!$user) {
            return false;
        }
        
        $balanceBefore = $user['points'];
        $balanceAfter = $balanceBefore + $points;
        
        // 更新用户积分
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}users 
            SET points = points + :points 
            WHERE id = :user_id
        ", [
            'points' => $points,
            'user_id' => $userId
        ]);
        
        // 记录积分流水
        $this->db->execute("
            INSERT INTO {$this->db->getPrefix()}points_log 
            (user_id, type, source, amount, balance_before, balance_after, description, related_id) 
            VALUES (:user_id, 'earn', :source, :amount, :balance_before, :balance_after, :description, :related_id)
        ", [
            'user_id' => $userId,
            'source' => $source,
            'amount' => $points,
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'description' => $description,
            'related_id' => $relatedId
        ]);
        
        return true;
    }
    
    /**
     * 获取系统设置
     */
    private function getSetting($key, $default = null) {
        $setting = $this->db->fetch("
            SELECT value FROM {$this->db->getPrefix()}system_config 
            WHERE `key` = :key
        ", ['key' => $key]);
        
        if ($setting) {
            // 根据类型转换值
            $value = $setting['value'];
            if (is_numeric($value)) {
                return strpos($value, '.') !== false ? floatval($value) : intval($value);
            }
            if ($value === '1' || $value === 'true') {
                return true;
            }
            if ($value === '0' || $value === 'false') {
                return false;
            }
            return $value;
        }
        
        return $default;
    }
    
    /**
     * 获取用户邀请统计
     */
    public function getUserInviteStats($userId) {
        // 直接邀请统计
        $directStats = $this->db->fetch("
            SELECT
                COUNT(*) as total_invites,
                SUM(CASE WHEN ie.type IN ('register', 'recharge') THEN ie.points_earned ELSE 0 END) as total_earnings
            FROM {$this->db->getPrefix()}users u
            LEFT JOIN {$this->db->getPrefix()}invite_earnings ie ON u.id = ie.invitee_id AND ie.inviter_id = :user_id1
            WHERE u.inviter_id = :user_id2
        ", ['user_id1' => $userId, 'user_id2' => $userId]);
        
        // 二级邀请统计
        $level2Stats = $this->db->fetch("
            SELECT 
                COUNT(DISTINCT ie.invitee_id) as level2_invites,
                SUM(ie.points_earned) as level2_earnings
            FROM {$this->db->getPrefix()}invite_earnings ie
            WHERE ie.inviter_id = :user_id AND ie.level = 2
        ", ['user_id' => $userId]);
        
        // 确保查询结果不为false
        $directStats = $directStats ?: ['total_invites' => 0, 'total_earnings' => 0];
        $level2Stats = $level2Stats ?: ['level2_invites' => 0, 'level2_earnings' => 0];

        return [
            'direct_invites' => $directStats['total_invites'] ?: 0,
            'direct_earnings' => $directStats['total_earnings'] ?: 0,
            'level2_invites' => $level2Stats['level2_invites'] ?: 0,
            'level2_earnings' => $level2Stats['level2_earnings'] ?: 0,
            'total_earnings' => ($directStats['total_earnings'] ?: 0) + ($level2Stats['level2_earnings'] ?: 0)
        ];
    }

    /**
     * 申请提现
     */
    public function requestWithdraw($userId, $pointsAmount, $accountType, $accountInfo) {
        // 验证提现条件
        $validation = $this->validateWithdrawRequest($userId, $pointsAmount);
        if (!$validation['success']) {
            return $validation;
        }

        // 计算提现金额和手续费
        $pointsRate = $this->getSetting('points_to_money_rate', 100);
        $feeRate = $this->getSetting('withdraw_fee_rate', 0);

        $moneyAmount = $pointsAmount / $pointsRate;
        $feeAmount = $moneyAmount * $feeRate / 100;
        $actualAmount = $moneyAmount - $feeAmount;

        // 冻结用户积分
        $this->freezePoints($userId, $pointsAmount);

        // 创建提现申请
        $this->db->execute("
            INSERT INTO {$this->db->getPrefix()}withdraw_requests
            (user_id, points_amount, money_amount, fee_amount, actual_amount, account_type, account_info)
            VALUES (:user_id, :points_amount, :money_amount, :fee_amount, :actual_amount, :account_type, :account_info)
        ", [
            'user_id' => $userId,
            'points_amount' => $pointsAmount,
            'money_amount' => $moneyAmount,
            'fee_amount' => $feeAmount,
            'actual_amount' => $actualAmount,
            'account_type' => $accountType,
            'account_info' => json_encode($accountInfo)
        ]);

        return [
            'success' => true,
            'message' => '提现申请提交成功，请等待审核',
            'withdraw_id' => $this->db->lastInsertId(),
            'actual_amount' => $actualAmount
        ];
    }

    /**
     * 验证提现请求
     */
    private function validateWithdrawRequest($userId, $pointsAmount) {
        // 检查提现功能是否启用
        if (!$this->getSetting('withdraw_enabled', true)) {
            return ['success' => false, 'error' => '提现功能暂时关闭'];
        }

        // 获取用户信息
        $user = $this->db->fetch("
            SELECT points, frozen_points FROM {$this->db->getPrefix()}users
            WHERE id = :user_id
        ", ['user_id' => $userId]);

        if (!$user) {
            return ['success' => false, 'error' => '用户不存在'];
        }

        // 检查积分门槛
        $threshold = $this->getSetting('withdraw_threshold', 1000);
        if ($pointsAmount < $threshold) {
            return ['success' => false, 'error' => "提现积分不能少于{$threshold}"];
        }

        // 检查可用积分
        $availablePoints = $user['points'] - ($user['frozen_points'] ?: 0);
        if ($pointsAmount > $availablePoints) {
            return ['success' => false, 'error' => '可用积分不足'];
        }

        // 检查每日提现限额
        $dailyLimit = $this->getSetting('max_withdraw_daily', 10000);
        $todayWithdrawn = $this->getTodayWithdrawnPoints($userId);
        if ($todayWithdrawn + $pointsAmount > $dailyLimit) {
            return ['success' => false, 'error' => "超出每日提现限额({$dailyLimit}积分)"];
        }

        // 检查是否有待处理的提现申请
        $pendingWithdraw = $this->db->fetch("
            SELECT id FROM {$this->db->getPrefix()}withdraw_requests
            WHERE user_id = :user_id AND status IN ('pending', 'processing', 'approved')
            LIMIT 1
        ", ['user_id' => $userId]);

        if ($pendingWithdraw) {
            return ['success' => false, 'error' => '您有待处理的提现申请，请等待处理完成'];
        }

        return ['success' => true];
    }

    /**
     * 冻结用户积分
     */
    private function freezePoints($userId, $points) {
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}users
            SET frozen_points = COALESCE(frozen_points, 0) + :points
            WHERE id = :user_id
        ", [
            'points' => $points,
            'user_id' => $userId
        ]);
    }

    /**
     * 获取今日已提现积分
     */
    private function getTodayWithdrawnPoints($userId) {
        $result = $this->db->fetch("
            SELECT COALESCE(SUM(points_amount), 0) as total
            FROM {$this->db->getPrefix()}withdraw_requests
            WHERE user_id = :user_id
            AND DATE(created_at) = CURDATE()
            AND status != 'rejected'
        ", ['user_id' => $userId]);

        return $result ? $result['total'] : 0;
    }

    /**
     * 获取用户提现记录
     */
    public function getUserWithdrawHistory($userId, $limit = 20, $offset = 0) {
        return $this->db->fetchAll("
            SELECT * FROM {$this->db->getPrefix()}withdraw_requests
            WHERE user_id = :user_id
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ", [
            'user_id' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * 获取用户邀请收益记录
     */
    public function getUserEarningsHistory($userId, $limit = 20, $offset = 0) {
        return $this->db->fetchAll("
            SELECT ie.*, u.username as invitee_username, u.nickname as invitee_nickname
            FROM {$this->db->getPrefix()}invite_earnings ie
            LEFT JOIN {$this->db->getPrefix()}users u ON ie.invitee_id = u.id
            WHERE ie.inviter_id = :user_id
            ORDER BY ie.created_at DESC
            LIMIT :limit OFFSET :offset
        ", [
            'user_id' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }

    /**
     * 获取用户邀请的用户列表
     */
    public function getUserInvitees($userId, $limit = 20, $offset = 0) {
        return $this->db->fetchAll("
            SELECT u.id, u.username, u.nickname, u.created_at, u.last_login_time,
                   COALESCE(SUM(ie.points_earned), 0) as total_earnings
            FROM {$this->db->getPrefix()}users u
            LEFT JOIN {$this->db->getPrefix()}invite_earnings ie ON u.id = ie.invitee_id AND ie.inviter_id = :user_id1
            WHERE u.inviter_id = :user_id2
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT :limit OFFSET :offset
        ", [
            'user_id1' => $userId,
            'user_id2' => $userId,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }
}
