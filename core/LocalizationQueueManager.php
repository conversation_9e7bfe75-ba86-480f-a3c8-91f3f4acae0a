<?php

class LocalizationQueueManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 将套图添加到本地化队列
     */
    public function addAlbumToQueue($albumId, $priority = 5) {
        // 获取套图信息
        $album = $this->db->fetch("SELECT * FROM {$this->db->getPrefix()}albums WHERE id = :id", ['id' => $albumId]);
        if (!$album) {
            throw new Exception('套图不存在');
        }
        
        // 获取未本地化的图片
        $images = $this->db->fetchAll("
            SELECT id, file_url 
            FROM {$this->db->getPrefix()}album_images 
            WHERE album_id = :album_id 
            AND (local_file_url IS NULL OR local_file_url = '')
            ORDER BY sort ASC, id ASC
        ", ['album_id' => $albumId]);
        
        if (empty($images)) {
            return ['success' => true, 'message' => '该套图所有图片已本地化', 'added_count' => 0];
        }
        
        // 批量插入队列
        $addedCount = 0;
        foreach ($images as $image) {
            // 检查是否已在队列中
            $existing = $this->db->fetch("
                SELECT id FROM {$this->db->getPrefix()}localization_queue 
                WHERE image_id = :image_id AND status IN ('pending', 'processing', 'retry')
            ", ['image_id' => $image['id']]);
            
            if (!$existing) {
                $this->db->execute("
                    INSERT INTO {$this->db->getPrefix()}localization_queue 
                    (album_id, image_id, image_url, priority, status) 
                    VALUES (:album_id, :image_id, :image_url, :priority, 'pending')
                ", [
                    'album_id' => $albumId,
                    'image_id' => $image['id'],
                    'image_url' => $image['file_url'],
                    'priority' => $priority
                ]);
                $addedCount++;
            }
        }
        
        return [
            'success' => true, 
            'message' => "成功添加 {$addedCount} 张图片到本地化队列",
            'added_count' => $addedCount,
            'total_images' => count($images)
        ];
    }
    
    /**
     * 批量添加多个套图到队列
     */
    public function addBatchToQueue($albumIds, $batchName = null, $priority = 5) {
        if (empty($albumIds)) {
            throw new Exception('套图ID列表不能为空');
        }
        
        // 创建批次记录
        if (!$batchName) {
            $batchName = '批量本地化_' . date('Y-m-d_H-i-s');
        }
        
        $this->db->execute("
            INSERT INTO {$this->db->getPrefix()}localization_batches 
            (batch_name, album_ids, status) 
            VALUES (:batch_name, :album_ids, 'pending')
        ", [
            'batch_name' => $batchName,
            'album_ids' => json_encode($albumIds)
        ]);
        
        $batchId = $this->db->lastInsertId();
        $totalAdded = 0;
        $totalImages = 0;
        
        foreach ($albumIds as $albumId) {
            try {
                $result = $this->addAlbumToQueue($albumId, $priority);
                $totalAdded += $result['added_count'];
                $totalImages += $result['total_images'];
            } catch (Exception $e) {
                error_log("添加套图 {$albumId} 到队列失败: " . $e->getMessage());
            }
        }
        
        // 更新批次统计
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_batches 
            SET total_images = :total_images, status = 'processing'
            WHERE id = :batch_id
        ", [
            'total_images' => $totalImages,
            'batch_id' => $batchId
        ]);
        
        return [
            'success' => true,
            'batch_id' => $batchId,
            'batch_name' => $batchName,
            'total_albums' => count($albumIds),
            'total_images' => $totalImages,
            'added_count' => $totalAdded,
            'message' => "批次创建成功，共添加 {$totalAdded} 张图片到队列"
        ];
    }
    
    /**
     * 获取下一个待处理的任务
     */
    public function getNextTask($workerName) {
        // 更新工作进程心跳
        $this->updateWorkerHeartbeat($workerName);
        
        // 获取优先级最高的待处理任务
        $task = $this->db->fetch("
            SELECT * FROM {$this->db->getPrefix()}localization_queue 
            WHERE status IN ('pending', 'retry') 
            AND attempts < max_attempts
            ORDER BY priority ASC, created_at ASC 
            LIMIT 1
        ");
        
        if (!$task) {
            return null;
        }
        
        // 标记任务为处理中
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_queue 
            SET status = 'processing', started_at = NOW(), attempts = attempts + 1
            WHERE id = :id
        ", ['id' => $task['id']]);
        
        // 更新工作进程状态
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_workers 
            SET status = 'working', current_task_id = :task_id, last_heartbeat = NOW()
            WHERE worker_name = :worker_name
        ", [
            'task_id' => $task['id'],
            'worker_name' => $workerName
        ]);
        
        return $task;
    }
    
    /**
     * 标记任务完成
     */
    public function markTaskCompleted($taskId, $localFileUrl, $fileSize = null) {
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_queue 
            SET status = 'completed', local_file_url = :local_file_url, 
                file_size = :file_size, completed_at = NOW()
            WHERE id = :id
        ", [
            'id' => $taskId,
            'local_file_url' => $localFileUrl,
            'file_size' => $fileSize
        ]);
        
        // 更新对应的图片记录
        $task = $this->db->fetch("SELECT image_id FROM {$this->db->getPrefix()}localization_queue WHERE id = :id", ['id' => $taskId]);
        if ($task) {
            $this->db->execute("
                UPDATE {$this->db->getPrefix()}album_images 
                SET local_file_url = :local_file_url 
                WHERE id = :id
            ", [
                'local_file_url' => $localFileUrl,
                'id' => $task['image_id']
            ]);
        }
    }
    
    /**
     * 标记任务失败
     */
    public function markTaskFailed($taskId, $errorMessage) {
        $task = $this->db->fetch("SELECT attempts, max_attempts FROM {$this->db->getPrefix()}localization_queue WHERE id = :id", ['id' => $taskId]);
        
        if ($task && $task['attempts'] >= $task['max_attempts']) {
            // 达到最大重试次数，标记为失败
            $status = 'failed';
        } else {
            // 可以重试
            $status = 'retry';
        }
        
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_queue 
            SET status = :status, error_message = :error_message
            WHERE id = :id
        ", [
            'id' => $taskId,
            'status' => $status,
            'error_message' => $errorMessage
        ]);
    }
    
    /**
     * 更新工作进程心跳
     */
    public function updateWorkerHeartbeat($workerName) {
        $this->db->execute("
            INSERT INTO {$this->db->getPrefix()}localization_workers 
            (worker_name, last_heartbeat) 
            VALUES (:worker_name, NOW())
            ON DUPLICATE KEY UPDATE 
            last_heartbeat = NOW(), processed_count = processed_count + 1
        ", ['worker_name' => $workerName]);
    }
    
    /**
     * 获取队列统计信息
     */
    public function getQueueStats() {
        $stats = $this->db->fetch("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'retry' THEN 1 ELSE 0 END) as retry
            FROM {$this->db->getPrefix()}localization_queue
        ");
        
        return $stats;
    }
    
    /**
     * 清理失败的任务（重置为待处理）
     */
    public function resetFailedTasks() {
        $result = $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_queue 
            SET status = 'pending', attempts = 0, error_message = NULL
            WHERE status = 'failed'
        ");
        
        return $result;
    }

    /**
     * 获取活跃的工作进程
     */
    public function getActiveWorkers() {
        return $this->db->fetchAll("
            SELECT * FROM {$this->db->getPrefix()}localization_workers
            WHERE last_heartbeat > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY last_heartbeat DESC
        ");
    }

    /**
     * 清理僵死的工作进程
     */
    public function cleanupDeadWorkers() {
        // 将超过5分钟没有心跳的工作进程标记为停止
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_workers
            SET status = 'stopped', current_task_id = NULL
            WHERE last_heartbeat < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            AND status != 'stopped'
        ");

        // 将这些工作进程正在处理的任务重置为待处理
        $this->db->execute("
            UPDATE {$this->db->getPrefix()}localization_queue q
            JOIN {$this->db->getPrefix()}localization_workers w ON q.id = w.current_task_id
            SET q.status = 'pending', q.started_at = NULL
            WHERE w.status = 'stopped' AND q.status = 'processing'
        ");
    }
}
