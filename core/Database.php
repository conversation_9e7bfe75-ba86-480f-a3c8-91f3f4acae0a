<?php
/**
 * 数据库连接类
 */
require_once __DIR__ . '/Config.php';

class Database {
    private static $instance = null;
    private $pdo;
    private $config;
    
    private function __construct() {
        $this->config = require_once __DIR__ . '/../config/database.php';
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        $mysql = $this->config['mysql'];
        $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};dbname={$mysql['database']};charset={$mysql['charset']}";
        
        try {
            $this->pdo = new PDO($dsn, $mysql['username'], $mysql['password'], $mysql['options']);
        } catch (PDOException $e) {
            die('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    public function getPdo() {
        return $this->pdo;
    }
    
    public function getPrefix() {
        return $this->config['mysql']['prefix'];
    }
    
    /**
     * 执行查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log('SQL执行错误: ' . $e->getMessage() . ' SQL: ' . $sql);
            return false;
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : false;
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : [];
    }

    /**
     * 获取单个字段值
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchColumn() : false;
    }
    
    /**
     * 插入数据
     */
    public function insert($table, $data) {
        $table = $this->getPrefix() . $table;
        $fields = array_keys($data);
        $quotedFields = array_map(function($field) { return "`{$field}`"; }, $fields);
        $placeholders = ':' . implode(', :', $fields);

        $sql = "INSERT INTO {$table} (" . implode(', ', $quotedFields) . ") VALUES ({$placeholders})";
        $stmt = $this->query($sql, $data);

        return $stmt ? $this->pdo->lastInsertId() : false;
    }
    
    /**
     * 更新数据
     */
    public function update($table, $data, $where, $whereParams = []) {
        $table = $this->getPrefix() . $table;
        $setClause = [];

        foreach ($data as $field => $value) {
            $setClause[] = "`{$field}` = :{$field}";
        }

        $sql = "UPDATE {$table} SET " . implode(', ', $setClause) . " WHERE {$where}";
        $params = array_merge($data, $whereParams);

        return $this->query($sql, $params);
    }
    
    /**
     * 删除数据
     */
    public function delete($table, $where, $params = []) {
        $table = $this->getPrefix() . $table;
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        return $this->query($sql, $params);
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->pdo->beginTransaction();
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        return $this->pdo->commit();
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->pdo->rollback();
    }

    /**
     * 获取最后插入的ID
     */
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}
