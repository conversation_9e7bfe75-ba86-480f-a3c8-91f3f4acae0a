<?php

require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/FTPUploadManager.php';

class AlbumLocalizationManager {
    private $db;
    private $ftpUploader;
    private $maxRetries = 3;
    private $downloadTimeout = 30;
    private $enableWebP = false;
    
    public function __construct() {
        $this->db = Database::getInstance();

        try {
            $this->ftpUploader = new FTPUploadManager();
        } catch (Exception $e) {
            error_log("FTPUploadManager初始化失败: " . $e->getMessage());
            throw new Exception("FTP配置错误: " . $e->getMessage());
        }

        // 检查WebP转换设置
        $this->enableWebP = $this->isWebPEnabled();
    }

    /**
     * 检查是否启用WebP转换
     */
    private function isWebPEnabled() {
        $setting = $this->db->fetch("SELECT value FROM {$this->db->getPrefix()}system_config WHERE `key` = 'enable_webp_convert'");
        return $setting && $setting['value'] == '1';
    }
    
    /**
     * 本地化套图的所有图片（带进度回调）
     */
    public function localizeAlbumWithProgress($albumId, $progressCallback = null) {
        return $this->localizeAlbum($albumId, $progressCallback);
    }

    /**
     * 开始本地化单个套图（支持队列模式）
     */
    public function localizeAlbum($albumId, $progressCallback = null, $useQueue = false) {
        if ($useQueue) {
            return $this->localizeAlbumWithQueue($albumId);
        }
        // 1. 检查是否已有进行中的任务
        $existingTask = $this->getActiveTask($albumId);
        if ($existingTask) {
            // 检查任务是否超时（超过1小时）
            $createdTime = strtotime($existingTask['created_at']);
            $currentTime = time();
            $timeDiff = $currentTime - $createdTime;

            if ($timeDiff > 3600) { // 1小时 = 3600秒
                // 清理超时任务
                $this->updateTaskStatus($existingTask['id'], 'failed', '任务超时自动清理');
            } else {
                throw new Exception('该套图正在本地化中，请稍后再试');
            }
        }
        
        // 2. 获取套图信息和图片列表
        $album = $this->getAlbumById($albumId);
        if (!$album) {
            throw new Exception('套图不存在');
        }
        
        $images = $this->getAlbumImages($albumId);
        if (empty($images)) {
            throw new Exception('套图没有图片');
        }
        
        // 3. 如果已有本地化图片，先删除旧文件
        $this->deleteOldLocalImages($albumId);
        
        // 4. 创建本地化任务
        $taskId = $this->createLocalizationTask($albumId, count($images));
        
        // 5. 开始处理
        return $this->processLocalizationTask($taskId, $images, $progressCallback);
    }
    
    /**
     * 获取套图信息
     */
    private function getAlbumById($albumId) {
        return $this->db->fetch("
            SELECT * FROM {$this->db->getPrefix()}albums 
            WHERE id = :id
        ", ['id' => $albumId]);
    }
    
    /**
     * 获取套图图片列表
     */
    private function getAlbumImages($albumId) {
        return $this->db->fetchAll("
            SELECT * FROM {$this->db->getPrefix()}album_images 
            WHERE album_id = :album_id 
            ORDER BY sort ASC, id ASC
        ", ['album_id' => $albumId]);
    }
    
    /**
     * 获取活跃的本地化任务
     */
    private function getActiveTask($albumId) {
        return $this->db->fetch("
            SELECT * FROM {$this->db->getPrefix()}localization_tasks 
            WHERE album_id = :album_id AND status IN ('pending', 'processing')
        ", ['album_id' => $albumId]);
    }
    
    /**
     * 创建本地化任务
     */
    private function createLocalizationTask($albumId, $totalImages) {
        $result = $this->db->insert('localization_tasks', [
            'album_id' => $albumId,
            'status' => 'pending',
            'total_images' => $totalImages,
            'success_count' => 0,
            'failed_count' => 0
        ]);
        
        return $this->db->lastInsertId();
    }
    
    /**
     * 获取任务信息
     */
    private function getTaskById($taskId) {
        return $this->db->fetch("
            SELECT * FROM {$this->db->getPrefix()}localization_tasks 
            WHERE id = :id
        ", ['id' => $taskId]);
    }
    
    /**
     * 更新任务状态
     */
    private function updateTaskStatus($taskId, $status) {
        $this->db->update('localization_tasks', 
            ['status' => $status], 
            'id = :id', 
            ['id' => $taskId]
        );
    }
    
    /**
     * 更新任务进度
     */
    private function updateTaskProgress($taskId, $successCount, $failedCount, $failedImages = []) {
        $updateData = [
            'success_count' => $successCount,
            'failed_count' => $failedCount
        ];
        
        if (!empty($failedImages)) {
            $updateData['failed_images'] = json_encode($failedImages, JSON_UNESCAPED_UNICODE);
        }
        
        $this->db->update('localization_tasks', 
            $updateData, 
            'id = :id', 
            ['id' => $taskId]
        );
    }
    
    /**
     * 记录进度日志
     */
    private function logProgress($taskId, $message) {
        $task = $this->getTaskById($taskId);
        $currentLog = $task['error_log'] ?? '';
        $newLog = $currentLog . "\n" . date('Y-m-d H:i:s') . ': ' . $message;
        
        $this->db->update('localization_tasks', 
            ['error_log' => $newLog], 
            'id = :id', 
            ['id' => $taskId]
        );
    }
    
    /**
     * 删除旧的本地化图片
     */
    private function deleteOldLocalImages($albumId) {
        $oldImages = $this->db->fetchAll("
            SELECT local_file_url FROM {$this->db->getPrefix()}album_images 
            WHERE album_id = :album_id AND local_file_url IS NOT NULL
        ", ['album_id' => $albumId]);
        
        foreach ($oldImages as $image) {
            try {
                $this->ftpUploader->deleteFile($image['local_file_url']);
            } catch (Exception $e) {
                // 忽略删除失败的错误
                error_log('删除旧图片失败: ' . $e->getMessage());
            }
        }
        
        // 清空数据库中的本地化URL
        $this->db->query("
            UPDATE {$this->db->getPrefix()}album_images 
            SET local_file_url = NULL 
            WHERE album_id = :album_id
        ", ['album_id' => $albumId]);
    }
    
    /**
     * 处理本地化任务
     */
    private function processLocalizationTask($taskId, $images, $progressCallback = null) {
        $task = $this->getTaskById($taskId);
        $albumId = $task['album_id'];

        $successCount = 0;
        $failedCount = 0;
        $failedImages = [];

        // 更新任务状态为处理中
        $this->updateTaskStatus($taskId, 'processing');
        $this->logProgress($taskId, "开始处理套图 #{$albumId}，共 " . count($images) . " 张图片");

        // 发送开始进度回调
        error_log("processLocalizationTask开始，图片数量: " . count($images));
        if ($progressCallback) {
            error_log("发送开始进度回调");
            $progressCallback([
                'type' => 'start',
                'current' => 0,
                'total' => count($images),
                'success_count' => 0,
                'failed_count' => 0,
                'message' => "开始处理套图，共 " . count($images) . " 张图片"
            ]);
        }
        
        foreach ($images as $index => $image) {
            // 发送开始处理单张图片的进度
            if ($progressCallback) {
                $progressCallback([
                    'type' => 'processing',
                    'current' => $index + 1,
                    'total' => count($images),
                    'success_count' => $successCount,
                    'failed_count' => $failedCount,
                    'message' => "正在处理第 " . ($index + 1) . " 张图片..."
                ]);
            }

            $result = $this->processImage($albumId, $image, $index + 1);
            
            if ($result['success']) {
                $successCount++;
                
                // 更新图片的本地化URL
                $this->db->update('album_images', 
                    ['local_file_url' => $result['path']], 
                    'id = :id', 
                    ['id' => $image['id']]
                );
                
                // 实时更新进度
                $this->updateTaskProgress($taskId, $successCount, $failedCount);
                
                // 记录成功日志
                $this->logProgress($taskId, "图片 " . ($index + 1) . " 本地化成功: " . $result['path']);

                // 调用进度回调
                if ($progressCallback) {
                    $progressCallback([
                        'type' => 'progress',
                        'current' => $index + 1,
                        'total' => count($images),
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'message' => "图片 " . ($index + 1) . " 本地化成功",
                        'file_path' => $result['path']
                    ]);
                }

                // 输出进度信息，保持连接活跃
                if (php_sapi_name() !== 'cli' && !$progressCallback) {
                    echo " "; // 输出空格保持连接
                    flush();
                }

            } else {
                $failedImages[] = [
                    'image_id' => $image['id'],
                    'index' => $index + 1,
                    'url' => $image['file_url'],
                    'error' => $result['error'],
                    'retry_count' => 0
                ];
                $failedCount++;
                
                // 实时更新进度
                $this->updateTaskProgress($taskId, $successCount, $failedCount, $failedImages);
                
                // 记录失败日志
                $this->logProgress($taskId, "图片 " . ($index + 1) . " 本地化失败: " . $result['error']);

                // 调用进度回调
                if ($progressCallback) {
                    $progressCallback([
                        'type' => 'progress',
                        'current' => $index + 1,
                        'total' => count($images),
                        'success_count' => $successCount,
                        'failed_count' => $failedCount,
                        'message' => "图片 " . ($index + 1) . " 本地化失败: " . $result['error'],
                        'error' => $result['error']
                    ]);
                }
            }
            
            // 每张图片处理后暂停1秒，避免服务器压力
            sleep(1);
        }
        
        // 处理失败的图片 - 重试机制
        if (!empty($failedImages)) {
            $failedImages = $this->retryFailedImages($taskId, $failedImages, $successCount);
            $failedCount = count($failedImages);
        }
        
        // 最终结果处理
        if (empty($failedImages)) {
            // 全部成功
            $this->completeTask($taskId, $albumId, $successCount);
            return [
                'success' => true,
                'message' => "套图本地化完成，成功: {$successCount}张",
                'total' => count($images),
                'success_count' => $successCount,
                'failed_count' => 0
            ];
        } else {
            // 部分失败
            $this->failTask($taskId, $failedImages);
            return [
                'success' => false,
                'message' => "套图本地化完成，成功: {$successCount}张，失败: {$failedCount}张",
                'total' => count($images),
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'failed_images' => $failedImages
            ];
        }
    }
    
    /**
     * 处理单张图片
     */
    private function processImage($albumId, $image, $index) {
        try {
            // 1. 下载图片到临时文件
            $tempFile = $this->downloadImageWithRetry($image['file_url']);
            if (!$tempFile) {
                return ['success' => false, 'error' => '下载失败'];
            }
            
            // 2. 验证图片文件
            if (!$this->ftpUploader->validateImage($tempFile)) {
                unlink($tempFile);
                return ['success' => false, 'error' => '图片文件无效'];
            }

            // 2.5. WebP转换（如果启用）
            if ($this->enableWebP) {
                $webpFile = $this->convertToWebP($tempFile);
                if ($webpFile) {
                    unlink($tempFile); // 删除原始文件
                    $tempFile = $webpFile; // 使用WebP文件
                }
            }

            // 3. 上传到FTP
            $remotePath = $this->ftpUploader->uploadFile($tempFile, 'album', [
                'album_id' => $albumId,
                'index' => $index
            ]);
            
            // 4. 清理临时文件
            unlink($tempFile);
            
            if ($remotePath) {
                return ['success' => true, 'path' => $remotePath];
            } else {
                return ['success' => false, 'error' => 'FTP上传失败'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * 下载图片（带重试）
     */
    private function downloadImageWithRetry($imageUrl, $maxRetries = 3) {
        for ($i = 0; $i < $maxRetries; $i++) {
            $tempFile = $this->downloadImage($imageUrl);
            if ($tempFile) {
                return $tempFile;
            }
            
            // 重试前等待
            if ($i < $maxRetries - 1) {
                sleep(2);
            }
        }
        
        return false;
    }
    
    /**
     * 下载图片
     */
    private function downloadImage($imageUrl) {
        // 从URL中提取文件扩展名
        $extension = $this->getImageExtensionFromUrl($imageUrl);

        // 创建带扩展名的临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'album_img_') . '.' . $extension;

        $context = stream_context_create([
            'http' => [
                'timeout' => $this->downloadTimeout,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);

        $imageData = @file_get_contents($imageUrl, false, $context);

        if ($imageData === false) {
            return false;
        }

        if (file_put_contents($tempFile, $imageData) === false) {
            return false;
        }

        return $tempFile;
    }

    /**
     * 从URL中提取图片扩展名
     */
    private function getImageExtensionFromUrl($imageUrl) {
        // 先尝试从URL路径中获取扩展名
        $parsedUrl = parse_url($imageUrl);
        $path = $parsedUrl['path'] ?? '';
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

        // 如果URL中有有效的图片扩展名，使用它
        $validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        if (in_array($extension, $validExtensions)) {
            return $extension;
        }

        // 如果URL中没有扩展名或扩展名无效，默认使用jpg
        return 'jpg';
    }

    /**
     * 转换图片为WebP格式
     */
    private function convertToWebP($sourceFile) {
        // 检查GD库是否支持WebP
        if (!function_exists('imagewebp')) {
            error_log("WebP转换失败: GD库不支持WebP");
            return false;
        }

        // 获取图片信息
        $imageInfo = getimagesize($sourceFile);
        if (!$imageInfo) {
            error_log("WebP转换失败: 无法获取图片信息");
            return false;
        }

        $mimeType = $imageInfo['mime'];
        $image = null;

        // 根据MIME类型创建图片资源
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($sourceFile);
                break;
            case 'image/png':
                $image = imagecreatefrompng($sourceFile);
                // 保持PNG透明度
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($sourceFile);
                break;
            case 'image/webp':
                // 已经是WebP格式，直接返回
                return $sourceFile;
            default:
                error_log("WebP转换失败: 不支持的图片格式 {$mimeType}");
                return false;
        }

        if (!$image) {
            error_log("WebP转换失败: 无法创建图片资源");
            return false;
        }

        // 创建WebP文件路径
        $webpFile = preg_replace('/\.[^.]+$/', '.webp', $sourceFile);

        // 转换为WebP（质量设置为85，平衡文件大小和质量）
        $success = imagewebp($image, $webpFile, 85);

        // 释放内存
        imagedestroy($image);

        if ($success) {
            return $webpFile;
        } else {
            error_log("WebP转换失败: imagewebp函数执行失败");
            return false;
        }
    }

    /**
     * 重试失败的图片
     */
    private function retryFailedImages($taskId, $failedImages, &$successCount) {
        $this->logProgress($taskId, "开始重试失败的图片，共 " . count($failedImages) . " 张");

        $stillFailed = [];

        foreach ($failedImages as $failedImage) {
            if ($failedImage['retry_count'] >= $this->maxRetries) {
                $stillFailed[] = $failedImage;
                continue;
            }

            // 增加重试次数
            $failedImage['retry_count']++;

            $this->logProgress($taskId, "重试图片 {$failedImage['index']} (第{$failedImage['retry_count']}次)");

            // 获取图片信息
            $image = $this->db->fetch("
                SELECT * FROM {$this->db->getPrefix()}album_images
                WHERE id = :id
            ", ['id' => $failedImage['image_id']]);

            if (!$image) {
                $stillFailed[] = $failedImage;
                continue;
            }

            // 重试处理
            $result = $this->processImage(
                $this->getTaskById($taskId)['album_id'],
                $image,
                $failedImage['index']
            );

            if ($result['success']) {
                $successCount++;

                // 更新图片的本地化URL
                $this->db->update('album_images',
                    ['local_file_url' => $result['path']],
                    'id = :id',
                    ['id' => $image['id']]
                );

                $this->logProgress($taskId, "图片 {$failedImage['index']} 重试成功");

                // 更新进度
                $this->updateTaskProgress($taskId, $successCount, count($stillFailed));

            } else {
                $failedImage['error'] = $result['error'];
                $stillFailed[] = $failedImage;
                $this->logProgress($taskId, "图片 {$failedImage['index']} 重试失败: " . $result['error']);
            }

            // 重试间隔
            sleep(2);
        }

        return $stillFailed;
    }

    /**
     * 完成任务
     */
    private function completeTask($taskId, $albumId, $successCount) {
        // 1. 更新任务状态
        $this->updateTaskStatus($taskId, 'completed');

        // 2. 更新套图的本地化状态
        $this->db->update('albums',
            [
                'localization_status' => 'completed',
                'localized_count' => $successCount
            ],
            'id = :id',
            ['id' => $albumId]
        );

        // 3. 记录完成日志
        $this->logProgress($taskId, "套图本地化任务完成，共成功 {$successCount} 张图片");
    }

    /**
     * 标记任务失败
     */
    private function failTask($taskId, $failedImages) {
        $this->updateTaskStatus($taskId, 'failed');
        $this->updateTaskProgress($taskId, 0, count($failedImages), $failedImages);

        $task = $this->getTaskById($taskId);
        $albumId = $task['album_id'];
        $successCount = $task['success_count'];

        // 更新套图状态
        $this->db->update('albums',
            [
                'localization_status' => 'failed',
                'localized_count' => $successCount
            ],
            'id = :id',
            ['id' => $albumId]
        );
    }

    /**
     * 批量本地化所有套图
     */
    public function batchLocalizeAll() {
        // 获取所有需要本地化的套图
        $albums = $this->db->fetchAll("
            SELECT a.id, a.title, COUNT(ai.id) as image_count
            FROM {$this->db->getPrefix()}albums a
            LEFT JOIN {$this->db->getPrefix()}album_images ai ON a.id = ai.album_id
            WHERE a.localization_status IN ('none', 'failed')
            AND ai.local_file_url IS NULL
            GROUP BY a.id, a.title
            HAVING image_count > 0
            ORDER BY a.id ASC
        ");

        $totalAlbums = count($albums);
        $processedCount = 0;
        $successCount = 0;
        $failedCount = 0;

        // 设置输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }
        ob_start();

        foreach ($albums as $album) {
            $processedCount++;

            try {
                $result = $this->localizeAlbum($album['id']);

                if ($result['success']) {
                    $successCount++;
                } else {
                    $failedCount++;
                }

                // 返回进度给前端
                echo json_encode([
                    'type' => 'progress',
                    'album_id' => $album['id'],
                    'album_title' => $album['title'],
                    'current' => $processedCount,
                    'total' => $totalAlbums,
                    'result' => $result
                ]) . "\n";

                // 刷新输出缓冲区
                ob_flush();
                flush();

                // 每个套图处理完后休息5秒
                sleep(5);

            } catch (Exception $e) {
                $failedCount++;

                echo json_encode([
                    'type' => 'error',
                    'album_id' => $album['id'],
                    'album_title' => $album['title'],
                    'error' => $e->getMessage()
                ]) . "\n";

                ob_flush();
                flush();
            }
        }

        // 返回最终结果
        echo json_encode([
            'type' => 'complete',
            'total' => $totalAlbums,
            'success' => $successCount,
            'failed' => $failedCount
        ]) . "\n";

        ob_end_flush();
    }

    /**
     * 使用队列模式本地化套图
     */
    private function localizeAlbumWithQueue($albumId) {
        require_once __DIR__ . '/LocalizationQueueManager.php';
        $queueManager = new LocalizationQueueManager();

        try {
            $result = $queueManager->addAlbumToQueue($albumId, 5);

            return [
                'success' => true,
                'message' => $result['message'],
                'queue_mode' => true,
                'added_count' => $result['added_count'],
                'total_images' => $result['total_images']
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'queue_mode' => true
            ];
        }
    }

    /**
     * 批量添加套图到队列
     */
    public function batchLocalizeWithQueue($albumIds, $batchName = null) {
        require_once __DIR__ . '/LocalizationQueueManager.php';
        $queueManager = new LocalizationQueueManager();

        try {
            $result = $queueManager->addBatchToQueue($albumIds, $batchName, 5);

            return [
                'success' => true,
                'message' => $result['message'],
                'batch_id' => $result['batch_id'],
                'batch_name' => $result['batch_name'],
                'total_albums' => $result['total_albums'],
                'total_images' => $result['total_images'],
                'added_count' => $result['added_count']
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
