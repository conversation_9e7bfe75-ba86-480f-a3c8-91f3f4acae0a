<?php
require_once __DIR__ . '/../core/Database.php';

/**
 * 图片处理和FTP上传类
 */
class ImageHandler {
    private $db;
    private $ftpConfig;
    private $ftpConnection;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadFtpConfig();
    }
    
    /**
     * 加载FTP配置
     */
    private function loadFtpConfig() {
        $configs = $this->db->fetchAll(
            "SELECT `key`, `value` FROM {$this->db->getPrefix()}system_config WHERE `group` = 'ftp'"
        );
        
        $this->ftpConfig = [];
        foreach ($configs as $config) {
            $this->ftpConfig[$config['key']] = $config['value'];
        }
    }
    
    /**
     * 连接FTP服务器
     */
    private function connectFtp() {
        if ($this->ftpConnection) {
            return true;
        }
        
        if (empty($this->ftpConfig['ftp_host'])) {
            return false;
        }
        
        $this->ftpConnection = ftp_connect($this->ftpConfig['ftp_host'], $this->ftpConfig['ftp_port'] ?: 21);
        
        if (!$this->ftpConnection) {
            return false;
        }
        
        if (!ftp_login($this->ftpConnection, $this->ftpConfig['ftp_username'], $this->ftpConfig['ftp_password'])) {
            ftp_close($this->ftpConnection);
            $this->ftpConnection = null;
            return false;
        }
        
        ftp_pasv($this->ftpConnection, true);
        return true;
    }
    
    /**
     * 关闭FTP连接
     */
    private function closeFtp() {
        if ($this->ftpConnection) {
            ftp_close($this->ftpConnection);
            $this->ftpConnection = null;
        }
    }
    
    /**
     * 处理上传的图片
     */
    public function processUploadedImage($file, $albumId, $enableWebp = null) {
        if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => '文件上传失败'];
        }
        
        // 检查文件类型
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'message' => '不支持的图片格式'];
        }
        
        // 检查文件大小（最大10MB）
        if ($file['size'] > 10 * 1024 * 1024) {
            return ['success' => false, 'message' => '文件大小超过限制'];
        }
        
        $originalName = $file['name'];
        $tempPath = $file['tmp_name'];
        
        // 获取图片信息
        $imageInfo = getimagesize($tempPath);
        if (!$imageInfo) {
            return ['success' => false, 'message' => '无效的图片文件'];
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $format = $this->getImageFormat($imageInfo['mime']);
        
        // 生成文件名
        $fileName = $this->generateFileName($format);
        $localPath = $this->getLocalPath($albumId, $fileName);
        
        // 确保目录存在
        $localDir = dirname($localPath);
        if (!is_dir($localDir)) {
            mkdir($localDir, 0755, true);
        }
        
        // 检查是否需要转换为WebP
        if ($enableWebp === null) {
            $enableWebp = $this->getConfig('enable_webp_convert', false);
        }
        
        $isWebp = false;
        $finalPath = $localPath;
        
        if ($enableWebp && $format !== 'webp' && function_exists('imagewebp')) {
            $webpFileName = $this->generateFileName('webp');
            $webpPath = $this->getLocalPath($albumId, $webpFileName);
            
            if ($this->convertToWebp($tempPath, $webpPath, $format)) {
                $finalPath = $webpPath;
                $fileName = $webpFileName;
                $format = 'webp';
                $isWebp = true;
            }
        }
        
        // 如果没有转换为WebP，则复制原图
        if (!$isWebp) {
            if (!move_uploaded_file($tempPath, $finalPath)) {
                return ['success' => false, 'message' => '文件保存失败'];
            }
        }
        
        // 上传到FTP服务器
        $ftpPath = $this->uploadToFtp($finalPath, $albumId, $fileName);
        $fileUrl = $this->getFtpUrl($albumId, $fileName);
        
        // 生成缩略图
        $thumbnailUrl = $this->generateThumbnail($finalPath, $albumId, $fileName);
        
        // 保存到数据库
        $imageData = [
            'album_id' => $albumId,
            'filename' => $fileName,
            'original_name' => $originalName,
            'file_path' => $finalPath,
            'file_url' => $fileUrl,
            'thumbnail_url' => $thumbnailUrl,
            'file_size' => filesize($finalPath),
            'width' => $width,
            'height' => $height,
            'format' => $format,
            'is_webp' => $isWebp ? 1 : 0
        ];
        
        $imageId = $this->db->insert('album_images', $imageData);
        
        if (!$imageId) {
            // 清理文件
            @unlink($finalPath);
            return ['success' => false, 'message' => '数据库保存失败'];
        }
        
        return ['success' => true, 'image_id' => $imageId, 'image_data' => $imageData];
    }
    
    /**
     * 从URL下载并处理图片（用于采集）
     */
    public function processImageFromUrl($url, $albumId, $enableWebp = null) {
        // 下载图片
        $tempPath = $this->downloadImage($url);
        if (!$tempPath) {
            return ['success' => false, 'message' => '图片下载失败'];
        }
        
        // 获取图片信息
        $imageInfo = getimagesize($tempPath);
        if (!$imageInfo) {
            @unlink($tempPath);
            return ['success' => false, 'message' => '无效的图片文件'];
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $format = $this->getImageFormat($imageInfo['mime']);
        $originalName = basename(parse_url($url, PHP_URL_PATH));
        
        // 生成文件名
        $fileName = $this->generateFileName($format);
        $localPath = $this->getLocalPath($albumId, $fileName);
        
        // 确保目录存在
        $localDir = dirname($localPath);
        if (!is_dir($localDir)) {
            mkdir($localDir, 0755, true);
        }
        
        // 检查是否需要转换为WebP
        if ($enableWebp === null) {
            $enableWebp = $this->getConfig('enable_webp_convert', false);
        }
        
        $isWebp = false;
        $finalPath = $localPath;
        
        if ($enableWebp && $format !== 'webp' && function_exists('imagewebp')) {
            $webpFileName = $this->generateFileName('webp');
            $webpPath = $this->getLocalPath($albumId, $webpFileName);
            
            if ($this->convertToWebp($tempPath, $webpPath, $format)) {
                $finalPath = $webpPath;
                $fileName = $webpFileName;
                $format = 'webp';
                $isWebp = true;
                @unlink($tempPath);
            }
        }
        
        // 如果没有转换为WebP，则移动原图
        if (!$isWebp) {
            if (!rename($tempPath, $finalPath)) {
                @unlink($tempPath);
                return ['success' => false, 'message' => '文件保存失败'];
            }
        }
        
        // 上传到FTP服务器
        $ftpPath = $this->uploadToFtp($finalPath, $albumId, $fileName);
        $fileUrl = $this->getFtpUrl($albumId, $fileName);
        
        // 生成缩略图
        $thumbnailUrl = $this->generateThumbnail($finalPath, $albumId, $fileName);
        
        // 保存到数据库
        $imageData = [
            'album_id' => $albumId,
            'filename' => $fileName,
            'original_name' => $originalName,
            'file_path' => $finalPath,
            'file_url' => $fileUrl,
            'thumbnail_url' => $thumbnailUrl,
            'file_size' => filesize($finalPath),
            'width' => $width,
            'height' => $height,
            'format' => $format,
            'is_webp' => $isWebp ? 1 : 0
        ];
        
        $imageId = $this->db->insert('album_images', $imageData);
        
        if (!$imageId) {
            // 清理文件
            @unlink($finalPath);
            return ['success' => false, 'message' => '数据库保存失败'];
        }
        
        return ['success' => true, 'image_id' => $imageId, 'image_data' => $imageData];
    }
    
    /**
     * 下载图片
     */
    private function downloadImage($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            ]
        ]);
        
        $imageData = file_get_contents($url, false, $context);
        if (!$imageData) {
            return false;
        }
        
        $tempPath = tempnam(sys_get_temp_dir(), 'img_');
        if (file_put_contents($tempPath, $imageData) === false) {
            return false;
        }
        
        return $tempPath;
    }
    
    /**
     * 转换图片为WebP格式
     */
    private function convertToWebp($sourcePath, $targetPath, $sourceFormat) {
        $quality = $this->getConfig('image_quality', 85);
        
        switch ($sourceFormat) {
            case 'jpeg':
            case 'jpg':
                $image = imagecreatefromjpeg($sourcePath);
                break;
            case 'png':
                $image = imagecreatefrompng($sourcePath);
                // 保持透明度
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case 'gif':
                $image = imagecreatefromgif($sourcePath);
                break;
            default:
                return false;
        }
        
        if (!$image) {
            return false;
        }
        
        $result = imagewebp($image, $targetPath, $quality);
        imagedestroy($image);
        
        return $result;
    }
    
    /**
     * 上传文件到FTP
     */
    private function uploadToFtp($localPath, $albumId, $fileName) {
        if (!$this->connectFtp()) {
            return false;
        }
        
        $remotePath = $this->getFtpPath($albumId, $fileName);
        $remoteDir = dirname($remotePath);
        
        // 创建远程目录
        $this->createFtpDir($remoteDir);
        
        $result = ftp_put($this->ftpConnection, $remotePath, $localPath, FTP_BINARY);
        $this->closeFtp();
        
        return $result ? $remotePath : false;
    }
    
    /**
     * 创建FTP目录
     */
    private function createFtpDir($dir) {
        $dirs = explode('/', trim($dir, '/'));
        $currentDir = '/';
        
        foreach ($dirs as $dirName) {
            if (empty($dirName)) continue;
            
            $currentDir .= $dirName . '/';
            if (!@ftp_chdir($this->ftpConnection, $currentDir)) {
                ftp_mkdir($this->ftpConnection, $currentDir);
            }
        }
    }
    
    /**
     * 生成缩略图
     */
    private function generateThumbnail($imagePath, $albumId, $fileName) {
        $thumbWidth = 300;
        $thumbHeight = 200;
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return null;
        }
        
        $srcWidth = $imageInfo[0];
        $srcHeight = $imageInfo[1];
        $format = $this->getImageFormat($imageInfo['mime']);
        
        // 计算缩略图尺寸（保持比例）
        $ratio = min($thumbWidth / $srcWidth, $thumbHeight / $srcHeight);
        $newWidth = intval($srcWidth * $ratio);
        $newHeight = intval($srcHeight * $ratio);
        
        // 创建源图像
        switch ($format) {
            case 'jpeg':
            case 'jpg':
                $srcImage = imagecreatefromjpeg($imagePath);
                break;
            case 'png':
                $srcImage = imagecreatefrompng($imagePath);
                break;
            case 'gif':
                $srcImage = imagecreatefromgif($imagePath);
                break;
            case 'webp':
                $srcImage = imagecreatefromwebp($imagePath);
                break;
            default:
                return null;
        }
        
        if (!$srcImage) {
            return null;
        }
        
        // 创建缩略图
        $thumbImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // 保持透明度
        if ($format === 'png' || $format === 'gif') {
            imagealphablending($thumbImage, false);
            imagesavealpha($thumbImage, true);
            $transparent = imagecolorallocatealpha($thumbImage, 255, 255, 255, 127);
            imagefill($thumbImage, 0, 0, $transparent);
        }
        
        imagecopyresampled($thumbImage, $srcImage, 0, 0, 0, 0, $newWidth, $newHeight, $srcWidth, $srcHeight);
        
        // 保存缩略图
        $thumbFileName = 'thumb_' . $fileName;
        $thumbPath = $this->getLocalPath($albumId, $thumbFileName);
        
        $result = false;
        switch ($format) {
            case 'jpeg':
            case 'jpg':
                $result = imagejpeg($thumbImage, $thumbPath, 85);
                break;
            case 'png':
                $result = imagepng($thumbImage, $thumbPath, 8);
                break;
            case 'gif':
                $result = imagegif($thumbImage, $thumbPath);
                break;
            case 'webp':
                $result = imagewebp($thumbImage, $thumbPath, 85);
                break;
        }
        
        imagedestroy($srcImage);
        imagedestroy($thumbImage);
        
        if ($result) {
            // 上传缩略图到FTP
            $this->uploadToFtp($thumbPath, $albumId, $thumbFileName);
            return $this->getFtpUrl($albumId, $thumbFileName);
        }
        
        return null;
    }
    
    /**
     * 获取图片格式
     */
    private function getImageFormat($mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return 'jpeg';
            case 'image/png':
                return 'png';
            case 'image/gif':
                return 'gif';
            case 'image/webp':
                return 'webp';
            default:
                return 'jpeg';
        }
    }
    
    /**
     * 生成文件名
     */
    private function generateFileName($format) {
        return date('YmdHis') . '_' . mt_rand(1000, 9999) . '.' . $format;
    }
    
    /**
     * 获取本地路径
     */
    private function getLocalPath($albumId, $fileName) {
        $baseDir = __DIR__ . '/../uploads/albums/' . intval($albumId / 1000);
        return $baseDir . '/' . $fileName;
    }
    
    /**
     * 获取FTP路径
     */
    private function getFtpPath($albumId, $fileName) {
        $basePath = rtrim($this->ftpConfig['ftp_path'], '/') . '/albums/' . intval($albumId / 1000);
        return $basePath . '/' . $fileName;
    }
    
    /**
     * 获取FTP URL
     */
    private function getFtpUrl($albumId, $fileName) {
        $cdnUrl = rtrim($this->ftpConfig['cdn_url'], '/');
        $path = '/albums/' . intval($albumId / 1000) . '/' . $fileName;
        return $cdnUrl . $path;
    }
    
    /**
     * 获取配置
     */
    private function getConfig($key, $default = null) {
        $config = $this->db->fetch(
            "SELECT value FROM {$this->db->getPrefix()}system_config WHERE `key` = :key",
            ['key' => $key]
        );
        
        return $config ? $config['value'] : $default;
    }
    
    /**
     * 上传头像
     */
    public function uploadAvatar($file, $userId) {
        $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/avatars/';
        $this->createDirectory($uploadDir);
        
        // 生成文件名
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = 'avatar_' . $userId . '_' . time() . '.' . $extension;
        $localPath = $uploadDir . $filename;
        
        // 移动上传文件
        if (!move_uploaded_file($file['tmp_name'], $localPath)) {
            return false;
        }
        
        // 调整图片大小
        $this->resizeImage($localPath, $localPath, 200, 200);
        
        // 转换为WebP
        $webpPath = $this->convertToWebP($localPath);
        if ($webpPath) {
            unlink($localPath);
            $localPath = $webpPath;
            $filename = str_replace('.' . $extension, '.webp', $filename);
        }
        
        // 上传到FTP（如果配置了）
        if ($this->connectFtp()) {
            $remotePath = '/uploads/avatars/' . $filename;
            if (ftp_put($this->ftpConnection, $remotePath, $localPath, FTP_BINARY)) {
                unlink($localPath);
                $this->closeFtp();
                return ($this->ftpConfig['ftp_base_url'] ?? '') . $remotePath;
            }
            $this->closeFtp();
        }
        
        return '/uploads/avatars/' . $filename;
    }
    
    /**
     * 调整图片大小
     */
    private function resizeImage($source, $destination, $width, $height) {
        $info = getimagesize($source);
        if (!$info) return false;
        
        $mime = $info['mime'];
        
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($source);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($source);
                break;
            default:
                return false;
        }
        
        $originalWidth = imagesx($image);
        $originalHeight = imagesy($image);
        
        // 计算缩放比例（保持正方形，裁切中心部分）
        $scale = max($width / $originalWidth, $height / $originalHeight);
        $newWidth = $originalWidth * $scale;
        $newHeight = $originalHeight * $scale;
        
        // 计算裁切位置
        $cropX = ($newWidth - $width) / 2;
        $cropY = ($newHeight - $height) / 2;
        
        // 创建新图像
        $newImage = imagecreatetruecolor($width, $height);
        
        // 保持透明度
        if ($mime === 'image/png' || $mime === 'image/gif') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefill($newImage, 0, 0, $transparent);
        }
        
        // 复制并调整大小
        $resized = imagecreatetruecolor($newWidth, $newHeight);
        imagecopyresampled($resized, $image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // 裁切到指定大小
        imagecopy($newImage, $resized, 0, 0, $cropX, $cropY, $width, $height);
        
        // 保存图像
        switch ($mime) {
            case 'image/jpeg':
                imagejpeg($newImage, $destination, 90);
                break;
            case 'image/png':
                imagepng($newImage, $destination);
                break;
            case 'image/gif':
                imagegif($newImage, $destination);
                break;
            case 'image/webp':
                imagewebp($newImage, $destination, 90);
                break;
        }
        
        imagedestroy($image);
        imagedestroy($resized);
        imagedestroy($newImage);
        
        return true;
    }
}
