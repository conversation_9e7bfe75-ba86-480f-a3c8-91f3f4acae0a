<?php
/**
 * Redis缓存类
 */

class Cache {
    private static $instance = null;
    private $redis;
    private $config;
    
    private function __construct() {
        $config = include __DIR__ . '/../config/database.php';
        
        // 确保config是数组
        if (!is_array($config)) {
            $config = [
                'redis' => [
                    'host' => '127.0.0.1',
                    'port' => 6379,
                    'password' => '000)))00009000',
                    'database' => 0
                ]
            ];
        }
        
        $this->config = $config;
        
        // 设置Redis默认配置（备用）
        if (!isset($this->config['redis']) || !is_array($this->config['redis'])) {
            $this->config['redis'] = [
                'host' => '127.0.0.1',
                'port' => 6379,
                'password' => '000)))00009000',
                'database' => 0
            ];
        }
        
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        $redis = $this->config['redis'];
        
        // 验证Redis配置
        if (!isset($redis['host']) || !isset($redis['port'])) {
            error_log('Redis configuration is incomplete');
            return;
        }
        
        try {
            $this->redis = new Redis();
            
            // 连接Redis服务器
            $connected = $this->redis->connect($redis['host'], $redis['port'], 3); // 3秒超时
            
            if (!$connected) {
                error_log('Failed to connect to Redis server');
                return;
            }
            
            // 认证
            if (!empty($redis['password'])) {
                $auth = $this->redis->auth($redis['password']);
                if (!$auth) {
                    error_log('Redis authentication failed');
                    return;
                }
            }
            
            // 选择数据库
            $this->redis->select($redis['database'] ?? 0);
            
        } catch (Exception $e) {
            error_log('Redis连接失败: ' . $e->getMessage());
            $this->redis = null;
        }
    }
    
    /**
     * 设置缓存
     */
    public function set($key, $value, $expire = 3600) {
        if (!$this->redis) return false;
        
        $prefix = isset($this->config['redis']['prefix']) ? $this->config['redis']['prefix'] : 'liapian:';
        $key = $prefix . $key;
        
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        
        if ($expire > 0) {
            return $this->redis->setex($key, $expire, $value);
        } else {
            return $this->redis->set($key, $value);
        }
    }
    
    /**
     * 获取缓存
     */
    public function get($key) {
        if (!$this->redis) return false;
        
        $prefix = isset($this->config['redis']['prefix']) ? $this->config['redis']['prefix'] : 'liapian:';
        $key = $prefix . $key;
        $value = $this->redis->get($key);
        
        if ($value === false) {
            return false;
        }
        
        // 尝试解析JSON
        $decoded = json_decode($value, true);
        return json_last_error() === JSON_ERROR_NONE ? $decoded : $value;
    }
    
    /**
     * 删除缓存
     */
    public function delete($key) {
        if (!$this->redis) return false;
        
        $prefix = isset($this->config['redis']['prefix']) ? $this->config['redis']['prefix'] : 'liapian:';
        $key = $prefix . $key;
        return $this->redis->del($key);
    }
    
    /**
     * 批量删除缓存
     */
    public function deletePattern($pattern) {
        if (!$this->redis) return false;
        
        $prefix = isset($this->config['redis']['prefix']) ? $this->config['redis']['prefix'] : 'liapian:';
        $pattern = $prefix . $pattern;
        $keys = $this->redis->keys($pattern);
        
        if (empty($keys)) {
            return true;
        }
        
        return $this->redis->del($keys);
    }
    
    /**
     * 检查缓存是否存在
     */
    public function exists($key) {
        if (!$this->redis) return false;
        
        $key = $this->config['redis']['prefix'] . $key;
        return $this->redis->exists($key);
    }
    
    /**
     * 自增
     */
    public function incr($key, $step = 1) {
        if (!$this->redis) return false;
        
        $key = $this->config['redis']['prefix'] . $key;
        return $this->redis->incrBy($key, $step);
    }
    
    /**
     * 自减
     */
    public function decr($key, $step = 1) {
        if (!$this->redis) return false;
        
        $key = $this->config['redis']['prefix'] . $key;
        return $this->redis->decrBy($key, $step);
    }
    
    /**
     * 设置过期时间
     */
    public function expire($key, $seconds) {
        if (!$this->redis) return false;
        
        $key = $this->config['redis']['prefix'] . $key;
        return $this->redis->expire($key, $seconds);
    }
    
    /**
     * 获取TTL
     */
    public function ttl($key) {
        if (!$this->redis) return false;
        
        $key = $this->config['redis']['prefix'] . $key;
        return $this->redis->ttl($key);
    }
}
