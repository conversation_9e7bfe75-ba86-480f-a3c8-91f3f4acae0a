# 丽片网 Nginx 伪静态规则
# 请将以下规则添加到您的 Nginx 站点配置文件中

server {
    listen 443 ssl http2;
    server_name www.liapian.com;
    root /www/wwwroot/www.liapian.com;
    index index.php index.html;
    
    # SSL配置（请根据实际情况配置）
    # ssl_certificate /path/to/your/certificate.crt;
    # ssl_certificate_key /path/to/your/private.key;
    
    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 禁止访问敏感文件和目录
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(json|lock|sql)$ {
        deny all;
    }
    
    location ~ ^/(config|core|models|database)/ {
        deny all;
    }
    
    # 静态资源直接访问
    location ~* \.(css|js|png|jpg|jpeg|gif|webp|ico|svg|woff|woff2|ttf|eot|mp4|avi|mov)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Admin API接口路由 - 特殊处理流式输出
    location ~ ^/admin/api/.*\.php$ {
        try_files $uri =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;

        # 禁用缓冲，支持流式输出
        fastcgi_buffering off;
        fastcgi_cache off;

        # 增加超时时间
        fastcgi_read_timeout 900s;
        fastcgi_send_timeout 900s;

        # 禁用gzip压缩（流式输出不需要）
        gzip off;

        # 添加流式输出头部
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 普通API接口路由
    location ~ ^/api/.*\.php$ {
        try_files $uri =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # API目录的其他文件
    location ^~ /api/ {
        try_files $uri $uri/ =404;
    }
    
    # 火车头发布接口
    location = /train_publish {
        rewrite ^(.*)$ /api/train_publish.php last;
    }
    
    location = /train_publish/ {
        rewrite ^(.*)$ /api/train_publish.php last;
    }
    
    # 安装页面
    location = /install {
        rewrite ^(.*)$ /install.php last;
    }
    
    location = /install/ {
        rewrite ^(.*)$ /install.php last;
    }
    
    # 管理后台路由
    location ^~ /admin {
        rewrite ^/admin/?(.*)$ /admin/index.php last;
    }
    
    # 用户相关路由
    location = /login {
        rewrite ^(.*)$ /pages/login.php last;
    }
    
    location = /login/ {
        rewrite ^(.*)$ /pages/login.php last;
    }
    
    location = /register {
        rewrite ^(.*)$ /pages/register.php last;
    }
    
    location = /register/ {
        rewrite ^(.*)$ /pages/register.php last;
    }
    
    location = /logout {
        rewrite ^(.*)$ /pages/logout.php last;
    }
    
    location = /logout/ {
        rewrite ^(.*)$ /pages/logout.php last;
    }
    
    location = /profile {
        rewrite ^(.*)$ /pages/profile.php last;
    }
    
    location = /profile/ {
        rewrite ^(.*)$ /pages/profile.php last;
    }
    
    location ~ ^/user/(.+)$ {
        rewrite ^/user/(.+)$ /pages/user/$1.php last;
    }
    
    # 套图路由
    location = /albums {
        rewrite ^(.*)$ /pages/albums.php last;
    }
    
    location = /albums/ {
        rewrite ^(.*)$ /pages/albums.php last;
    }
    
    location ~ ^/albums/category/([^/]+)/?$ {
        rewrite ^/albums/category/([^/]+)/?$ /pages/albums.php?category=$1 last;
    }
    
    location ~ ^/album/([^/]+)/?$ {
        rewrite ^/album/([^/]+)/?$ /pages/album.php?slug=$1 last;
    }
    
    # 视频路由
    location = /videos {
        rewrite ^(.*)$ /pages/videos.php last;
    }
    
    location = /videos/ {
        rewrite ^(.*)$ /pages/videos.php last;
    }
    
    location ~ ^/videos/category/([^/]+)/?$ {
        rewrite ^/videos/category/([^/]+)/?$ /pages/videos.php?category=$1 last;
    }
    
    location ~ ^/video/([^/]+)/?$ {
        rewrite ^/video/([^/]+)/?$ /pages/video.php?slug=$1 last;
    }
    
    # 其他页面路由
    location = /ranking {
        rewrite ^(.*)$ /pages/ranking.php last;
    }
    
    location = /ranking/ {
        rewrite ^(.*)$ /pages/ranking.php last;
    }
    
    location = /latest {
        rewrite ^(.*)$ /pages/latest.php last;
    }
    
    location = /latest/ {
        rewrite ^(.*)$ /pages/latest.php last;
    }
    
    location = /search {
        rewrite ^(.*)$ /pages/search.php last;
    }
    
    location = /search/ {
        rewrite ^(.*)$ /pages/search.php last;
    }
    
    # VIP相关页面
    location = /vip {
        rewrite ^(.*)$ /pages/vip.php last;
    }
    
    location = /vip/ {
        rewrite ^(.*)$ /pages/vip.php last;
    }
    
    location = /recharge {
        rewrite ^(.*)$ /pages/recharge.php last;
    }
    
    location = /recharge/ {
        rewrite ^(.*)$ /pages/recharge.php last;
    }

    location = /support {
        rewrite ^(.*)$ /pages/support.php last;
    }

    location = /support/ {
        rewrite ^(.*)$ /pages/support.php last;
    }
    
    # 静态页面路由
    location = /about {
        rewrite ^(.*)$ /pages/about.php last;
    }
    
    location = /about/ {
        rewrite ^(.*)$ /pages/about.php last;
    }
    
    location = /contact {
        rewrite ^(.*)$ /pages/contact.php last;
    }
    
    location = /contact/ {
        rewrite ^(.*)$ /pages/contact.php last;
    }
    
    location = /privacy {
        rewrite ^(.*)$ /pages/privacy.php last;
    }
    
    location = /privacy/ {
        rewrite ^(.*)$ /pages/privacy.php last;
    }
    
    location = /terms {
        rewrite ^(.*)$ /pages/terms.php last;
    }
    
    location = /terms/ {
        rewrite ^(.*)$ /pages/terms.php last;
    }
    
    # PHP文件处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_pass unix:/tmp/php-cgi-83.sock;  # 请根据实际PHP版本调整
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # 首页处理
    location = / {
        try_files $uri /index.php;
    }
    
    # 404处理
    location / {
        try_files $uri $uri/ @fallback;
    }
    
    location @fallback {
        rewrite ^(.*)$ /pages/404.php last;
    }
    
    # 压缩设置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name www.liapian.com;
    return 301 https://$server_name$request_uri;
}
