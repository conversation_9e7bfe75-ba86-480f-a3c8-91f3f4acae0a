<?php
session_start();

require_once __DIR__ . '/includes/functions.php';

// 设置页面信息
$pageTitle = '全页面测试 - 丽片网';
$pageDescription = '测试所有页面是否正常显示';
$pageKeywords = '测试,页面,功能';

// 页面内容
ob_start();
?>

<div class="container">
    <div class="row">
        <div class="col-12">
            <h1 class="text-warning mb-4">
                <i class="fas fa-check-circle me-2"></i>全页面功能测试
            </h1>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-thumbs-up me-2"></i>页面显示正常</h5>
                <p class="mb-0">如果您能立即看到这个页面，说明模板系统工作正常！</p>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-link me-2"></i>主要页面测试</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <a href="/" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-home me-2"></i>首页</span>
                                    <span class="badge bg-success">正常</span>
                                </a>
                                <a href="/login" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-sign-in-alt me-2"></i>登录页面</span>
                                    <span class="badge bg-warning text-dark" id="login-status">测试中</span>
                                </a>
                                <a href="/register" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-user-plus me-2"></i>注册页面</span>
                                    <span class="badge bg-warning text-dark" id="register-status">测试中</span>
                                </a>
                                <a href="/albums" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-images me-2"></i>套图列表</span>
                                    <span class="badge bg-warning text-dark" id="albums-status">测试中</span>
                                </a>
                                <a href="/videos" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-video me-2"></i>视频列表</span>
                                    <span class="badge bg-warning text-dark" id="videos-status">测试中</span>
                                </a>
                                <a href="/ranking" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-trophy me-2"></i>排行榜</span>
                                    <span class="badge bg-warning text-dark" id="ranking-status">测试中</span>
                                </a>
                                <a href="/latest" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-clock me-2"></i>最近更新</span>
                                    <span class="badge bg-warning text-dark" id="latest-status">测试中</span>
                                </a>
                                <a href="/search" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-search me-2"></i>搜索页面</span>
                                    <span class="badge bg-warning text-dark" id="search-status">测试中</span>
                                </a>
                                <a href="/vip" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-crown me-2"></i>VIP页面</span>
                                    <span class="badge bg-warning text-dark" id="vip-status">测试中</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>系统状态</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-dark table-striped">
                                <tr>
                                    <td>模板系统</td>
                                    <td><span class="badge bg-success">正常</span></td>
                                </tr>
                                <tr>
                                    <td>公共函数</td>
                                    <td><span class="badge bg-success">已加载</span></td>
                                </tr>
                                <tr>
                                    <td>数据库连接</td>
                                    <td>
                                        <?php
                                        try {
                                            $db = Database::getInstance();
                                            echo '<span class="badge bg-success">正常</span>';
                                        } catch (Exception $e) {
                                            echo '<span class="badge bg-danger">失败</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>分类数据</td>
                                    <td>
                                        <?php
                                        try {
                                            $categories = getCategories();
                                            echo '<span class="badge bg-success">' . count($categories) . ' 个</span>';
                                        } catch (Exception $e) {
                                            echo '<span class="badge bg-danger">加载失败</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td>用户会话</td>
                                    <td>
                                        <?php if (isset($_SESSION['user_id'])): ?>
                                        <span class="badge bg-info">已登录</span>
                                        <?php else: ?>
                                        <span class="badge bg-secondary">未登录</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-warning" onclick="testAllPages()">
                                    <i class="fas fa-play me-1"></i>自动测试所有页面
                                </button>
                                <button class="btn btn-outline-warning" onclick="clearCache()">
                                    <i class="fas fa-trash me-1"></i>清理浏览器缓存
                                </button>
                                <button class="btn btn-outline-info" onclick="checkConsole()">
                                    <i class="fas fa-bug me-1"></i>检查控制台错误
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>测试说明</h6>
                <ul class="mb-0">
                    <li>点击上方链接测试各个页面是否正常显示</li>
                    <li>如果页面立即显示内容，说明黑屏问题已解决</li>
                    <li>如果仍然黑屏，请按F12查看控制台错误</li>
                    <li>建议在不同浏览器中测试以确保兼容性</li>
                </ul>
            </div>
            
            <div class="text-center mt-4">
                <a href="/" class="btn btn-warning btn-lg">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                <button onclick="window.location.reload()" class="btn btn-outline-warning btn-lg ms-2">
                    <i class="fas fa-refresh me-2"></i>刷新测试
                </button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
document.addEventListener('DOMContentLoaded', function() {
    console.log('全页面测试页加载完成');
    
    // 显示JavaScript正常工作的提示
    setTimeout(function() {
        const alert = document.createElement('div');
        alert.className = 'alert alert-success alert-dismissible fade show';
        alert.innerHTML = '<i class=\"fas fa-check me-2\"></i>JavaScript正常工作！模板系统运行正常。<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>';
        document.querySelector('.container').insertBefore(alert, document.querySelector('.container').firstChild);
    }, 1000);
});

function testAllPages() {
    const pages = ['/login', '/register', '/albums', '/videos', '/ranking', '/latest', '/search', '/vip'];
    let completed = 0;
    
    pages.forEach((page, index) => {
        setTimeout(() => {
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = page;
            
            iframe.onload = function() {
                const statusId = page.substring(1) + '-status';
                const statusEl = document.getElementById(statusId);
                if (statusEl) {
                    statusEl.className = 'badge bg-success';
                    statusEl.textContent = '正常';
                }
                document.body.removeChild(iframe);
                completed++;
                
                if (completed === pages.length) {
                    alert('所有页面测试完成！');
                }
            };
            
            iframe.onerror = function() {
                const statusId = page.substring(1) + '-status';
                const statusEl = document.getElementById(statusId);
                if (statusEl) {
                    statusEl.className = 'badge bg-danger';
                    statusEl.textContent = '错误';
                }
                document.body.removeChild(iframe);
                completed++;
            };
            
            document.body.appendChild(iframe);
        }, index * 500);
    });
}

function clearCache() {
    if (confirm('确定要清理浏览器缓存吗？这将刷新页面。')) {
        window.location.reload(true);
    }
}

function checkConsole() {
    alert('请按F12打开开发者工具，查看Console标签页中是否有错误信息。');
}
";

// 包含布局模板
include __DIR__ . '/templates/layout.php';
?>
