#!/bin/bash

# 丽片网 - 快速部署脚本
# 使用说明：bash deploy.sh

echo "=========================================="
echo "        丽片网 - 快速部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否为root用户
# if [[ $EUID -eq 0 ]]; then
#    echo -e "${RED}请不要以root用户身份运行此脚本${NC}"
#    exit 1
# fi

# 网站目录
SITE_DIR="/www/wwwroot/www.liapian.com"

echo -e "${BLUE}正在检查环境...${NC}"

# 检查目录是否存在
if [ ! -d "$SITE_DIR" ]; then
    echo -e "${RED}错误: 网站目录不存在: $SITE_DIR${NC}"
    exit 1
fi

cd "$SITE_DIR"

echo -e "${BLUE}1. 设置目录权限...${NC}"

# 设置基础权限
chmod -R 755 .
chmod -R 777 uploads/ 2>/dev/null || mkdir -p uploads && chmod -R 777 uploads/
chmod -R 777 cache/ 2>/dev/null || mkdir -p cache && chmod -R 777 cache/

# 创建必要的目录
mkdir -p uploads/albums
mkdir -p uploads/videos
mkdir -p uploads/temp
mkdir -p cache/templates
mkdir -p cache/data

echo -e "${GREEN}✓ 目录权限设置完成${NC}"

echo -e "${BLUE}2. 检查PHP扩展...${NC}"

# 检查必要的PHP扩展
PHP_EXTENSIONS=("mysqli" "pdo" "pdo_mysql" "redis" "gd" "curl" "mbstring" "json")

for ext in "${PHP_EXTENSIONS[@]}"; do
    if php -m | grep -q "^$ext$"; then
        echo -e "${GREEN}✓ $ext 扩展已安装${NC}"
    else
        echo -e "${RED}✗ $ext 扩展未安装${NC}"
        echo -e "${YELLOW}请安装 $ext 扩展${NC}"
    fi
done

echo -e "${BLUE}3. 检查数据库连接...${NC}"

# 读取数据库配置
DB_HOST="localhost"
DB_USER="www_liapian_com"
DB_PASS="LF4NFSiMy36eWim3"
DB_NAME="www_liapian_com"

# 测试数据库连接
mysql -h"$DB_HOST" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SELECT 1;" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ 数据库连接正常${NC}"
else
    echo -e "${RED}✗ 数据库连接失败${NC}"
    echo -e "${YELLOW}请检查数据库配置或运行安装脚本${NC}"
fi

echo -e "${BLUE}4. 检查Redis连接...${NC}"

# 测试Redis连接
redis-cli ping 2>/dev/null | grep -q "PONG"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Redis连接正常${NC}"
else
    echo -e "${YELLOW}⚠ Redis连接失败，将使用文件缓存${NC}"
fi

echo -e "${BLUE}5. 生成必要的配置文件...${NC}"

# 生成.htaccess文件的备份
if [ -f ".htaccess" ]; then
    cp .htaccess .htaccess.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}✓ .htaccess 备份完成${NC}"
fi

# 创建上传目录的.htaccess
cat > uploads/.htaccess << EOF
# 禁止直接访问PHP文件
<Files "*.php">
    Order Allow,Deny
    Deny from all
</Files>

# 允许图片和视频文件
<FilesMatch "\.(jpg|jpeg|png|gif|webp|mp4|avi|mov)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>
EOF

echo -e "${GREEN}✓ 上传目录安全配置完成${NC}"

echo -e "${BLUE}6. 检查Nginx配置...${NC}"

# 检查Nginx配置文件
NGINX_CONF="/www/server/panel/vhost/nginx/www.liapian.com.conf"

if [ -f "$NGINX_CONF" ]; then
    echo -e "${GREEN}✓ 找到Nginx配置文件${NC}"
    
    # 检查是否包含URL重写规则
    if grep -q "try_files" "$NGINX_CONF"; then
        echo -e "${GREEN}✓ URL重写规则已配置${NC}"
    else
        echo -e "${YELLOW}⚠ 可能需要配置URL重写规则${NC}"
        echo -e "${BLUE}请在Nginx配置中添加以下规则：${NC}"
        echo "location / {"
        echo "    try_files \$uri \$uri/ @rewrite;"
        echo "}"
        echo "location @rewrite {"
        echo "    rewrite ^/(.*)$ /index.php;\$"
        echo "}"
    fi
else
    echo -e "${YELLOW}⚠ 未找到Nginx配置文件${NC}"
fi

echo -e "${BLUE}7. 检查SSL证书...${NC}"

# 检查SSL证书
if curl -s -I https://www.liapian.com | head -n 1 | grep -q "200\|301\|302"; then
    echo -e "${GREEN}✓ HTTPS访问正常${NC}"
else
    echo -e "${YELLOW}⚠ HTTPS访问异常，请检查SSL证书${NC}"
fi

echo -e "${BLUE}8. 创建定时任务...${NC}"

# 创建定时任务脚本
cat > cron_tasks.sh << 'EOF'
#!/bin/bash
# 丽片网定时任务

SITE_DIR="/www/wwwroot/www.liapian.com"
cd "$SITE_DIR"

# 清理过期缓存
find cache/ -name "*.cache" -mtime +7 -delete 2>/dev/null

# 清理临时文件
find uploads/temp/ -mtime +1 -delete 2>/dev/null

# 备份数据库（每天凌晨2点）
if [ "$(date +%H)" == "02" ]; then
    mysqldump -u www_liapian_com -pLF4NFSiMy36eWim3 www_liapian_com > backups/db_$(date +%Y%m%d).sql
    
    # 保留最近7天的备份
    find backups/ -name "db_*.sql" -mtime +7 -delete 2>/dev/null
fi
EOF

chmod +x cron_tasks.sh

# 创建备份目录
mkdir -p backups

echo -e "${GREEN}✓ 定时任务脚本创建完成${NC}"
echo -e "${YELLOW}请手动添加到crontab：${NC}"
echo "*/30 * * * * /bin/bash $SITE_DIR/cron_tasks.sh"

echo -e "${BLUE}9. 性能优化建议...${NC}"

echo -e "${YELLOW}建议进行以下优化：${NC}"
echo "1. 启用OPcache PHP扩展"
echo "2. 配置CDN加速静态资源"
echo "3. 启用Gzip压缩"
echo "4. 设置浏览器缓存"
echo "5. 定期清理日志文件"

echo ""
echo "=========================================="
echo -e "${GREEN}        部署检查完成！${NC}"
echo "=========================================="
echo ""
echo -e "${BLUE}下一步操作：${NC}"
echo "1. 访问 https://www.liapian.com/install 安装数据库"
echo "2. 访问 https://www.liapian.com 查看网站首页"
echo "3. 访问 https://www.liapian.com/admin 进入管理后台"
echo "4. 默认管理员账号：admin / 123456"
echo ""
echo -e "${RED}重要提醒：${NC}"
echo "1. 请及时修改默认管理员密码"
echo "2. 配置FTP服务器信息"
echo "3. 设置支付接口"
echo "4. 上传网站LOGO和图标"
echo ""
echo -e "${GREEN}祝您使用愉快！${NC}"
