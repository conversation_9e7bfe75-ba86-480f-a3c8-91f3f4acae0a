<?php
/**
 * 数据库安装脚本
 * 访问 /install.php 来安装数据库
 */

// 检查是否已经安装
if (file_exists(__DIR__ . '/install.lock')) {
    die('系统已经安装完成，如需重新安装请删除 install.lock 文件');
}

$error = '';
$success = '';

if ($_POST['action'] === 'install') {
    try {
        require_once __DIR__ . '/config/database.php';
        $config = require __DIR__ . '/config/database.php';
        
        $mysql = $config['mysql'];
        $dsn = "mysql:host={$mysql['host']};port={$mysql['port']};charset={$mysql['charset']}";
        
        // 连接数据库
        $pdo = new PDO($dsn, $mysql['username'], $mysql['password']);
        
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$mysql['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$mysql['database']}`");
        
        // 读取并执行SQL文件
        $sql = file_get_contents(__DIR__ . '/database/install.sql');
        
        // 分割SQL语句
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(--|#)/', $statement)) {
                $pdo->exec($statement);
            }
        }
        
        // 创建安装锁定文件
        file_put_contents(__DIR__ . '/install.lock', date('Y-m-d H:i:s'));
        
        $success = '数据库安装成功！您现在可以访问网站了。';
        
        // 显示默认管理员账号信息
        $adminInfo = '
            <div class="alert alert-info mt-3">
                <h6>默认管理员账号信息：</h6>
                <p>用户名：admin<br>密码：123456</p>
                <p>请及时登录后台修改密码！</p>
                <p><a href="/admin" class="btn btn-primary btn-sm">进入管理后台</a></p>
            </div>
        ';
        
    } catch (Exception $e) {
        $error = '安装失败：' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>丽片网 - 系统安装</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: #e9ecef;
            min-height: 100vh;
        }
        .install-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 50px 20px;
        }
        .card {
            background-color: #212529;
            border: 1px solid #495057;
        }
        .card-header {
            background-color: #343a40;
            border-bottom: 1px solid #495057;
        }
        .form-control {
            background-color: #212529;
            border-color: #495057;
            color: #e9ecef;
        }
        .form-control:focus {
            background-color: #212529;
            border-color: #ffc107;
            color: #e9ecef;
            box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #000;
        }
        .text-warning {
            color: #ffc107 !important;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="text-center mb-4">
            <h1 class="text-warning">丽片网</h1>
            <p class="text-muted">系统安装向导</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 text-warning">数据库安装</h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                <?php echo $adminInfo ?? ''; ?>
                <div class="text-center mt-3">
                    <a href="/" class="btn btn-warning">访问首页</a>
                </div>
                <?php else: ?>
                
                <div class="alert alert-info">
                    <h6>安装说明：</h6>
                    <ul class="mb-0">
                        <li>确保您已经创建了MySQL数据库</li>
                        <li>确保数据库配置信息正确</li>
                        <li>确保PHP有写入文件的权限</li>
                        <li>安装完成后请及时修改默认管理员密码</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-warning">当前数据库配置：</h6>
                    <?php
                    $config = require __DIR__ . '/config/database.php';
                    $mysql = $config['mysql'];
                    ?>
                    <ul class="list-unstyled">
                        <li><strong>主机：</strong><?php echo htmlspecialchars($mysql['host']); ?></li>
                        <li><strong>端口：</strong><?php echo htmlspecialchars($mysql['port']); ?></li>
                        <li><strong>数据库：</strong><?php echo htmlspecialchars($mysql['database']); ?></li>
                        <li><strong>用户名：</strong><?php echo htmlspecialchars($mysql['username']); ?></li>
                        <li><strong>表前缀：</strong><?php echo htmlspecialchars($mysql['prefix']); ?></li>
                    </ul>
                </div>
                
                <form method="post">
                    <input type="hidden" name="action" value="install">
                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning btn-lg">
                            开始安装数据库
                        </button>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        如需修改数据库配置，请编辑 config/database.php 文件
                    </small>
                </div>
                
                <?php endif; ?>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <small class="text-muted">
                &copy; 2025 丽片网. 专业的美女套图视频网站
            </small>
        </div>
    </div>
    
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>
