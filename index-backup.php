<?php
session_start();

// 引入必要的文件
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/models/Album.php';
require_once __DIR__ . '/models/Video.php';

require_once __DIR__ . '/models/Album.php';
require_once __DIR__ . '/models/Video.php';
require_once __DIR__ . '/models/User.php';
require_once __DIR__ . '/core/Database.php';
require_once __DIR__ . '/core/Cache.php';

/**
 * 获取分类列表
 */
function getCategories($type = 'album') {
    $db = Database::getInstance();
    // 暂时返回所有分类，后续可以根据需要添加type字段过滤
    return $db->fetchAll(
        "SELECT * FROM {$db->getPrefix()}categories WHERE status = 1 ORDER BY sort_order ASC"
    );
}

/**
 * 获取系统配置
 */
function getConfig($key, $default = '') {
    $db = Database::getInstance();
    $cache = Cache::getInstance();
    
    $cacheKey = 'config:' . $key;
    $value = $cache->get($cacheKey);
    
    if ($value === false) {
        $config = $db->fetch(
            "SELECT config_value FROM {$db->getPrefix()}system_config WHERE config_key = :key",
            ['key' => $key]
        );
        $value = $config ? $config['config_value'] : $default;
        $cache->set($cacheKey, $value, 3600);
    }
    
    return $value;
}

// 初始化数据
try {
    $albumModel = new Album();
    $videoModel = new Video();

    // 获取最新套图
    $latestAlbums = $albumModel->getList([
        'page' => 1,
        'page_size' => 12,
        'order' => 'latest'
    ]);

    // 获取热门套图
    $hotAlbums = $albumModel->getList([
        'page' => 1,
        'page_size' => 8,
        'order' => 'view'
    ]);

    // 获取最新视频
    $latestVideos = $videoModel->getList([
        'page' => 1,
        'page_size' => 8,
        'order' => 'latest'
    ]);

    // 获取热门视频
    $hotVideos = $videoModel->getList([
        'page' => 1,
        'page_size' => 6,
        'order' => 'view'
    ]);
} catch (Exception $e) {
    // 如果数据库查询失败，使用空数据
    $latestAlbums = ['list' => [], 'total' => 0];
    $hotAlbums = ['list' => [], 'total' => 0];
    $latestVideos = ['list' => [], 'total' => 0];
    $hotVideos = ['list' => [], 'total' => 0];

    // 记录错误（在生产环境中应该记录到日志文件）
    error_log("首页数据加载失败: " . $e->getMessage());
}

// 处理套图数据
foreach ($latestAlbums['list'] as &$album) {
    $album['is_free'] = !$album['is_paid'];
    $album['cover'] = $album['cover_image'] ?: '/assets/images/placeholder.svg';
    $album['slug'] = $album['slug'] ?: 'album-' . $album['id'];
    $album['image_count'] = $album['total_images'] ?: 0;
}

foreach ($hotAlbums['list'] as &$album) {
    $album['is_free'] = !$album['is_paid'];
    $album['cover'] = $album['cover_image'] ?: '/assets/images/placeholder.svg';
    $album['slug'] = $album['slug'] ?: 'album-' . $album['id'];
    $album['image_count'] = $album['total_images'] ?: 0;
}

// 处理视频数据
foreach ($latestVideos['list'] as &$video) {
    $video['is_free'] = !$video['is_paid'];
    $video['cover'] = $video['cover_image'] ?: '/assets/images/placeholder.svg';
    $video['slug'] = $video['slug'] ?: 'video-' . $video['id'];
    $video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
}

foreach ($hotVideos['list'] as &$video) {
    $video['is_free'] = !$video['is_paid'];
    $video['cover'] = $video['cover_image'] ?: '/assets/images/placeholder.svg';
    $video['slug'] = $video['slug'] ?: 'video-' . $video['id'];
    $video['duration_formatted'] = $video['duration'] ? gmdate("H:i:s", $video['duration']) : '00:00';
}

// 设置页面信息
$pageTitle = getConfig('site_name', '丽片网') . ' - ' . getConfig('site_description', '专业的美女套图视频网站');
$pageDescription = getConfig('site_description', '专业的美女套图视频网站，提供高质量的美女图片和视频内容');
$pageKeywords = getConfig('site_keywords', '美女,套图,视频,写真');

// 页面内容
ob_start();
?>

<!-- 轮播图 -->
<div class="container-fluid px-0 mb-4">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
        <div class="carousel-indicators">
            <?php for ($i = 0; $i < min(5, count($latestAlbums['list'])); $i++): ?>
            <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="<?php echo $i; ?>" 
                    <?php echo $i === 0 ? 'class="active"' : ''; ?>></button>
            <?php endfor; ?>
        </div>
        
        <div class="carousel-inner">
            <?php foreach (array_slice($latestAlbums['list'], 0, 5) as $index => $album): ?>
            <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                <div class="position-relative">
                    <img src="<?php echo htmlspecialchars($album['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                         class="d-block w-100" style="height: 400px; object-fit: cover;"
                         alt="<?php echo htmlspecialchars($album['title']); ?>"
                         onerror="this.src='/assets/images/placeholder.svg'">
                    <div class="carousel-caption d-none d-md-block bg-dark bg-opacity-75 rounded p-3">
                        <h5 class="text-warning"><?php echo htmlspecialchars($album['title']); ?></h5>
                        <p><?php echo htmlspecialchars(mb_substr($album['description'], 0, 100) . '...'); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-<?php echo $album['is_free'] ? 'success' : 'warning'; ?> me-2">
                                    <?php echo $album['is_free'] ? '免费' : 'VIP'; ?>
                                </span>
                                <small class="text-muted">
                                    <i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?>
                                    <i class="fas fa-heart ms-2 me-1"></i><?php echo number_format($album['favorite_count']); ?>
                                    <i class="fas fa-images ms-2 me-1"></i><?php echo $album['image_count']; ?>张
                                </small>
                            </div>
                            <a href="/album/<?php echo $album['slug']; ?>" class="btn btn-warning btn-sm">
                                <i class="fas fa-play me-1"></i>立即观看
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
    </div>
</div>

<div class="container">
    <!-- 会员权益提示 -->
    <?php if (!isset($_SESSION['user_id'])): ?>
    <div class="member-notice mb-4">
        <h5><i class="fas fa-crown me-2"></i>成为会员，享受更多特权</h5>
        <p>注册即可免费浏览10张图片，升级VIP解锁全站内容</p>
        <div class="row text-center">
            <div class="col-md-3 col-6 mb-2">
                <div class="text-warning fw-bold">天费会员</div>
                <small class="text-muted">1天无限浏览</small>
            </div>
            <div class="col-md-3 col-6 mb-2">
                <div class="text-warning fw-bold">月费会员</div>
                <small class="text-muted">30天无限浏览</small>
            </div>
            <div class="col-md-3 col-6 mb-2">
                <div class="text-warning fw-bold">三年会员</div>
                <small class="text-muted">图片+视频+下载</small>
            </div>
            <div class="col-md-3 col-6 mb-2">
                <div class="text-warning fw-bold">永久会员</div>
                <small class="text-muted">永久无限制</small>
            </div>
        </div>
        <div class="mt-3">
            <a href="/register" class="btn btn-warning me-2">
                <i class="fas fa-user-plus me-1"></i>立即注册
            </a>
            <a href="/login" class="btn btn-outline-warning">
                <i class="fas fa-sign-in-alt me-1"></i>会员登录
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- 统计信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stats-number"><?php echo number_format($latestAlbums['total']); ?></div>
                            <small class="text-muted">套图总数</small>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stats-number">10000+</div>
                            <small class="text-muted">注册会员</small>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stats-number">1000+</div>
                            <small class="text-muted">视频资源</small>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="stats-number">每日更新</div>
                            <small class="text-muted">持续更新</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 热门套图 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-fire me-2"></i>热门套图
                </h3>
                <a href="/albums" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            
            <div class="album-grid">
                <?php foreach ($hotAlbums['list'] as $album): ?>
                <div class="album-card">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($album['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                             alt="<?php echo htmlspecialchars($album['title']); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?></span>
                                    <span><i class="fas fa-images me-1"></i><?php echo $album['image_count']; ?>张</span>
                                </div>
                            </div>
                        </div>
                        <?php if (!$album['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <a href="/album/<?php echo $album['slug']; ?>" class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($album['title']); ?>
                        </a>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($album['description'], 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($album['category_name']); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($album['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 最新套图 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-clock me-2"></i>最新套图
                </h3>
                <a href="/latest" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
            
            <div class="album-grid">
                <?php foreach ($latestAlbums['list'] as $album): ?>
                <div class="album-card">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($album['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                             alt="<?php echo htmlspecialchars($album['title']); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-eye me-1"></i><?php echo number_format($album['view_count']); ?></span>
                                    <span><i class="fas fa-images me-1"></i><?php echo $album['image_count']; ?>张</span>
                                </div>
                            </div>
                        </div>
                        <?php if (!$album['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <a href="/album/<?php echo $album['slug']; ?>" class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($album['title']); ?>
                        </a>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($album['description'], 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i><?php echo date('m-d', strtotime($album['published_at'])); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($album['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 热门视频 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-video me-2"></i>热门视频
                </h3>
                <a href="/videos" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>

            <div class="album-grid">
                <?php foreach ($hotVideos['list'] as $video): ?>
                <div class="album-card video-card">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($video['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                             alt="<?php echo htmlspecialchars($video['title']); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?></span>
                                    <span><i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?></span>
                                </div>
                            </div>
                            <div class="play-button">
                                <i class="fas fa-play-circle"></i>
                            </div>
                        </div>
                        <?php if (!$video['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <a href="/video/<?php echo $video['slug']; ?>" class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($video['title']); ?>
                        </a>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($video['description'], 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($video['category_name']); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($video['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 最新视频 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-warning mb-0">
                    <i class="fas fa-film me-2"></i>最新视频
                </h3>
                <a href="/videos?order=latest" class="btn btn-outline-warning btn-sm">
                    查看更多 <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>

            <div class="album-grid">
                <?php foreach ($latestVideos['list'] as $video): ?>
                <div class="album-card video-card">
                    <div class="card-img-container">
                        <img src="<?php echo htmlspecialchars($video['cover'] ?: '/assets/images/placeholder.svg'); ?>"
                             alt="<?php echo htmlspecialchars($video['title']); ?>" class="img-fluid"
                             onerror="this.src='/assets/images/placeholder.svg'">
                        <div class="card-overlay">
                            <div class="card-stats">
                                <div class="d-flex justify-content-between">
                                    <span><i class="fas fa-play me-1"></i><?php echo number_format($video['view_count']); ?></span>
                                    <span><i class="fas fa-clock me-1"></i><?php echo $video['duration_formatted']; ?></span>
                                </div>
                            </div>
                            <div class="play-button">
                                <i class="fas fa-play-circle"></i>
                            </div>
                        </div>
                        <?php if (!$video['is_free']): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-warning text-dark">VIP</span>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <a href="/video/<?php echo $video['slug']; ?>" class="card-title text-decoration-none">
                            <?php echo htmlspecialchars($video['title']); ?>
                        </a>
                        <p class="card-text">
                            <?php echo htmlspecialchars(mb_substr($video['description'], 0, 50) . '...'); ?>
                        </p>
                        <div class="card-meta">
                            <span class="text-muted">
                                <i class="fas fa-calendar me-1"></i><?php echo date('m-d', strtotime($video['created_at'])); ?>
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-heart me-1"></i><?php echo number_format($video['favorite_count']); ?>
                            </span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- 硬编码的友情链接已删除，现在使用footer中的动态友情链接 -->
</div>

<?php
$content = ob_get_clean();

// 页面脚本
$pageScript = "
// 立即显示页面内容，防止黑屏
(function() {
    // 确保页面立即可见
    document.documentElement.style.visibility = 'visible';
    document.documentElement.style.opacity = '1';
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.style.display = 'block';
})();

// DOM加载完成后的处理
document.addEventListener('DOMContentLoaded', function() {
    // 强制显示所有主要容器
    const containers = document.querySelectorAll('.container, .container-fluid, main, .content, .row, .col');
    containers.forEach(el => {
        el.style.display = 'block';
        el.style.visibility = 'visible';
        el.style.opacity = '1';
    });

    // 移除可能的加载遮罩
    const loadingMask = document.querySelector('.loading-mask, .loader, .loading');
    if (loadingMask) {
        loadingMask.remove();
    }

    // 确保所有图片都能正常显示
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        // 如果图片已经加载，移除占位符样式
        if (img.complete && img.naturalHeight !== 0) {
            img.classList.remove('img-placeholder');
            img.classList.add('loaded');
        } else {
            // 监听图片加载完成
            img.addEventListener('load', function() {
                this.classList.remove('img-placeholder');
                this.classList.add('loaded');
            });

            // 监听图片加载失败
            img.addEventListener('error', function() {
                this.classList.remove('img-placeholder');
                this.classList.add('loaded');
                if (!this.src.includes('placeholder.svg')) {
                    this.src = '/assets/images/placeholder.svg';
                }
            });
        }
    });

    // 初始化轮播图
    if (typeof bootstrap !== 'undefined' && document.querySelector('#heroCarousel')) {
        try {
            new bootstrap.Carousel(document.querySelector('#heroCarousel'), {
                interval: 5000,
                wrap: true
            });
        } catch (e) {
            console.log('轮播图初始化失败:', e);
        }
    }

    // 确保页面完全可见
    setTimeout(function() {
        document.body.style.visibility = 'visible';
        document.body.style.opacity = '1';
    }, 100);
});

// 页面加载完成后再次确保可见
window.addEventListener('load', function() {
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.style.display = 'block';
});
";

?>

<!-- 导航栏 -->
<nav class="navbar navbar-expand-lg navbar-dark" style="visibility: visible !important; opacity: 1 !important;">
    <div class="container">
        <a class="navbar-brand fw-bold text-warning" href="/">
            <i class="fas fa-camera-retro me-2"></i>丽片网
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link active" href="/"><i class="fas fa-home me-1"></i>首页</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/albums"><i class="fas fa-images me-1"></i>套图</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/videos"><i class="fas fa-video me-1"></i>视频</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/ranking"><i class="fas fa-trophy me-1"></i>排行榜</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/latest"><i class="fas fa-clock me-1"></i>最新</a>
                </li>
            </ul>

            <form class="d-flex me-3" action="/search" method="GET">
                <input class="form-control me-2" type="search" name="q" placeholder="搜索..." style="width: 200px;">
                <button class="btn btn-outline-warning" type="submit">
                    <i class="fas fa-search"></i>
                </button>
            </form>

            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="/login"><i class="fas fa-sign-in-alt me-1"></i>登录</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-warning" href="/register"><i class="fas fa-user-plus me-1"></i>注册</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-warning" href="/vip"><i class="fas fa-crown me-1"></i>VIP</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- 主要内容 -->
<main class="container-fluid py-4" style="visibility: visible !important; opacity: 1 !important; display: block !important;">
    <div class="container" style="visibility: visible !important; opacity: 1 !important; display: block !important;">

        <!-- 测试提示 -->
        <div class="alert alert-success mb-4" role="alert">
            <h4 class="alert-heading"><i class="fas fa-check-circle me-2"></i>页面正常显示</h4>
            <p>如果您能立即看到这个内容，说明黑屏问题已经解决！</p>
            <hr>
            <p class="mb-0">
                <a href="/simple-test.php" class="btn btn-warning me-2">简单测试页</a>
                <a href="/index-test.php" class="btn btn-outline-warning">完整测试页</a>
            </p>
        </div>

        <!-- 轮播图 -->
        <div class="row mb-4">
            <div class="col-12">
                <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img src="/assets/images/placeholder.svg" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="轮播图1">
                            <div class="carousel-caption d-none d-md-block">
                                <h5>欢迎来到丽片网</h5>
                                <p>精美套图和视频内容</p>
                            </div>
                        </div>
                        <div class="carousel-item">
                            <img src="/assets/images/placeholder.svg" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="轮播图2">
                            <div class="carousel-caption d-none d-md-block">
                                <h5>高质量内容</h5>
                                <p>专业的美女套图视频网站</p>
                            </div>
                        </div>
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 最新套图 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="text-warning mb-0">
                        <i class="fas fa-images me-2"></i>最新套图
                    </h3>
                    <a href="/albums" class="btn btn-outline-warning btn-sm">
                        查看更多 <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <img src="/assets/images/placeholder.svg" class="card-img-top" alt="套图1">
                            <div class="card-body">
                                <h6 class="card-title">测试套图 1</h6>
                                <p class="card-text small text-muted">精美套图内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <img src="/assets/images/placeholder.svg" class="card-img-top" alt="套图2">
                            <div class="card-body">
                                <h6 class="card-title">测试套图 2</h6>
                                <p class="card-text small text-muted">精美套图内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <img src="/assets/images/placeholder.svg" class="card-img-top" alt="套图3">
                            <div class="card-body">
                                <h6 class="card-title">测试套图 3</h6>
                                <p class="card-text small text-muted">精美套图内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <img src="/assets/images/placeholder.svg" class="card-img-top" alt="套图4">
                            <div class="card-body">
                                <h6 class="card-title">测试套图 4</h6>
                                <p class="card-text small text-muted">精美套图内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新视频 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="text-warning mb-0">
                        <i class="fas fa-video me-2"></i>最新视频
                    </h3>
                    <a href="/videos" class="btn btn-outline-warning btn-sm">
                        查看更多 <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>

                <div class="row">
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <div class="position-relative">
                                <img src="/assets/images/placeholder.svg" class="card-img-top" alt="视频1">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <i class="fas fa-play-circle fa-3x text-warning"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">测试视频 1</h6>
                                <p class="card-text small text-muted">精彩视频内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <div class="position-relative">
                                <img src="/assets/images/placeholder.svg" class="card-img-top" alt="视频2">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <i class="fas fa-play-circle fa-3x text-warning"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">测试视频 2</h6>
                                <p class="card-text small text-muted">精彩视频内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <div class="position-relative">
                                <img src="/assets/images/placeholder.svg" class="card-img-top" alt="视频3">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <i class="fas fa-play-circle fa-3x text-warning"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">测试视频 3</h6>
                                <p class="card-text small text-muted">精彩视频内容</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="card">
                            <div class="position-relative">
                                <img src="/assets/images/placeholder.svg" class="card-img-top" alt="视频4">
                                <div class="position-absolute top-50 start-50 translate-middle">
                                    <i class="fas fa-play-circle fa-3x text-warning"></i>
                                </div>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">测试视频 4</h6>
                                <p class="card-text small text-muted">精彩视频内容</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</main>

<!-- 页脚 -->
<footer class="bg-black text-light py-4 mt-5">
    <div class="container text-center">
        <p>&copy; 2025 丽片网. 保留所有权利.</p>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>

<!-- 确保页面立即可见的脚本 -->
<script>
// 立即执行
(function() {
    document.documentElement.style.visibility = 'visible';
    document.documentElement.style.opacity = '1';
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.style.display = 'block';

    // 显示所有容器
    var containers = document.querySelectorAll('.container, .container-fluid, main, .content, .row, .col');
    for (var i = 0; i < containers.length; i++) {
        containers[i].style.visibility = 'visible';
        containers[i].style.opacity = '1';
        containers[i].style.display = 'block';
    }
})();

// DOM加载完成后
document.addEventListener('DOMContentLoaded', function() {
    console.log('首页加载完成');

    // 再次确保页面可见
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.style.display = 'block';

    // 初始化轮播图
    var carousel = document.querySelector('#heroCarousel');
    if (carousel && typeof bootstrap !== 'undefined') {
        new bootstrap.Carousel(carousel, {
            interval: 5000,
            wrap: true
        });
    }
});

// 页面完全加载后
window.addEventListener('load', function() {
    console.log('页面完全加载');
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
});
</script>

</body>
</html>
