<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; }
        .test-info { padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            margin: 10px; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .test-button:hover { background: #0056b3; }
        .status { 
            background: #333; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-family: monospace; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>图片拖拽功能测试页面</h1>
        
        <div class="status">
            <h3>测试说明：</h3>
            <p>1. 点击"打开测试图片"按钮</p>
            <p>2. 图片会在灯箱中以全屏模式打开（可拖拽模式）</p>
            <p>3. 尝试拖拽图片，应该能正常拖拽</p>
            <p>4. 点击图片切换到适应模式（不可拖拽）</p>
            <p>5. 再次点击图片切换回全屏模式</p>
            <p>6. <span class="warning">重点测试：此时拖拽功能应该仍然有效</span></p>
        </div>
        
        <button class="test-button" onclick="openTestImage()">📸 打开测试图片</button>
        <button class="test-button" onclick="runDragTest()">🔧 运行拖拽测试</button>
        <button class="test-button" onclick="debugCurrentState()">🐛 调试当前状态</button>
        <button class="test-button" onclick="quickToggleTest()">⚡ 快速切换测试</button>
        
        <div id="testResults" class="status">
            <p>等待测试...</p>
        </div>
        
        <div class="status">
            <h3>修复内容：</h3>
            <p class="success">✅ 修复了事件处理器的闭包问题</p>
            <p class="success">✅ 改进了事件清理机制</p>
            <p class="success">✅ 确保拖拽偏移量在模式切换时正确保持</p>
            <p class="success">✅ 增强了触摸事件的处理</p>
            <p class="success">✅ 添加了详细的调试日志</p>
        </div>
    </div>

    <!-- 模拟灯箱结构 -->
    <div id="lightbox" style="display: none; position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0,0,0,0.9); z-index: 10000; justify-content: center; align-items: center;">
        <div style="position: relative; width: 100%; height: 100%;">
            <img id="lightboxImage" src="/assets/images/placeholder.php?w=800&h=1200&text=测试图片%20-%20尝试拖拽我" 
                 style="position: absolute; top: 0; left: 0; width: 100vw; height: auto; min-height: 100vh; object-fit: cover; cursor: grab;" />
            <button onclick="closeLightbox()" style="position: absolute; top: 20px; right: 20px; background: rgba(0,0,0,0.7); color: white; border: none; padding: 10px; border-radius: 5px; cursor: pointer; z-index: 10001;">✕ 关闭</button>
            <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(0,0,0,0.7); color: white; padding: 10px 20px; border-radius: 20px; font-size: 14px;">
                拖拽图片查看完整内容 | 点击切换显示模式
            </div>
        </div>
    </div>

    <script>
        // 模拟album.php中的核心变量和函数
        var isFullscreenMode = true;
        var currentImageEventHandlers = {
            mousedown: null,
            mousemove: null,
            mouseup: null,
            touchstart: null,
            touchmove: null,
            touchend: null,
            click: null
        };

        function openTestImage() {
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightboxImage');
            
            // 重置状态
            isFullscreenMode = true;
            lightboxImage.dragOffsetY = 0;
            
            // 应用样式
            applyImageMode();
            
            // 显示灯箱
            lightbox.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            updateTestResults('测试图片已打开，当前为全屏可拖拽模式');
        }

        function closeLightbox() {
            const lightbox = document.getElementById('lightbox');
            lightbox.style.display = 'none';
            document.body.style.overflow = 'auto';
            
            // 清理事件
            cleanupImageEvents(document.getElementById('lightboxImage'));
            
            updateTestResults('灯箱已关闭');
        }

        function runDragTest() {
            updateTestResults('开始拖拽测试...<br/>1. 请尝试拖拽图片<br/>2. 点击图片切换模式<br/>3. 再次点击切换回拖拽模式<br/>4. 验证拖拽是否仍然有效');
        }

        function debugCurrentState() {
            const lightboxImage = document.getElementById('lightboxImage');
            const activeHandlers = Object.keys(currentImageEventHandlers).filter(key => currentImageEventHandlers[key] !== null);
            
            const debugInfo = `
                当前模式: ${isFullscreenMode ? '全屏可拖拽' : '适应模式'}<br/>
                拖拽偏移: ${lightboxImage.dragOffsetY || 0}px<br/>
                激活的事件处理器: ${activeHandlers.join(', ') || '无'}<br/>
                图片变换: ${lightboxImage.style.transform || '无'}<br/>
                光标样式: ${lightboxImage.style.cursor || '默认'}
            `;
            
            updateTestResults(debugInfo);
        }

        function quickToggleTest() {
            toggleImageMode();
            setTimeout(() => {
                toggleImageMode();
                updateTestResults('快速切换测试完成 - 检查拖拽功能是否正常');
            }, 1000);
        }

        function updateTestResults(message) {
            document.getElementById('testResults').innerHTML = '<p>' + message + '</p>';
        }

        // 从album.php复制的核心函数（简化版）
        <?php
        // 从album.php中提取关键的JavaScript函数
        $albumFile = file_get_contents(__DIR__ . '/pages/album.php');
        
        // 提取JavaScript部分
        preg_match('/function toggleImageMode\(\).*?function applyImageMode\(\).*?function cleanupImageEvents.*?function setupImageEvents.*?function setupDragEvents.*?function setupClickEvents.*?(?=window\.openLightbox|$)/s', $albumFile, $matches);
        
        if (isset($matches[0])) {
            // 清理PHP标签和多余的内容
            $jsCode = $matches[0];
            $jsCode = preg_replace('/\$pageScript = "([^"]*)"/', '', $jsCode);
            $jsCode = str_replace(['<?php', '?>', '\\"', "\\'"], ['', '', '"', "'"], $jsCode);
            echo $jsCode;
        }
        ?>
    </script>
</body>
</html>
