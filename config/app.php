<?php
/**
 * 应用配置文件
 */

require_once __DIR__ . '/../core/Config.php';

return [
    'app_name' => Config::getSiteName(),
    'app_url' => 'https://www.liapian.com',
    'timezone' => 'Asia/Shanghai',
    'debug' => false,
    
    // 安全配置
    'session_name' => 'LIAPIAN_SESSION',
    'csrf_token_name' => '_token',
    'password_salt' => 'your_password_salt_here',
    
    // 文件上传配置
    'upload' => [
        'max_size' => 50 * 1024 * 1024, // 50MB
        'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'allowed_video_types' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
        'image_quality' => 85,
        'enable_webp_convert' => true, // 是否启用WebP格式转换
    ],
    
    // FTP配置
    'ftp' => [
        'host' => 'your_ftp_host',
        'port' => 21,
        'username' => 'your_ftp_username',
        'password' => 'your_ftp_password',
        'passive' => true,
        'ssl' => false,
        'timeout' => 30,
        'base_path' => '/uploads/',
    ],
    
    // CDN配置
    'cdn' => [
        'default' => 'cdn1',
        'urls' => [
            'cdn1' => 'https://cdn1.liapian.com',
            'cdn2' => 'https://cdn2.liapian.com',
            'cdn3' => 'https://cdn3.liapian.com',
        ]
    ],
    
    // 用户组权限配置
    'user_groups' => [
        'guest' => [
            'name' => '游客',
            'free_image_count' => 3,
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 1,
        ],
        'member' => [
            'name' => '注册会员',
            'free_image_count' => 10,
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 1,
        ],
        'vip_day' => [
            'name' => '天费会员',
            'free_image_count' => -1, // -1表示无限制
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 10,
            'duration_days' => 1,
        ],
        'vip_week' => [
            'name' => '周费会员',
            'free_image_count' => -1,
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 10,
            'duration_days' => 7,
        ],
        'vip_month' => [
            'name' => '月费会员',
            'free_image_count' => -1,
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 10,
            'duration_days' => 30,
        ],
        'vip_year' => [
            'name' => '年费会员',
            'free_image_count' => -1,
            'can_view_video' => false,
            'can_download' => false,
            'images_per_page' => 10,
            'duration_days' => 365,
        ],
        'vip_3year' => [
            'name' => '三年会员',
            'free_image_count' => -1,
            'can_view_video' => true,
            'can_download' => true,
            'images_per_page' => 20,
            'duration_days' => 1095,
        ],
        'vip_forever' => [
            'name' => '永久会员',
            'free_image_count' => -1,
            'can_view_video' => true,
            'can_download' => true,
            'images_per_page' => -1, // 无限制
            'duration_days' => -1, // 永久
        ],
    ],
    
    // 积分配置
    'points' => [
        'register_reward' => 100,
        'invite_reward' => 50,
        'daily_checkin' => 10,
    ],
    
    // 静态资源配置（使用国内CDN）
    'assets' => [
        'bootstrap_css' => 'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css',
        'bootstrap_js' => 'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js',
        'jquery' => 'https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js',
        'font_awesome' => 'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    ]
];
