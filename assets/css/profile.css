/* 用户中心样式 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 用户信息卡片 */
.card-body img.rounded-circle {
    border: 3px solid var(--bs-warning);
    padding: 2px;
}

.card-body .stats {
    font-size: 0.9rem;
}

/* 导航菜单样式 */
.list-group-item {
    border: none;
    border-radius: 0;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: var(--bs-warning);
    color: var(--bs-dark);
}

.list-group-item.active {
    background-color: var(--bs-warning);
    color: var(--bs-dark);
    border-color: var(--bs-warning);
}

.list-group-item i {
    width: 20px;
    text-align: center;
}

/* 内容卡片样式 */
.profile-card {
    background: var(--bs-card-bg);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
}

/* 头像上传样式 */
.avatar-upload {
    position: relative;
    display: inline-block;
}

.avatar-upload input[type="file"] {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.avatar-upload::after {
    content: '更换头像';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: var(--bs-secondary);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.avatar-upload:hover::after {
    opacity: 1;
}

/* 收藏和历史网格 */
.favorites-grid,
.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.favorite-item,
.history-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.3s ease;
    aspect-ratio: 3/4;
}

.favorite-item:hover,
.history-item:hover {
    transform: translateY(-2px);
}

.favorite-item img,
.history-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.favorite-overlay,
.history-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 1rem 0.75rem 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.favorite-item:hover .favorite-overlay,
.history-item:hover .history-overlay {
    opacity: 1;
}

.favorite-title,
.history-title {
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.favorite-meta,
.history-meta {
    font-size: 0.75rem;
    opacity: 0.8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--bs-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h6 {
    margin-bottom: 0.5rem;
}

/* 表单样式增强 */
.profile-form .form-label {
    font-weight: 600;
    color: var(--bs-body-color);
    margin-bottom: 0.5rem;
}

.profile-form .form-control,
.profile-form .form-select {
    border-radius: 8px;
    border-color: var(--bs-border-color);
    transition: all 0.2s ease;
}

.profile-form .form-control:focus,
.profile-form .form-select:focus {
    border-color: var(--bs-warning);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* 安全设置样式 */
.security-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--bs-border-color);
}

.security-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.device-list .list-group-item {
    border-radius: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid var(--bs-border-color);
}

/* VIP 样式 */
.vip-benefits {
    background: linear-gradient(135deg, #ffc107, #ff8f00);
    color: var(--bs-dark);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
}

.vip-benefits h6 {
    color: var(--bs-dark);
}

.vip-benefits .list-unstyled li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.vip-benefits .list-unstyled i {
    margin-right: 0.5rem;
    color: var(--bs-dark);
}

/* 统计数字样式 */
.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--bs-warning);
}

.stats-label {
    font-size: 0.8rem;
    color: var(--bs-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: var(--bs-gray-200);
}

.progress-bar {
    border-radius: 4px;
    background: linear-gradient(90deg, var(--bs-warning), #ff8f00);
}

/* 徽章样式 */
.badge-custom {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-vip {
    background: linear-gradient(45deg, #ffc107, #ff8f00);
    color: var(--bs-dark);
}

.badge-premium {
    background: linear-gradient(45deg, #6f42c1, #8e44ad);
    color: white;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .favorites-grid,
    .history-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .favorites-grid,
    .history-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
    }
    
    .profile-form .row .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .vip-benefits {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .favorites-grid,
    .history-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-body .row.text-center {
        margin-bottom: 1rem;
    }
    
    .list-group-item {
        padding: 0.75rem;
    }
    
    .list-group-item .badge {
        font-size: 0.7rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-content.active {
    animation: fadeIn 0.3s ease;
}

/* 按钮增强 */
.btn-outline-warning:hover {
    color: var(--bs-dark);
    background-color: var(--bs-warning);
    border-color: var(--bs-warning);
}

.btn-warning {
    color: var(--bs-dark);
    background-color: var(--bs-warning);
    border-color: var(--bs-warning);
}

.btn-warning:hover {
    color: var(--bs-dark);
    background-color: #e0a800;
    border-color: #d39e00;
}

/* 卡片阴影增强 */
.card {
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

/* 文本选择样式 */
::selection {
    background-color: var(--bs-warning);
    color: var(--bs-dark);
}
