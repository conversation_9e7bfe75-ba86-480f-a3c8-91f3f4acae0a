/* 丽片网自定义样式 */

/* 基础样式 */
html, body {
    font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: #1a1a1a !important;
    color: #e9ecef !important;
    min-height: 100vh;
    opacity: 1 !important;
    visibility: visible !important;
    margin: 0;
    padding: 0;
}

/* 确保页面内容立即可见 */
.container, .container-fluid, main, .content {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 防止页面闪烁和黑屏 */
.card, .album-card, .video-card, img {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* 强制显示主要内容区域 */
.main-content {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 移除可能的隐藏样式 */
.hidden {
    display: block !important;
}

/* 确保Bootstrap组件正常显示 */
.row {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.col, .col-12, .col-md-6, .col-lg-4, [class*="col-"] {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* 导航栏样式 */
.navbar {
    min-height: 60px;
}

.navbar .container {
    display: flex !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.navbar-brand {
    display: flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    margin-right: 1rem !important;
}

.navbar-collapse {
    display: flex !important;
    flex-basis: auto !important;
    flex-grow: 1 !important;
    align-items: center !important;
}

.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
}

.navbar-dark .navbar-nav .nav-link {
    color: #e9ecef !important;
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* 强制导航栏单行布局 */
@media (min-width: 992px) {
    .navbar-expand-lg .navbar-collapse {
        display: flex !important;
        flex-basis: auto;
    }

    .navbar-expand-lg .navbar-toggler {
        display: none;
    }
}

/* 修复可能的Bootstrap冲突 */
.navbar > .container {
    display: flex !important;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

.navbar-brand {
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 28px;
    text-decoration: none;
    white-space: nowrap;
}

.dropdown-menu {
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
}

.dropdown-item:hover {
    background-color: #495057 !important;
    color: #ffc107 !important;
}

/* 卡片样式 */
.card {
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2) !important;
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* 套图网格布局 */
.album-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.album-card {
    background: #212529;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #495057;
}

.album-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3);
    border-color: #ffc107;
}

.album-card .card-img-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.album-card .card-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.album-card:hover .card-img-container img {
    transform: scale(1.1);
}

.album-card .card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.8) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.album-card:hover .card-overlay {
    opacity: 1;
}

.album-card .card-stats {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    color: white;
    font-size: 0.9em;
}

.album-card .card-body {
    padding: 15px;
}

.album-card .card-title {
    font-size: 1.1em;
    margin-bottom: 8px;
    color: #ffc107 !important;
    text-decoration: none !important;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.album-card .card-title:hover {
    color: #fff3cd !important;
}

/* 确保标题链接在所有状态下都可见 */
.album-card a.card-title,
.album-card a.card-title:link,
.album-card a.card-title:visited,
.album-card a.card-title:active {
    color: #ffc107 !important;
    text-decoration: none !important;
}

.album-card a.card-title:hover,
.album-card a.card-title:focus {
    color: #fff3cd !important;
    text-decoration: none !important;
}

.album-card .card-text {
    color: #adb5bd;
    font-size: 0.9em;
    margin-bottom: 10px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.album-card .card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8em;
    color: #adb5bd !important; /* 改为更亮的灰色，确保在深色背景下可见 */
}

/* 按钮样式 */
.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #495057;
    font-weight: 500;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #495057;
}

/* 强制覆盖黄色按钮文字颜色 */
.btn-warning,
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active {
    color: #495057 !important;
}

/* 面包屑导航样式优化 */
.breadcrumb-item + .breadcrumb-item::before {
    content: ">" !important;
    color: #6c757d !important;
    margin: 0 0.25rem !important; /* 缩小间距从0.5rem到0.25rem */
    font-weight: normal !important;
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

/* 分页样式 */
.pagination .page-link {
    background-color: #212529;
    border-color: #495057;
    color: #e9ecef;
}

.pagination .page-link:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.pagination .page-item.active .page-link {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

/* 表单样式 */
.form-control {
    background-color: #212529 !important;
    border-color: #495057 !important;
    color: #e9ecef !important;
}

.form-control:focus {
    background-color: #212529 !important;
    border-color: #ffc107 !important;
    color: #e9ecef !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

.form-select {
    background-color: #212529 !important;
    border-color: #495057 !important;
    color: #e9ecef !important;
}

.form-select:focus {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
}

/* 模态框样式 */
.modal-content {
    background-color: #212529 !important;
    border: 1px solid #495057 !important;
}

.modal-header {
    border-bottom: 1px solid #495057 !important;
}

.modal-footer {
    border-top: 1px solid #495057 !important;
}

/* 提示框样式 */
.alert-info {
    background-color: rgba(13, 202, 240, 0.1) !important;
    border-color: rgba(13, 202, 240, 0.2) !important;
    color: #9ec5fe !important;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-color: rgba(255, 193, 7, 0.2) !important;
    color: #fff3cd !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.2) !important;
    color: #f8d7da !important;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    border-color: rgba(25, 135, 84, 0.2) !important;
    color: #d1e7dd !important;
}

/* 图片浏览器样式 */
.image-viewer {
    background-color: rgba(0, 0, 0, 0.9);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-viewer img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
}

.image-viewer .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 20px;
    color: #000;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 193, 7, 0.3);
    border-radius: 50%;
    border-top-color: #ffc107;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .album-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        padding: 15px 0;
    }
    
    .album-card .card-img-container {
        height: 180px;
    }
    
    .navbar-brand {
        font-size: 1.1em;
        display: flex !important;
        align-items: center !important;
    }

    .navbar .container {
        display: flex !important;
        align-items: center !important;
        flex-wrap: nowrap !important;
    }
    
    .container {
        padding: 0 10px;
    }
}

@media (max-width: 576px) {
    .album-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 10px;
        padding: 10px 0;
    }
    
    .album-card .card-img-container {
        height: 150px;
    }
    
    .album-card .card-body {
        padding: 10px;
    }
    
    .album-card .card-title {
        font-size: 1em;
    }
    
    .album-card .card-text {
        font-size: 0.8em;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #495057;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ffc107;
}

/* 视频卡片特殊样式 */
.video-card .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3em;
    color: rgba(255, 255, 255, 0.8);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.video-card:hover .play-button {
    opacity: 1;
}

.video-card .card-overlay {
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.9) 100%);
}

/* 特效样式 */
.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px #ffc107, 0 0 10px #ffc107; }
    to { text-shadow: 0 0 10px #ffc107, 0 0 20px #ffc107; }
}

/* VIP标识 */
.vip-badge {
    background: linear-gradient(45deg, #ffc107, #ff6b6b);
    color: white;
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7em;
    margin-left: 5px;
}

/* 会员权限提示 */
.member-notice {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin: 20px 0;
}

.member-notice h5 {
    color: #ffc107;
    margin-bottom: 15px;
}

.member-notice p {
    color: #adb5bd;
    margin-bottom: 15px;
}

/* 统计数字样式 */
.stats-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #ffc107;
}

/* 图片懒加载占位符 */
.img-placeholder {
    background: linear-gradient(90deg, #2c2c2c 25%, #3c3c3c 50%, #2c2c2c 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.img-placeholder::before {
    content: "加载中...";
    color: #adb5bd;
    font-size: 14px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 修复文字颜色问题 - 确保在深色背景下可见 */
.text-muted {
    color: #adb5bd !important; /* 原来的 #6c757d 太暗，改为更亮的灰色 */
}

/* 确保所有文字在深色背景下都可见 */
.card-text, .small, small {
    color: #adb5bd !important;
}

/* 链接颜色优化 */
a.text-muted {
    color: #adb5bd !important;
}

a.text-muted:hover {
    color: #ffc107 !important;
}

/* 确保所有链接在深色背景下可见 */
a {
    color: #ffc107 !important;
    text-decoration: none !important;
}

a:hover, a:focus {
    color: #fff3cd !important;
    text-decoration: none !important;
}

/* Bootstrap链接颜色覆盖 */
.text-decoration-none {
    text-decoration: none !important;
}

/* 表单标签颜色 */
.form-label {
    color: #e9ecef !important;
}

/* 输入框占位符颜色 */
.form-control::placeholder {
    color: #6c757d !important;
}

/* 下拉菜单项颜色 */
.dropdown-item {
    color: #e9ecef !important;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: #ffc107 !important;
    background-color: #495057 !important;
}

/* 页脚文字颜色优化 */
footer .text-muted {
    color: #adb5bd !important;
}

footer h6 {
    color: #e9ecef !important;
}

footer ul.text-muted li {
    color: #adb5bd !important;
}

/* 确保所有链接在深色背景下可见 */
footer a.text-muted {
    color: #adb5bd !important;
    transition: color 0.3s ease;
}

footer a.text-muted:hover {
    color: #ffc107 !important;
}

/* 表单检查框标签颜色 */
.form-check-label {
    color: #e9ecef !important;
}

/* 输入组文字颜色 */
.input-group-text {
    color: #ffc107 !important;
    background-color: #343a40 !important;
    border-color: #495057 !important;
}

/* 按钮文字确保可见 */
.btn-outline-secondary {
    color: #adb5bd !important;
    border-color: #495057 !important;
}

.btn-outline-secondary:hover {
    color: #000 !important;
    background-color: #adb5bd !important;
    border-color: #adb5bd !important;
}

/* 确保所有段落文字可见 */
p {
    color: #e9ecef !important;
}

/* 列表项文字颜色 */
li {
    color: #e9ecef !important;
}

/* 强制所有文字在深色背景下可见 */
.text-dark {
    color: #e9ecef !important;
}

/* 卡片内容文字颜色 */
.card-body p, .card-body small, .card-body .small {
    color: #adb5bd !important;
}

/* 确保图片加载后移除占位符效果 */
img.loaded {
    animation: none;
    background: none;
}

img.loaded::before {
    display: none;
}

/* 图片分页查看模式样式 */
.image-gallery {
    transition: all 0.3s ease;
    min-height: 400px;
}

/* 单张模式 */
.image-gallery.single-mode {
    display: block !important;
    grid-template-columns: unset !important;
    gap: unset !important;
    width: 100%;
    min-height: 70vh;
}

.image-gallery.single-mode .image-item {
    width: 100%;
    display: block;
    cursor: pointer;
    position: relative;
    aspect-ratio: unset !important;
    overflow: hidden;
}

.image-gallery.single-mode .image-item img {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    min-width: 100% !important;
    object-fit: contain !important;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: block !important;
}

.image-gallery.single-mode .image-item:hover img {
    transform: scale(1.05);
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

/* 网格模式 - 小网格 (6张以内) */
.image-gallery.grid-mode.grid-small {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

/* 网格模式 - 大网格 (6张以上) */
.image-gallery.grid-mode.grid-large {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 15px 0;
}

.image-gallery.grid-mode .image-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background: #2c2c2c;
}

.image-gallery.grid-mode .image-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-gallery.grid-mode .image-item:hover img {
    transform: scale(1.05);
}

/* 分页控制样式 */
.pagination-info {
    color: #adb5bd;
    font-size: 14px;
}

.pagination-buttons .btn {
    min-width: 80px;
}

.pagination-buttons .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 查看模式按钮样式 */
.btn-group .btn.active {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000 !important;
}

/* VIP自定义设置样式 */
#customSettings .form-control {
    background-color: #343a40;
    border-color: #495057;
    color: #e9ecef;
}

#customSettings .form-control:focus {
    background-color: #495057;
    border-color: #ffc107;
    color: #e9ecef;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .image-gallery.grid-mode.grid-small {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .image-gallery.grid-mode.grid-large {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
    }

    .image-gallery.grid-mode .image-item img {
        height: 200px;
    }

    .pagination-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .pagination-buttons .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .image-gallery.grid-mode.grid-small,
    .image-gallery.grid-mode.grid-large {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
    }

    .image-gallery.grid-mode .image-item img {
        height: 180px;
    }

    .image-gallery.single-mode .image-item img {
        width: 100% !important;
        height: auto !important;
        max-width: 100% !important;
        min-width: 100% !important;
    }
}
