/* 套图详情页样式 */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.image-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
    aspect-ratio: 3/4;
}

.image-item:hover {
    transform: scale(1.02);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    opacity: 0;
    transition: opacity 0.3s ease;
    padding: 0.5rem;
}

.image-item:hover .image-overlay {
    opacity: 1;
}

.image-number {
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    align-self: flex-start;
}

.image-actions {
    align-self: flex-end;
}

/* 图片灯箱样式 */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.95);
    display: none;
    visibility: hidden;
    justify-content: center;
    align-items: center;
    z-index: 99999;
}

.lightbox-content {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(0,0,0,0.95);
    overflow: hidden;
    position: relative;
}

.lightbox-content.windowed {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    height: auto;
    background: #212529;
    border-radius: 8px;
}

.lightbox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    position: relative;
    z-index: 1000;
}

.lightbox-title {
    font-weight: 600;
}

.lightbox-actions {
    display: flex;
    gap: 0.5rem;
}

.lightbox-body {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    min-height: 400px;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: grab;
}

.lightbox-body img {
    width: 100vw;
    height: auto;
    min-height: 100vh;
    object-fit: cover;
    cursor: grab;
    position: absolute;
    top: 0;
    left: 0;
    user-select: none;
    -webkit-user-drag: none;
    transition: none;
}

.lightbox-body img:active {
    cursor: grabbing;
}

.lightbox-body.dragging {
    cursor: grabbing;
}

.lightbox-body.dragging img {
    cursor: grabbing;
}

.lightbox-content.windowed .lightbox-body img {
    max-height: 70vh;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 2rem;
    pointer-events: none;
    z-index: 1000;
}

.lightbox-prev,
.lightbox-next {
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    font-size: 20px;
    z-index: 1001;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(0,0,0,0.8);
    transform: scale(1.1);
}

/* 全屏模式下的导航按钮 */
.lightbox-content:not(.windowed) .lightbox-nav {
    padding: 0 2rem;
}

.lightbox-content:not(.windowed) .lightbox-prev,
.lightbox-content:not(.windowed) .lightbox-next {
    width: 70px;
    height: 70px;
    font-size: 20px;
}

.lightbox-footer {
    padding: 1rem;
    background: rgba(0,0,0,0.8);
    color: white;
    text-align: center;
    position: relative;
    z-index: 1000;
}

.lightbox-counter {
    font-size: 0.9rem;
}

/* 视图模式样式 */
.image-gallery.single-mode {
    display: block !important;
    grid-template-columns: unset !important;
    gap: unset !important;
    width: 100%;
    min-height: 70vh;
}

.image-gallery.single-mode .image-item {
    width: 100%;
    display: block;
    position: relative;
    aspect-ratio: unset !important;
    overflow: hidden;
    pointer-events: auto;
}

.image-gallery.single-mode .image-item img {
    width: 100% !important;
    height: auto !important;
    max-width: 100% !important;
    min-width: 100% !important;
    object-fit: contain !important;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: block !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 100 !important;
}

.image-gallery.single-mode .image-item:hover img {
    transform: scale(1.05);
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

.image-gallery.single-mode .image-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    border-radius: 8px;
    padding: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 50;
}

.image-gallery.single-mode .image-item:hover .image-overlay {
    opacity: 1;
}

/* 确保覆盖层中的按钮可以点击 */
.image-gallery.single-mode .image-overlay .image-actions {
    pointer-events: auto;
}

.image-gallery.single-mode .image-overlay .image-actions button {
    pointer-events: auto;
}

.image-gallery.waterfall-mode {
    columns: 4;
    column-gap: 1rem;
}

.image-gallery.waterfall-mode .image-item {
    break-inside: avoid;
    margin-bottom: 1rem;
    aspect-ratio: auto;
}

.image-gallery.theater-mode {
    grid-template-columns: 1fr;
    gap: 2rem;
}

.image-gallery.theater-mode .image-item {
    aspect-ratio: auto;
    max-height: 80vh;
}

/* 相关推荐样式 */
.related-album {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    aspect-ratio: 3/4;
}

.related-album img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-album:hover img {
    transform: scale(1.05);
}

.related-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 0.75rem 0.5rem 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.related-album:hover .related-overlay {
    opacity: 1;
}

.related-title {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.related-stats {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* 套图描述样式 */
.album-description {
    line-height: 1.6;
    color: var(--bs-secondary);
}

/* 标签样式 */
.tags .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    transition: all 0.2s ease;
}

.tags .badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* 评论区样式 */
.comment-form textarea {
    resize: vertical;
    min-height: 100px;
}

.comments-list {
    min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
    
    .image-gallery.waterfall-mode {
        columns: 3;
    }
}

@media (max-width: 768px) {
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    .image-gallery.waterfall-mode {
        columns: 2;
    }
    
    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .lightbox-header {
        padding: 0.75rem;
    }
    
    .lightbox-title {
        font-size: 0.9rem;
    }
    
    .lightbox-nav {
        padding: 0 0.5rem;
    }
    
    .lightbox-prev,
    .lightbox-next {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .image-gallery {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .image-gallery.waterfall-mode {
        columns: 1;
    }
    
    .image-item {
        aspect-ratio: 1;
    }
}

/* 加载状态 */
.loading-placeholder {
    background: linear-gradient(90deg, 
        var(--bs-gray-200) 25%, 
        var(--bs-gray-100) 50%, 
        var(--bs-gray-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .loading-placeholder {
        background: linear-gradient(90deg, 
            var(--bs-gray-800) 25%, 
            var(--bs-gray-700) 50%, 
            var(--bs-gray-800) 75%);
        background-size: 200% 100%;
    }
}
