/* 套图列表样式 */
.album-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 0;
}

.album-card {
    background: var(--bs-card-bg);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.album-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-img-container {
    position: relative;
    overflow: hidden;
    padding-bottom: 60%; /* 5:3 比例 */
}

.card-img-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.album-card:hover .card-img-container img {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.album-card:hover .card-overlay {
    opacity: 1;
}

.card-stats {
    color: white;
    font-size: 0.875rem;
}

.album-card .card-body {
    padding: 1rem;
}

.album-card .card-title {
    color: #ffc107 !important;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-decoration: none !important;
}

.album-card .card-title:hover {
    color: #fff3cd !important;
    text-decoration: none !important;
}

/* 确保标题链接在所有状态下都可见 */
.album-card a.card-title,
.album-card a.card-title:link,
.album-card a.card-title:visited,
.album-card a.card-title:active {
    color: #ffc107 !important;
    text-decoration: none !important;
}

.album-card a.card-title:hover,
.album-card a.card-title:focus {
    color: #fff3cd !important;
    text-decoration: none !important;
}

.album-card .card-text {
    color: var(--bs-secondary);
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

.card-meta {
    font-size: 0.8rem;
}

.card-meta .count {
    transition: color 0.2s ease;
}

/* 响应式网格 */
@media (max-width: 1200px) {
    .album-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.25rem;
    }
}

@media (max-width: 768px) {
    .album-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .album-card .card-body {
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .album-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .album-card .card-title {
        font-size: 0.875rem;
    }
}

/* 列表视图样式 */
.album-grid.list-view {
    display: block;
}

.album-grid.list-view .album-card {
    display: flex;
    margin-bottom: 1rem;
}

.album-grid.list-view .card-img-container {
    width: 200px;
    padding-bottom: 0;
    height: 120px;
    flex-shrink: 0;
}

.album-grid.list-view .card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ffc107;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 筛选表单样式 */
.card .form-select,
.card .form-control {
    border-color: var(--bs-border-color);
    transition: all 0.2s ease;
}

.card .form-select:focus,
.card .form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: var(--bs-body-color);
    background-color: var(--bs-card-bg);
    border-color: var(--bs-border-color);
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107;
}

.pagination .page-item.active .page-link {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107;
}

/* 按钮样式增强 */
.btn-favorite.active,
.btn-like.active {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-favorite.active .fa-heart::before {
    content: "\f004"; /* 实心爱心 */
}

.btn-like.active .fa-thumbs-up::before {
    content: "\f164"; /* 实心点赞 */
}

/* Toast 通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    background-color: var(--bs-card-bg);
    border: 1px solid var(--bs-border-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.toast.toast-success {
    border-left: 4px solid #28a745;
}

.toast.toast-error {
    border-left: 4px solid #dc3545;
}

.toast.toast-warning {
    border-left: 4px solid #ffc107;
}

.toast.toast-info {
    border-left: 4px solid #17a2b8;
}
