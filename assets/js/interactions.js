/**
 * 通用用户交互功能 - 逐步恢复版本
 */

// 防止重复声明
if (typeof window.ToastNotification === 'undefined') {

// Toast 通知系统
class ToastNotification {
    constructor() {
        this.container = this.createContainer();
    }

    createContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        this.container.appendChild(toast);

        // 使用Bootstrap的Toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration
        });

        bsToast.show();

        // 自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, duration + 500);
    }
}

// 全局Toast实例
const toastNotification = new ToastNotification();

// 全局函数
function showToast(message, type = 'info', duration = 3000) {
    toastNotification.show(message, type, duration);
}

// 用户交互处理
class UserInteractionHandler {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 收藏功能
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-favorite, .btn-favorite *')) {
                e.preventDefault();
                const btn = e.target.closest('.btn-favorite');
                this.toggleFavorite(btn);
            }
        });

        // 下载功能
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-download, .btn-download *')) {
                e.preventDefault();
                const btn = e.target.closest('.btn-download');
                this.downloadFile(btn);
            }
        });

        // 复制链接
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-copy, .btn-copy *')) {
                e.preventDefault();
                const btn = e.target.closest('.btn-copy');
                this.copyToClipboard(btn);
            }
        });
    }

    toggleFavorite(btn) {
        const albumId = btn.dataset.albumId || btn.dataset.videoId;
        const type = btn.dataset.type || 'album';

        if (!albumId) return;

        fetch('/api/user?action=toggle-favorite', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                id: albumId,
                type: type
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const icon = btn.querySelector('i');
                const text = btn.querySelector('.btn-text');

                if (data.favorited) {
                    icon.className = 'fas fa-heart';
                    if (text) text.textContent = '已收藏';
                    btn.classList.add('favorited');
                    showToast('收藏成功', 'success', 2000);
                } else {
                    icon.className = 'far fa-heart';
                    if (text) text.textContent = '收藏';
                    btn.classList.remove('favorited');
                    showToast('取消收藏', 'info', 2000);
                }
            } else {
                showToast(data.message || '操作失败', 'danger', 3000);
            }
        })
        .catch(error => {
            console.error('收藏操作失败:', error);
            showToast('网络错误，请重试', 'danger', 3000);
        });
    }

    downloadFile(btn) {
        const url = btn.dataset.url;
        const filename = btn.dataset.filename;

        if (!url) return;

        // 创建隐藏的下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = filename || 'download';
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showToast('开始下载...', 'success', 2000);
    }

    copyToClipboard(btn) {
        const text = btn.dataset.text || window.location.href;

        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('复制成功', 'success', 2000);
            }).catch(() => {
                showToast('复制失败', 'danger', 2000);
            });
        } else {
            // 降级方案
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                showToast('复制成功', 'success', 2000);
            } catch (err) {
                showToast('复制失败', 'danger', 2000);
            }
            document.body.removeChild(textarea);
        }
    }
}

// 懒加载图片
class LazyImageLoader {
    constructor(options = {}) {
        this.options = {
            threshold: 0.1,
            rootMargin: '50px',
            ...options
        };
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver(
                this.handleIntersection.bind(this),
                this.options
            );
            this.observeImages();
        } else {
            // 降级方案
            this.loadAllImages();
        }
    }

    observeImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.observer.observe(img));
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadImage(entry.target);
                this.observer.unobserve(entry.target);
            }
        });
    }

    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }

    loadAllImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.loadImage(img));
    }
}

// 无限滚动
class InfiniteScroll {
    constructor(options = {}) {
        this.loading = false;
        this.hasMore = true;
        this.page = 1;
        this.container = options.container || document.body;
        this.threshold = options.threshold || 100;
        this.loadCallback = options.loadCallback || (() => {});

        this.init();
    }

    init() {
        this.bindEvents();
        this.createLoader();
    }

    bindEvents() {
        window.addEventListener('scroll', this.handleScroll.bind(this));
    }

    handleScroll() {
        if (this.loading || !this.hasMore) return;

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        if (scrollTop + windowHeight >= documentHeight - this.threshold) {
            this.loadMore();
        }
    }

    loadMore() {
        if (this.loading) return;

        this.loading = true;
        this.showLoading();
        this.page++;

        this.loadCallback(this.page)
            .then(data => {
                if (data && data.length > 0) {
                    this.appendData(data);
                } else {
                    this.hasMore = false;
                    this.showNoMore();
                }
            })
            .catch(error => {
                console.error('加载失败:', error);
                showToast('加载失败，请重试', 'danger', 3000);
                this.page--; // 回退页码
            })
            .finally(() => {
                this.loading = false;
                this.hideLoading();
            });
    }

    appendData(data) {
        // 子类需要实现这个方法
        console.log('需要实现 appendData 方法');
    }

    createLoader() {
        const loader = document.createElement('div');
        loader.className = 'infinite-loader text-center py-4';
        loader.style.display = 'none';
        loader.innerHTML = `
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">加载中...</div>
        `;

        this.container.appendChild(loader);
    }

    showLoading() {
        const loader = document.querySelector('.infinite-loader');
        if (loader) {
            loader.style.display = 'block';
        }
    }

    hideLoading() {
        const loader = document.querySelector('.infinite-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }

    showNoMore() {
        let noMore = document.querySelector('.no-more');
        if (!noMore) {
            noMore = document.createElement('div');
            noMore.className = 'no-more text-center py-4 text-muted';
            noMore.innerHTML = '<i class="fas fa-check-circle me-2"></i>没有更多内容了';
            this.container.appendChild(noMore);
        }
    }
}

// 图片预览
class ImagePreview {
    constructor() {
        this.modal = null;
        this.images = [];
        this.currentIndex = 0;
        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
    }

    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'image-preview-modal';
        this.modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        this.modal.innerHTML = `
            <div class="preview-container" style="position: relative; max-width: 90%; max-height: 90%;">
                <img class="preview-image" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                <button class="btn-close-preview" style="position: absolute; top: -40px; right: 0; background: none; border: none; color: white; font-size: 24px; cursor: pointer;">&times;</button>
                <button class="nav-prev" style="position: absolute; left: -50px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.2); border: none; color: white; padding: 10px; cursor: pointer; border-radius: 50%;">&#8249;</button>
                <button class="nav-next" style="position: absolute; right: -50px; top: 50%; transform: translateY(-50%); background: rgba(255,255,255,0.2); border: none; color: white; padding: 10px; cursor: pointer; border-radius: 50%;">&#8250;</button>
                <div class="image-info" style="position: absolute; bottom: -40px; left: 0; color: white;">
                    <span class="image-counter"></span>
                </div>
            </div>
        `;

        document.body.appendChild(this.modal);
    }

    bindEvents() {
        // 点击图片预览
        document.addEventListener('click', (e) => {
            if (e.target.matches('img[data-preview]')) {
                e.preventDefault();
                this.showPreview(e.target);
            }
        });

        // 关闭预览
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal || e.target.matches('.btn-close-preview')) {
                this.hidePreview();
            }
        });

        // 导航按钮
        this.modal.querySelector('.nav-prev').addEventListener('click', () => this.prevImage());
        this.modal.querySelector('.nav-next').addEventListener('click', () => this.nextImage());

        // 键盘导航
        document.addEventListener('keydown', (e) => {
            if (this.modal.style.display === 'flex') {
                switch(e.key) {
                    case 'Escape':
                        this.hidePreview();
                        break;
                    case 'ArrowLeft':
                        this.prevImage();
                        break;
                    case 'ArrowRight':
                        this.nextImage();
                        break;
                }
            }
        });
    }

    showPreview(img) {
        const group = img.dataset.group || 'default';
        this.images = Array.from(document.querySelectorAll(`img[data-preview][data-group="${group}"]`));
        this.currentIndex = this.images.indexOf(img);

        this.updatePreview();
        this.modal.style.display = 'flex';
        // 注释掉可能导致页面隐藏的代码
        // document.body.style.overflow = 'hidden';
    }

    hidePreview() {
        this.modal.style.display = 'none';
        // 注释掉可能导致问题的代码
        // document.body.style.overflow = 'auto';
    }

    prevImage() {
        this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.updatePreview();
    }

    nextImage() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
        this.updatePreview();
    }

    updatePreview() {
        const img = this.images[this.currentIndex];
        const previewImg = this.modal.querySelector('.preview-image');
        const counter = this.modal.querySelector('.image-counter');

        previewImg.src = img.dataset.preview || img.src;
        counter.textContent = `${this.currentIndex + 1} / ${this.images.length}`;

        // 导航按钮显示/隐藏
        const prevBtn = this.modal.querySelector('.nav-prev');
        const nextBtn = this.modal.querySelector('.nav-next');

        prevBtn.style.display = this.images.length > 1 ? 'flex' : 'none';
        nextBtn.style.display = this.images.length > 1 ? 'flex' : 'none';
    }
}

// 表单验证
class FormValidator {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 表单提交验证
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-validate]')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });

        // 实时验证
        document.addEventListener('blur', (e) => {
            if (e.target.matches('input[data-validate], textarea[data-validate]')) {
                this.validateField(e.target);
            }
        }, true);

        // 输入时清除错误状态
        document.addEventListener('input', (e) => {
            if (e.target.matches('input.is-invalid, textarea.is-invalid')) {
                this.clearFieldError(e.target);
            }
        });
    }

    validateForm(form) {
        const fields = form.querySelectorAll('input[data-validate], textarea[data-validate]');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(field) {
        const rules = field.dataset.validate.split('|');
        const value = field.value.trim();

        for (const rule of rules) {
            const [ruleName, ruleValue] = rule.split(':');

            if (!this.applyRule(field, value, ruleName, ruleValue)) {
                return false;
            }
        }

        this.clearFieldError(field);
        return true;
    }

    applyRule(field, value, ruleName, ruleValue) {
        switch (ruleName) {
            case 'required':
                if (!value) {
                    this.showFieldError(field, '此字段为必填项');
                    return false;
                }
                break;

            case 'min':
                if (value.length < parseInt(ruleValue)) {
                    this.showFieldError(field, `最少需要${ruleValue}个字符`);
                    return false;
                }
                break;

            case 'max':
                if (value.length > parseInt(ruleValue)) {
                    this.showFieldError(field, `最多允许${ruleValue}个字符`);
                    return false;
                }
                break;

            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (value && !emailRegex.test(value)) {
                    this.showFieldError(field, '请输入有效的邮箱地址');
                    return false;
                }
                break;

            case 'password':
                if (value && value.length < 6) {
                    this.showFieldError(field, '密码至少需要6个字符');
                    return false;
                }
                break;

            case 'confirm':
                const targetField = document.querySelector(`[name="${ruleValue}"]`);
                if (targetField && value !== targetField.value) {
                    this.showFieldError(field, '两次输入的密码不一致');
                    return false;
                }
                break;
        }

        return true;
    }

    showFieldError(field, message) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');

        // 移除旧的错误信息
        const oldError = field.parentNode.querySelector('.invalid-feedback');
        if (oldError) {
            oldError.remove();
        }

        // 添加新的错误信息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');

        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }
}

// 导出供外部使用
window.showToast = showToast;
window.ToastNotification = ToastNotification;
window.UserInteractionHandler = UserInteractionHandler;
window.LazyImageLoader = LazyImageLoader;
window.InfiniteScroll = InfiniteScroll;
window.ImagePreview = ImagePreview;
window.FormValidator = FormValidator;

} // 结束重复声明检查

// 初始化功能
document.addEventListener('DOMContentLoaded', function() {
    new UserInteractionHandler();
    new LazyImageLoader();
    new ImagePreview();
    new FormValidator();
});

console.log("interactions.js 完全恢复版本 - 所有功能已加载");
