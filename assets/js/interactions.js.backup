/**
 * 通用用户交互功能
 */

// 防止重复声明
if (typeof window.ToastNotification === 'undefined') {

// Toast 通知系统
class ToastNotification {
    constructor() {
        this.container = this.createContainer();
    }
    
    createContainer() {
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }
    
    show(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type} show`;
        toast.setAttribute('role', 'alert');
        
        const iconMap = {
            success: 'fas fa-check-circle text-success',
            error: 'fas fa-exclamation-circle text-danger',
            warning: 'fas fa-exclamation-triangle text-warning',
            info: 'fas fa-info-circle text-info'
        };
        
        toast.innerHTML = `
            <div class="toast-header">
                <i class="${iconMap[type]} me-2"></i>
                <strong class="me-auto">提示</strong>
                <button type="button" class="btn-close" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        this.container.appendChild(toast);
        
        // 关闭按钮事件
        toast.querySelector('.btn-close').onclick = () => {
            this.hide(toast);
        };
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hide(toast);
            }, duration);
        }
        
        return toast;
    }
    
    hide(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

// 全局 Toast 实例
const toast = new ToastNotification();

// 显示消息的便捷函数
function showToast(message, type = 'info', duration = 3000) {
    toast.show(message, type, duration);
}

// 用户交互处理器
class UserInteractionHandler {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        // 点赞按钮
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-like')) {
                this.handleLike(e.target.closest('.btn-like'));
            }
        });
        
        // 收藏按钮
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-favorite')) {
                this.handleFavorite(e.target.closest('.btn-favorite'));
            }
        });
        
        // 下载按钮
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-download')) {
                this.handleDownload(e.target.closest('.btn-download'));
            }
        });
    }
    
    async handleLike(button) {
        const type = button.dataset.type;
        const id = button.dataset.id;
        
        if (!type || !id) {
            showToast('参数错误', 'error');
            return;
        }
        
        try {
            button.disabled = true;
            
            const response = await fetch('/api/user?action=favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type, id })
            });
            
            const data = await response.json();
            
            if (data.success) {
                const isLiked = data.action === 'favorited';
                button.classList.toggle('active', isLiked);
                
                // 更新计数
                const countEl = button.querySelector('.count');
                if (countEl) {
                    let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                    count += isLiked ? 1 : -1;
                    countEl.textContent = this.formatNumber(count);
                }
                
                // 更新图标
                const icon = button.querySelector('i');
                if (icon) {
                    if (isLiked) {
                        icon.className = icon.className.replace('far', 'fas');
                    } else {
                        icon.className = icon.className.replace('fas', 'far');
                    }
                }
                
                showToast(data.message, 'success', 2000);
            } else {
                showToast(data.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('Like error:', error);
            showToast('网络错误，请重试', 'error');
        } finally {
            button.disabled = false;
        }
    }
    
    async handleFavorite(button) {
        const type = button.dataset.type;
        const id = button.dataset.id;
        
        if (!type || !id) {
            showToast('参数错误', 'error');
            return;
        }
        
        try {
            button.disabled = true;
            
            const response = await fetch('/api/user?action=favorite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type, id })
            });
            
            const data = await response.json();
            
            if (data.success) {
                const isFavorited = data.action === 'favorited';
                button.classList.toggle('active', isFavorited);
                
                // 更新计数
                const countEl = button.querySelector('.count');
                if (countEl) {
                    let count = parseInt(countEl.textContent.replace(/,/g, '')) || 0;
                    count += isFavorited ? 1 : -1;
                    countEl.textContent = this.formatNumber(count);
                }
                
                // 更新图标
                const icon = button.querySelector('i');
                if (icon) {
                    if (isFavorited) {
                        icon.className = icon.className.replace('far', 'fas');
                    } else {
                        icon.className = icon.className.replace('fas', 'far');
                    }
                }
                
                showToast(data.message, 'success', 2000);
            } else {
                showToast(data.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('Favorite error:', error);
            showToast('网络错误，请重试', 'error');
        } finally {
            button.disabled = false;
        }
    }
    
    async handleDownload(button) {
        const url = button.dataset.url;
        const filename = button.dataset.filename;
        
        if (!url) {
            showToast('下载链接无效', 'error');
            return;
        }
        
        try {
            button.disabled = true;
            
            // 创建隐藏的下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || 'download';
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showToast('开始下载...', 'success', 2000);
            
            // 记录下载
            fetch('/api/user?action=record-download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ url, filename })
            }).catch(error => {
                console.error('Download record error:', error);
            });
            
        } catch (error) {
            console.error('Download error:', error);
            showToast('下载失败', 'error');
        } finally {
            setTimeout(() => {
                button.disabled = false;
            }, 2000);
        }
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
}

// 懒加载图片
class LazyImageLoader {
    constructor() {
        this.observer = null;
        this.init();
    }
    
    init() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px'
            });
            
            this.observeImages();
        } else {
            // 降级处理
            this.loadAllImages();
        }
    }
    
    observeImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            this.observer.observe(img);
        });
    }
    
    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }
    
    loadAllImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            this.loadImage(img);
        });
    }
}

// 无限滚动加载
class InfiniteScroll {
    constructor(options = {}) {
        this.loading = false;
        this.hasMore = true;
        this.page = 1;
        this.container = options.container || document.body;
        this.threshold = options.threshold || 100;
        this.loadCallback = options.loadCallback || (() => {});
        
        this.init();
    }
    
    init() {
        window.addEventListener('scroll', this.handleScroll.bind(this));
    }
    
    handleScroll() {
        if (this.loading || !this.hasMore) return;
        
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;
        
        if (scrollTop + windowHeight >= documentHeight - this.threshold) {
            this.loadMore();
        }
    }
    
    async loadMore() {
        if (this.loading || !this.hasMore) return;
        
        this.loading = true;
        this.showLoading();
        
        try {
            const hasMore = await this.loadCallback(this.page + 1);
            
            if (hasMore) {
                this.page++;
            } else {
                this.hasMore = false;
                this.showNoMore();
            }
        } catch (error) {
            console.error('Load more error:', error);
            showToast('加载失败，请重试', 'error');
        } finally {
            this.loading = false;
            this.hideLoading();
        }
    }
    
    showLoading() {
        let loader = document.querySelector('.infinite-loader');
        if (!loader) {
            loader = document.createElement('div');
            loader.className = 'infinite-loader text-center p-4';
            loader.innerHTML = '<div class="loading"></div> 加载中...';
            this.container.appendChild(loader);
        }
        loader.style.display = 'block';
    }
    
    hideLoading() {
        const loader = document.querySelector('.infinite-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }
    
    showNoMore() {
        let noMore = document.querySelector('.no-more');
        if (!noMore) {
            noMore = document.createElement('div');
            noMore.className = 'no-more text-center p-4 text-muted';
            noMore.innerHTML = '<small>没有更多内容了</small>';
            this.container.appendChild(noMore);
        }
        noMore.style.display = 'block';
    }
    
    reset() {
        this.page = 1;
        this.hasMore = true;
        this.loading = false;
        
        const loader = document.querySelector('.infinite-loader');
        const noMore = document.querySelector('.no-more');
        
        if (loader) loader.remove();
        if (noMore) noMore.remove();
    }
}

// 图片预览功能
class ImagePreview {
    constructor() {
        this.currentIndex = 0;
        this.images = [];
        this.modal = null;
        this.init();
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        this.modal = document.createElement('div');
        this.modal.className = 'image-preview-modal';
        this.modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <span class="modal-title"></span>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <img class="preview-image" src="" alt="">
                    <button class="nav-prev"><i class="fas fa-chevron-left"></i></button>
                    <button class="nav-next"><i class="fas fa-chevron-right"></i></button>
                </div>
                <div class="modal-footer">
                    <span class="image-counter"></span>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.modal);
    }
    
    bindEvents() {
        // 点击图片预览
        document.addEventListener('click', (e) => {
            const img = e.target.closest('img[data-preview]');
            if (img) {
                e.preventDefault();
                this.showPreview(img);
            }
        });
        
        // 模态框事件
        this.modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop') || 
                e.target.classList.contains('modal-close')) {
                this.hidePreview();
            } else if (e.target.closest('.nav-prev')) {
                this.prevImage();
            } else if (e.target.closest('.nav-next')) {
                this.nextImage();
            }
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.modal.style.display === 'flex') {
                if (e.key === 'Escape') this.hidePreview();
                if (e.key === 'ArrowLeft') this.prevImage();
                if (e.key === 'ArrowRight') this.nextImage();
            }
        });
    }
    
    showPreview(img) {
        // 收集同组图片
        const group = img.dataset.group || 'default';
        this.images = Array.from(document.querySelectorAll(`img[data-preview][data-group="${group}"]`));
        this.currentIndex = this.images.indexOf(img);
        
        this.updatePreview();
        this.modal.style.display = 'flex';
        // 注释掉可能导致页面隐藏的代码
        // document.body.style.overflow = 'hidden';
    }
    
    hidePreview() {
        this.modal.style.display = 'none';
        // 注释掉可能导致问题的代码
        // document.body.style.overflow = 'auto';
    }
    
    prevImage() {
        this.currentIndex = (this.currentIndex - 1 + this.images.length) % this.images.length;
        this.updatePreview();
    }
    
    nextImage() {
        this.currentIndex = (this.currentIndex + 1) % this.images.length;
        this.updatePreview();
    }
    
    updatePreview() {
        const img = this.images[this.currentIndex];
        const previewImg = this.modal.querySelector('.preview-image');
        const title = this.modal.querySelector('.modal-title');
        const counter = this.modal.querySelector('.image-counter');
        
        previewImg.src = img.dataset.preview || img.src;
        title.textContent = img.alt || '图片预览';
        counter.textContent = `${this.currentIndex + 1} / ${this.images.length}`;
        
        // 导航按钮显示/隐藏
        const prevBtn = this.modal.querySelector('.nav-prev');
        const nextBtn = this.modal.querySelector('.nav-next');
        
        prevBtn.style.display = this.images.length > 1 ? 'flex' : 'none';
        nextBtn.style.display = this.images.length > 1 ? 'flex' : 'none';
    }
}

// 表单验证
class FormValidator {
    constructor(form, rules = {}) {
        this.form = form;
        this.rules = rules;
        this.init();
    }
    
    init() {
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // 实时验证
        Object.keys(this.rules).forEach(field => {
            const input = this.form.querySelector(`[name="${field}"]`);
            if (input) {
                input.addEventListener('blur', () => {
                    this.validateField(field);
                });
            }
        });
    }
    
    handleSubmit(e) {
        if (!this.validateAll()) {
            e.preventDefault();
        }
    }
    
    validateField(fieldName) {
        const input = this.form.querySelector(`[name="${fieldName}"]`);
        const rules = this.rules[fieldName];
        
        if (!input || !rules) return true;
        
        const value = input.value.trim();
        let isValid = true;
        let message = '';
        
        // 必填验证
        if (rules.required && !value) {
            isValid = false;
            message = rules.messages?.required || '此字段是必填的';
        }
        
        // 长度验证
        if (isValid && rules.minLength && value.length < rules.minLength) {
            isValid = false;
            message = rules.messages?.minLength || `最少需要${rules.minLength}个字符`;
        }
        
        if (isValid && rules.maxLength && value.length > rules.maxLength) {
            isValid = false;
            message = rules.messages?.maxLength || `最多允许${rules.maxLength}个字符`;
        }
        
        // 正则验证
        if (isValid && rules.pattern && !rules.pattern.test(value)) {
            isValid = false;
            message = rules.messages?.pattern || '格式不正确';
        }
        
        // 自定义验证
        if (isValid && rules.validator) {
            const result = rules.validator(value, input, this.form);
            if (result !== true) {
                isValid = false;
                message = result || '验证失败';
            }
        }
        
        this.showFieldResult(input, isValid, message);
        return isValid;
    }
    
    validateAll() {
        let isValid = true;
        
        Object.keys(this.rules).forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    showFieldResult(input, isValid, message) {
        // 移除之前的状态
        input.classList.remove('is-valid', 'is-invalid');
        
        const feedback = input.parentNode.querySelector('.invalid-feedback, .valid-feedback');
        if (feedback) {
            feedback.remove();
        }
        
        // 添加新状态
        if (!isValid) {
            input.classList.add('is-invalid');
            const feedbackEl = document.createElement('div');
            feedbackEl.className = 'invalid-feedback';
            feedbackEl.textContent = message;
            input.parentNode.appendChild(feedbackEl);
        } else if (input.value.trim()) {
            input.classList.add('is-valid');
        }
    }
}

// 初始化所有功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化用户交互处理器
    new UserInteractionHandler();
    
    // 初始化懒加载
    new LazyImageLoader();
    
    // 初始化图片预览
    new ImagePreview();
    
    // 工具提示
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});

// 导出供外部使用
window.showToast = showToast;
window.ToastNotification = ToastNotification;
window.UserInteractionHandler = UserInteractionHandler;
window.LazyImageLoader = LazyImageLoader;
window.InfiniteScroll = InfiniteScroll;
window.ImagePreview = ImagePreview;
window.FormValidator = FormValidator;

} // 结束重复声明检查
