/**
 * 丽片网 - 前端交互脚本
 */

// 全局变量
window.LiaPian = {
    user: null,
    config: {},
    
    // 初始化
    init: function() {
        this.initImageViewer();
        this.initAjaxSetup();
        this.initGlobalEvents();
        this.initLazyLoad();
    },
    
    // 初始化图片查看器
    initImageViewer: function() {
        $(document).on('click', '.album-image', function(e) {
            e.preventDefault();
            const imgSrc = $(this).attr('href') || $(this).find('img').attr('src');
            LiaPian.showImageViewer(imgSrc);
        });
    },
    
    // 显示图片查看器
    showImageViewer: function(src) {
        const viewer = $(`
            <div class="image-viewer">
                <img src="${src}" alt="图片预览">
                <button class="close-btn" onclick="LiaPian.closeImageViewer()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `);
        
        $('body').append(viewer);
        viewer.fadeIn(200);
        
        // ESC键关闭
        $(document).on('keyup.imageViewer', function(e) {
            if (e.keyCode === 27) {
                LiaPian.closeImageViewer();
            }
        });
        
        // 点击背景关闭
        viewer.on('click', function(e) {
            if (e.target === this) {
                LiaPian.closeImageViewer();
            }
        });
    },
    
    // 关闭图片查看器
    closeImageViewer: function() {
        $('.image-viewer').fadeOut(200, function() {
            $(this).remove();
        });
        $(document).off('keyup.imageViewer');
    },
    
    // 初始化Ajax设置
    initAjaxSetup: function() {
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            error: function(xhr, status, error) {
                if (xhr.status === 401) {
                    LiaPian.showMessage('请先登录', 'warning');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1500);
                } else if (xhr.status === 403) {
                    LiaPian.showMessage('权限不足', 'danger');
                } else {
                    LiaPian.showMessage('网络错误，请稍后重试', 'danger');
                }
            }
        });
    },
    
    // 初始化全局事件
    initGlobalEvents: function() {
        // 收藏功能
        $(document).on('click', '.btn-favorite', function(e) {
            e.preventDefault();
            const btn = $(this);
            const type = btn.data('type');
            const id = btn.data('id');
            
            LiaPian.toggleFavorite(type, id, btn);
        });
        
        // 点赞功能
        $(document).on('click', '.btn-like', function(e) {
            e.preventDefault();
            const btn = $(this);
            const type = btn.data('type');
            const id = btn.data('id');
            
            LiaPian.toggleLike(type, id, btn);
        });
        
        // 表单提交
        $(document).on('submit', '.ajax-form', function(e) {
            e.preventDefault();
            LiaPian.submitForm($(this));
        });
    },
    
    // 初始化懒加载
    initLazyLoad: function() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.dataset.src;
                        if (src) {
                            img.src = src;
                            img.classList.remove('img-placeholder');
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        }
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                img.classList.add('img-placeholder');
                imageObserver.observe(img);
            });
        }
    },
    
    // 切换收藏状态
    toggleFavorite: function(type, id, btn) {
        const isFavorited = btn.hasClass('favorited');
        const url = isFavorited ? '/api/unfavorite' : '/api/favorite';
        
        btn.prop('disabled', true);
        
        $.post(url, {
            type: type,
            id: id
        }).done(function(response) {
            if (response.success) {
                if (isFavorited) {
                    btn.removeClass('favorited btn-warning').addClass('btn-outline-warning');
                    btn.find('i').removeClass('fas').addClass('far');
                    btn.find('.count').text(parseInt(btn.find('.count').text()) - 1);
                } else {
                    btn.addClass('favorited btn-warning').removeClass('btn-outline-warning');
                    btn.find('i').removeClass('far').addClass('fas');
                    btn.find('.count').text(parseInt(btn.find('.count').text()) + 1);
                }
                LiaPian.showMessage(response.message, 'success');
            } else {
                LiaPian.showMessage(response.message, 'danger');
            }
        }).always(function() {
            btn.prop('disabled', false);
        });
    },
    
    // 切换点赞状态
    toggleLike: function(type, id, btn) {
        const isLiked = btn.hasClass('liked');
        const url = isLiked ? '/api/unlike' : '/api/like';
        
        btn.prop('disabled', true);
        
        $.post(url, {
            type: type,
            id: id
        }).done(function(response) {
            if (response.success) {
                if (isLiked) {
                    btn.removeClass('liked btn-danger').addClass('btn-outline-danger');
                    btn.find('i').removeClass('fas').addClass('far');
                } else {
                    btn.addClass('liked btn-danger').removeClass('btn-outline-danger');
                    btn.find('i').removeClass('far').addClass('fas');
                }
                btn.find('.count').text(response.data.count);
                LiaPian.showMessage(response.message, 'success');
            } else {
                LiaPian.showMessage(response.message, 'danger');
            }
        }).always(function() {
            btn.prop('disabled', false);
        });
    },
    
    // 提交表单
    submitForm: function(form) {
        const btn = form.find('[type=submit]');
        const originalText = btn.text();
        
        btn.prop('disabled', true).html('<span class=\"loading\"></span> 处理中...');
        
        $.ajax({
            url: form.attr('action') || window.location.href,
            method: form.attr('method') || 'POST',
            data: form.serialize(),
            dataType: 'json'
        }).done(function(response) {
            if (response.success) {
                LiaPian.showMessage(response.message, 'success');
                if (response.redirect) {
                    setTimeout(() => {
                        window.location.href = response.redirect;
                    }, 1500);
                }
                if (response.reload) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            } else {
                LiaPian.showMessage(response.message, 'danger');
            }
        }).always(function() {
            btn.prop('disabled', false).text(originalText);
        });
    },
    
    // 显示消息提示
    showMessage: function(message, type = 'info', duration = 3000) {
        const alertClass = {
            success: 'alert-success',
            danger: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        }[type] || 'alert-info';
        
        const alert = $(`
            <div class=\"alert ${alertClass} alert-dismissible fade show position-fixed\" 
                 style=\"top: 20px; right: 20px; z-index: 9999; min-width: 300px;\">
                ${message}
                <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"alert\"></button>
            </div>
        `);
        
        $('body').append(alert);
        
        setTimeout(() => {
            alert.alert('close');
        }, duration);
    },
    
    // 确认对话框
    confirm: function(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    },
    
    // 加载更多内容
    loadMore: function(url, container, callback) {
        const btn = $('.load-more-btn');
        const originalText = btn.text();
        
        btn.prop('disabled', true).html('<span class=\"loading\"></span> 加载中...');
        
        $.get(url).done(function(response) {
            if (response.success) {
                $(container).append(response.data.html);
                if (callback) callback(response.data);
                
                if (!response.data.has_more) {
                    btn.text('没有更多了').prop('disabled', true);
                } else {
                    btn.text(originalText).prop('disabled', false);
                }
            } else {
                LiaPian.showMessage(response.message, 'danger');
                btn.text(originalText).prop('disabled', false);
            }
        }).fail(function() {
            btn.text(originalText).prop('disabled', false);
        });
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                LiaPian.showMessage('复制成功', 'success');
            }).catch(() => {
                LiaPian.showMessage('复制失败', 'danger');
            });
        } else {
            // 降级方案
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                LiaPian.showMessage('复制成功', 'success');
            } catch (err) {
                LiaPian.showMessage('复制失败', 'danger');
            }
            document.body.removeChild(textarea);
        }
    },
    
    // 格式化数字
    formatNumber: function(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toString();
        }
    },
    
    // 格式化时间
    formatTime: function(timestamp) {
        const date = new Date(timestamp * 1000);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) {
            return '刚刚';
        } else if (diff < 3600000) {
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) {
            return Math.floor(diff / 3600000) + '小时前';
        } else if (diff < 2592000000) {
            return Math.floor(diff / 86400000) + '天前';
        } else {
            return date.getFullYear() + '-' + 
                   String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(date.getDate()).padStart(2, '0');
        }
    }
};

// 页面加载完成后初始化
$(document).ready(function() {
    LiaPian.init();
});

// 导出到全局
window.LP = LiaPian;
