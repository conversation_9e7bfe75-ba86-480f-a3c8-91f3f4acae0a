<?php
/**
 * 生成默认头像
 */

// 获取参数
$name = $_GET['name'] ?? 'User';
$size = intval($_GET['size'] ?? 100);
$bg = $_GET['bg'] ?? '';

// 限制尺寸
$size = max(50, min(500, $size));

// 如果没有指定背景色，根据名字生成
if (!$bg) {
    $hash = md5($name);
    $bg = substr($hash, 0, 6);
}

// 获取名字的第一个字符
$initial = mb_substr($name, 0, 1, 'UTF-8');
$initial = mb_strtoupper($initial, 'UTF-8');

// 创建图像
$image = imagecreatetruecolor($size, $size);

// 设置颜色
$bgColor = imagecolorallocate($image, 
    hexdec(substr($bg, 0, 2)), 
    hexdec(substr($bg, 2, 2)), 
    hexdec(substr($bg, 4, 2))
);

// 文字颜色（白色）
$textColor = imagecolorallocate($image, 255, 255, 255);

// 填充背景
imagefill($image, 0, 0, $bgColor);

// 添加文字
$fontSize = $size / 3;
$font = null; // 使用内置字体

// 如果没有TTF字体，使用内置字体
$fontWidth = imagefontwidth(5);
$fontHeight = imagefontheight(5);
$textWidth = strlen($initial) * $fontWidth;
$textHeight = $fontHeight;

$x = ($size - $textWidth) / 2;
$y = ($size - $textHeight) / 2;

imagestring($image, 5, $x, $y, $initial, $textColor);

// 输出图像
header('Content-Type: image/png');
header('Cache-Control: public, max-age=86400'); // 缓存1天
imagepng($image);
imagedestroy($image);
?>
