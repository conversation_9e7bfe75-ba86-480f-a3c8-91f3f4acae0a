<?php
/**
 * 生成占位图片
 */

// 获取参数
$width = intval($_GET['w'] ?? 300);
$height = intval($_GET['h'] ?? 200);
$text = $_GET['text'] ?? "{$width}x{$height}";
$bg = $_GET['bg'] ?? '2c2c2c';
$color = $_GET['color'] ?? 'ffffff';

// 限制尺寸
$width = max(10, min(2000, $width));
$height = max(10, min(2000, $height));

// 创建图像
$image = imagecreatetruecolor($width, $height);

// 设置颜色
$bgColor = imagecolorallocate($image, 
    hexdec(substr($bg, 0, 2)), 
    hexdec(substr($bg, 2, 2)), 
    hexdec(substr($bg, 4, 2))
);

$textColor = imagecolorallocate($image, 
    hexdec(substr($color, 0, 2)), 
    hexdec(substr($color, 2, 2)), 
    hexdec(substr($color, 4, 2))
);

// 填充背景
imagefill($image, 0, 0, $bgColor);

// 添加文字
$fontSize = max(12, min($width, $height) / 10);
$font = null; // 使用内置字体

// 计算文字位置
$textBox = imagettfbbox($fontSize, 0, $font, $text);
$textWidth = $textBox[4] - $textBox[0];
$textHeight = $textBox[1] - $textBox[7];

// 如果没有TTF字体，使用内置字体
if (!$font) {
    $fontWidth = imagefontwidth(5);
    $fontHeight = imagefontheight(5);
    $textWidth = strlen($text) * $fontWidth;
    $textHeight = $fontHeight;
    
    $x = ($width - $textWidth) / 2;
    $y = ($height - $textHeight) / 2;
    
    imagestring($image, 5, $x, $y, $text, $textColor);
} else {
    $x = ($width - $textWidth) / 2;
    $y = ($height - $textHeight) / 2 + $textHeight;
    
    imagettftext($image, $fontSize, 0, $x, $y, $textColor, $font, $text);
}

// 输出图像
header('Content-Type: image/png');
header('Cache-Control: public, max-age=86400'); // 缓存1天
imagepng($image);
imagedestroy($image);
?>
