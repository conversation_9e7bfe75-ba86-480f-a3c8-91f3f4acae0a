<?php
require_once __DIR__ . '/../core/Config.php';
require_once __DIR__ . '/../includes/functions.php';

// 检查并更新Session中的用户组信息
if (isset($_SESSION['user_id'])) {
    require_once __DIR__ . '/../core/Database.php';
    $db = Database::getInstance();

    // 获取用户当前的用户组信息
    $currentUserGroup = $db->fetch("
        SELECT ug.group_name
        FROM {$db->getPrefix()}users u
        LEFT JOIN {$db->getPrefix()}user_groups ug ON u.group_id = ug.id
        WHERE u.id = :id
    ", ['id' => $_SESSION['user_id']]);

    // 如果数据库中的用户组与Session中的不一致，更新Session
    if ($currentUserGroup && $currentUserGroup['group_name'] !== ($_SESSION['group_name'] ?? '')) {
        $_SESSION['group_name'] = $currentUserGroup['group_name'] ?? '会员';
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN" style="visibility: visible !important; opacity: 1 !important; background: #1a1a1a !important;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? Config::getSiteName() . ' - ' . Config::getSiteDescription(); ?></title>
    <meta name="description" content="<?php echo $pageDescription ?? Config::getSiteDescription() . '，提供高质量的美女图片和视频内容'; ?>">
    <meta name="keywords" content="<?php echo $pageKeywords ?? Config::getSiteKeywords(); ?>">

    <!-- 基础样式 -->
    <style>
        html, body {
            background-color: #1a1a1a;
            color: #e9ecef;
        }

        /* 强制修复导航栏布局 */
        .navbar .container {
            display: flex !important;
            align-items: center !important;
            flex-wrap: nowrap !important;
        }

        .navbar-brand {
            display: inline-flex !important;
            align-items: center !important;
            margin-right: 1rem !important;
        }

        .navbar-collapse {
            display: flex !important;
            align-items: center !important;
        }

        .navbar-nav {
            display: flex !important;
            flex-direction: row !important;
            margin: 0 !important;
        }

        @media (min-width: 992px) {
            .navbar-expand-lg .navbar-collapse {
                display: flex !important;
            }
        }
    </style>
    
    <!-- Bootstrap CSS (本地文件) -->
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome (本地文件) -->
    <link href="/assets/css/font-awesome.min.css" rel="stylesheet">
    
    <!-- 自定义CSS -->
    <link href="/assets/css/style.css?v=<?php echo time() + 1; ?>" rel="stylesheet">
    
    <!-- 页面特定CSS -->
    <?php if (isset($pageCSS)): ?>
        <?php foreach ((array)$pageCSS as $css): ?>
    <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">

</head>
<body class="bg-dark text-light" style="visibility: visible !important; opacity: 1 !important; display: block !important;">

<!-- 立即强制显示页面 -->
<script>
(function() {
    document.body.style.visibility = 'visible';
    document.body.style.opacity = '1';
    document.body.style.display = 'block';

    // 显示所有容器
    var containers = document.querySelectorAll('.container, .container-fluid, main, .content, .row, .col');
    for (var i = 0; i < containers.length; i++) {
        containers[i].style.visibility = 'visible';
        containers[i].style.opacity = '1';
        containers[i].style.display = 'block';
    }
})();
</script>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-black border-bottom border-secondary">
        <div class="container">
            <a class="navbar-brand fw-bold text-warning" href="/">
                <i class="fas fa-camera-retro me-2"></i><?php echo Config::getSiteName(); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>首页</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="/albums" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-images me-1"></i>套图
                        </a>
                        <ul class="dropdown-menu bg-dark border-secondary">
                            <?php
                            try {
                                $albumCategories = function_exists('getCategories') ? getCategories('album') : [];
                                foreach ($albumCategories as $category):
                            ?>
                            <li><a class="dropdown-item text-light" href="/albums/cat/<?php echo htmlspecialchars($category['slug'] ?? ''); ?>"><?php echo htmlspecialchars($category['name'] ?? ''); ?></a></li>
                            <?php
                                endforeach;
                            } catch (Exception $e) {
                                // 如果获取分类失败，显示默认链接
                                echo '<li><a class="dropdown-item text-light" href="/albums">所有套图</a></li>';
                            }
                            ?>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="/videos" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-video me-1"></i>视频
                        </a>
                        <ul class="dropdown-menu bg-dark border-secondary">
                            <?php
                            try {
                                $videoCategories = function_exists('getCategories') ? getCategories('video') : [];
                                foreach ($videoCategories as $category):
                            ?>
                            <li><a class="dropdown-item text-light" href="/videos/cat/<?php echo htmlspecialchars($category['slug'] ?? ''); ?>"><?php echo htmlspecialchars($category['name'] ?? ''); ?></a></li>
                            <?php
                                endforeach;
                            } catch (Exception $e) {
                                // 如果获取分类失败，显示默认链接
                                echo '<li><a class="dropdown-item text-light" href="/videos">所有视频</a></li>';
                            }
                            ?>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ranking"><i class="fas fa-trophy me-1"></i>排行</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/latest"><i class="fas fa-clock me-1"></i>最新</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/vip"><i class="fas fa-crown me-1"></i>VIP</a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" action="/search" method="GET">
                    <div class="input-group">
                        <input class="form-control bg-dark text-light border-secondary" type="search" name="q" 
                               placeholder="搜索..." value="<?php echo htmlspecialchars($_GET['q'] ?? ''); ?>">
                        <button class="btn btn-outline-warning" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($_SESSION['username']); ?>
                            <span class="badge bg-warning text-dark ms-1"><?php echo $_SESSION['group_name'] ?? '会员'; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end bg-dark border-secondary">
                            <li><a class="dropdown-item text-light" href="/profile"><i class="fas fa-user-cog me-2"></i>个人中心</a></li>
                            <li><a class="dropdown-item text-light" href="/favorites"><i class="fas fa-heart me-2"></i>我的收藏</a></li>
                            <li><a class="dropdown-item text-light" href="/downloads"><i class="fas fa-download me-2"></i>下载记录</a></li>
                            <li><a class="dropdown-item text-light" href="/vip"><i class="fas fa-crown me-2"></i>VIP升级</a></li>
                            <li><a class="dropdown-item text-light" href="/recharge"><i class="fas fa-credit-card me-2"></i>充值中心</a></li>
                            <li><a class="dropdown-item text-warning" href="/invite-earnings"><i class="fas fa-hand-holding-usd me-2"></i>邀请分钱</a></li>
                            <li><a class="dropdown-item text-light" href="/support"><i class="fas fa-headset me-2"></i>客服工单</a></li>
                            <li><hr class="dropdown-divider border-secondary"></li>
                            <li><a class="dropdown-item text-danger" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/login"><i class="fas fa-sign-in-alt me-1"></i>登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-warning" href="/register"><i class="fas fa-user-plus me-1"></i>注册</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid py-4" style="visibility: visible !important; opacity: 1 !important; display: block !important;">
        <div style="visibility: visible !important; opacity: 1 !important; display: block !important;">
            <?php echo $content; ?>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-black text-light py-4 mt-5 border-top border-secondary">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-warning"><?php echo Config::getSiteName(); ?> ( Liapian.com )</h5>
                    <p class="text-muted"><?php echo Config::getSiteDescription(); ?>，为您提供高质量的美女图片和视频内容。</p>
                </div>
                <div class="col-md-3">
                    <h6>快捷链接</h6>
                    <ul class="list-unstyled">
                        <li><a href="/about" class="text-muted text-decoration-none">关于我们</a></li>
                        <li><a href="/contact" class="text-muted text-decoration-none">联系我们</a></li>
                        <li><a href="/privacy" class="text-muted text-decoration-none">隐私政策</a></li>
                        <li><a href="/terms" class="text-muted text-decoration-none">服务条款</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>会员等级</h6>
                    <ul class="list-unstyled text-muted">
                        <li>天费会员 - 每日无限浏览</li>
                        <li>月费会员 - 月度无限浏览</li>
                        <li>永久会员 - 永久无限浏览+下载</li>
                    </ul>
                </div>
            </div>

            <!-- 友情链接 -->
            <?php
            require_once __DIR__ . '/../models/FriendlyLink.php';
            $friendlyLinkModel = new FriendlyLink();
            $friendlyLinks = $friendlyLinkModel->getActiveLinks();
            if (!empty($friendlyLinks)):
            ?>
            <hr class="border-secondary">
            <div class="row">
                <div class="col-12">
                    <h6 class="text-warning mb-3">友情链接</h6>
                    <div class="d-flex flex-wrap gap-3">
                        <?php foreach ($friendlyLinks as $link): ?>
                        <a href="<?php echo htmlspecialchars($link['url']); ?>"
                           target="_blank"
                           class="text-muted text-decoration-none"
                           title="<?php echo htmlspecialchars($link['description'] ?: $link['name']); ?>">
                            <?php if ($link['logo']): ?>
                            <img src="<?php echo htmlspecialchars($link['logo']); ?>"
                                 alt="<?php echo htmlspecialchars($link['name']); ?>"
                                 style="width: 16px; height: 16px; margin-right: 5px;">
                            <?php endif; ?>
                            <?php echo htmlspecialchars($link['name']); ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <hr class="border-secondary">
            <div class="row">
                <div class="col-12 text-center text-muted">
                    <p>&copy; 2025 <?php echo Config::getSiteName(); ?>. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript文件 (使用本地文件，加载更快) -->
    <script src="/assets/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery.min.js"></script>
    <script src="/assets/js/app.js"></script>
    <script src="/assets/js/interactions.js"></script>
    
    <!-- 页面特定JS -->
    <?php if (isset($pageScript)): ?>
    <script><?php echo $pageScript; ?></script>
    <?php endif; ?>
    <script defer src="https://stats.rosehacker.com/script.js" data-website-id="150050a8-a144-41c0-aad7-68ba4826bd95"></script>
</body>
</html>
