-- 美女套图+视频网站数据库表结构
-- 数据库: www_liapian_com
-- 前缀: lp_

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- 管理员表
-- ----------------------------
DROP TABLE IF EXISTS `lp_admin_users`;
CREATE TABLE `lp_admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role` varchar(20) NOT NULL DEFAULT 'admin' COMMENT '角色',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 用户组表
-- ----------------------------
DROP TABLE IF EXISTS `lp_user_groups`;
CREATE TABLE `lp_user_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '用户组名称',
  `slug` varchar(50) NOT NULL COMMENT '用户组标识',
  `description` text COMMENT '描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格（积分）',
  `days` int(11) NOT NULL DEFAULT '0' COMMENT '有效天数 0为永久',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 用户组权限表
-- ----------------------------
DROP TABLE IF EXISTS `lp_user_permissions`;
CREATE TABLE `lp_user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_id` int(11) NOT NULL COMMENT '用户组ID',
  `permission_key` varchar(100) NOT NULL COMMENT '权限键',
  `permission_value` text COMMENT '权限值',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `group_id` (`group_id`),
  KEY `permission_key` (`permission_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组权限表';

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `lp_users`;
CREATE TABLE `lp_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `salt` varchar(32) NOT NULL COMMENT '密码盐',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `group_id` int(11) NOT NULL DEFAULT '1' COMMENT '用户组ID',
  `group_expire_time` datetime DEFAULT NULL COMMENT '用户组到期时间',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '积分',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '已浏览图片数量',
  `max_view_count` int(11) NOT NULL DEFAULT '10' COMMENT '最大浏览图片数量',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '邀请码',
  `inviter_id` int(11) DEFAULT NULL COMMENT '邀请人ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `group_id` (`group_id`),
  KEY `inviter_id` (`inviter_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 分类表
-- ----------------------------
DROP TABLE IF EXISTS `lp_categories`;
CREATE TABLE `lp_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `slug` varchar(100) NOT NULL COMMENT '分类标识',
  `description` text COMMENT '分类描述',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT '父分类ID',
  `type` varchar(20) NOT NULL DEFAULT 'album' COMMENT '分类类型 album套图 video视频',
  `cover` varchar(255) DEFAULT NULL COMMENT '分类封面',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `parent_id` (`parent_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- ----------------------------
-- 套图表
-- ----------------------------
DROP TABLE IF EXISTS `lp_albums`;
CREATE TABLE `lp_albums` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '标题',
  `slug` varchar(255) DEFAULT NULL COMMENT 'URL标识',
  `description` text COMMENT '描述',
  `content` longtext COMMENT '内容',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费 1免费 0收费',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int(11) NOT NULL DEFAULT '0' COMMENT '收藏数',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载数',
  `image_count` int(11) NOT NULL DEFAULT '0' COMMENT '图片数量',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1已发布 0草稿 -1下架',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `admin_id` int(11) DEFAULT NULL COMMENT '发布管理员ID',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`),
  KEY `is_free` (`is_free`),
  KEY `published_at` (`published_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套图表';

-- ----------------------------
-- 套图图片表
-- ----------------------------
DROP TABLE IF EXISTS `lp_album_images`;
CREATE TABLE `lp_album_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `album_id` int(11) NOT NULL COMMENT '套图ID',
  `filename` varchar(255) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `file_size` bigint(20) NOT NULL DEFAULT '0' COMMENT '文件大小',
  `width` int(11) NOT NULL DEFAULT '0' COMMENT '图片宽度',
  `height` int(11) NOT NULL DEFAULT '0' COMMENT '图片高度',
  `format` varchar(10) DEFAULT NULL COMMENT '图片格式',
  `is_webp` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为WebP格式',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `album_id` (`album_id`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套图图片表';

-- ----------------------------
-- 视频表
-- ----------------------------
DROP TABLE IF EXISTS `lp_videos`;
CREATE TABLE `lp_videos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '标题',
  `slug` varchar(255) DEFAULT NULL COMMENT 'URL标识',
  `description` text COMMENT '描述',
  `content` longtext COMMENT '内容',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图',
  `video_url` varchar(500) NOT NULL COMMENT '视频URL',
  `duration` int(11) NOT NULL DEFAULT '0' COMMENT '视频时长(秒)',
  `file_size` bigint(20) NOT NULL DEFAULT '0' COMMENT '文件大小',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费 1免费 0收费',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) NOT NULL DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int(11) NOT NULL DEFAULT '0' COMMENT '收藏数',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载数',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1已发布 0草稿 -1下架',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `admin_id` int(11) DEFAULT NULL COMMENT '发布管理员ID',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `category_id` (`category_id`),
  KEY `status` (`status`),
  KEY `is_free` (`is_free`),
  KEY `published_at` (`published_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频表';

-- ----------------------------
-- 充值卡表
-- ----------------------------
DROP TABLE IF EXISTS `lp_recharge_cards`;
CREATE TABLE `lp_recharge_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_number` varchar(50) NOT NULL COMMENT '卡号',
  `card_password` varchar(50) NOT NULL COMMENT '卡密',
  `points` int(11) NOT NULL DEFAULT '0' COMMENT '充值积分',
  `group_id` int(11) DEFAULT NULL COMMENT '用户组ID',
  `group_days` int(11) DEFAULT NULL COMMENT '用户组天数',
  `type` varchar(20) NOT NULL DEFAULT 'points' COMMENT '充值类型 points积分 group用户组',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1未使用 0已使用 -1已禁用',
  `used_user_id` int(11) DEFAULT NULL COMMENT '使用用户ID',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `admin_id` int(11) DEFAULT NULL COMMENT '生成管理员ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `status` (`status`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值卡表';

-- ----------------------------
-- 积分记录表
-- ----------------------------
DROP TABLE IF EXISTS `lp_points_log`;
CREATE TABLE `lp_points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '类型 recharge充值 consume消费 reward奖励',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `points` int(11) NOT NULL COMMENT '积分变动',
  `balance` int(11) NOT NULL COMMENT '变动后余额',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` int(11) DEFAULT NULL COMMENT '相关ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- ----------------------------
-- 收藏表
-- ----------------------------
DROP TABLE IF EXISTS `lp_favorites`;
CREATE TABLE `lp_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `target_type` varchar(20) NOT NULL COMMENT '收藏类型 album套图 video视频',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_target` (`user_id`,`target_type`,`target_id`),
  KEY `target_type` (`target_type`,`target_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';

-- ----------------------------
-- 浏览记录表
-- ----------------------------
DROP TABLE IF EXISTS `lp_view_logs`;
CREATE TABLE `lp_view_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `target_type` varchar(20) NOT NULL COMMENT '类型 album套图 video视频 image图片',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `target_type` (`target_type`,`target_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览记录表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `lp_system_config`;
CREATE TABLE `lp_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` longtext COMMENT '配置值',
  `type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型',
  `group` varchar(50) NOT NULL DEFAULT 'system' COMMENT '配置分组',
  `title` varchar(100) DEFAULT NULL COMMENT '配置标题',
  `description` text COMMENT '配置描述',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `group` (`group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 采集规则表
-- ----------------------------
DROP TABLE IF EXISTS `lp_collection_rules`;
CREATE TABLE `lp_collection_rules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '规则名称',
  `source_url` varchar(500) NOT NULL COMMENT '源站URL',
  `list_selector` varchar(255) NOT NULL COMMENT '列表选择器',
  `title_selector` varchar(255) NOT NULL COMMENT '标题选择器',
  `content_selector` varchar(255) DEFAULT NULL COMMENT '内容选择器',
  `image_selector` varchar(255) NOT NULL COMMENT '图片选择器',
  `category_id` int(11) NOT NULL COMMENT '默认分类ID',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '默认是否免费',
  `enable_webp` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否转换WebP',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `last_run_time` datetime DEFAULT NULL COMMENT '最后执行时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集规则表';

-- ----------------------------
-- 火车头发布记录表
-- ----------------------------
DROP TABLE IF EXISTS `lp_train_publish_log`;
CREATE TABLE `lp_train_publish_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(20) NOT NULL COMMENT '发布类型 album套图 video视频',
  `target_id` int(11) NOT NULL COMMENT '目标ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1成功 0失败',
  `error_msg` text COMMENT '错误信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `type` (`type`,`target_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='火车头发布记录表';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- 插入默认管理员账号 admin/admin123
INSERT INTO `lp_admin_users` (`username`, `password`, `email`, `real_name`, `role`, `status`) VALUES
('admin', '$2y$10$WDDP6H96xzj/Qr33RY2G9uRiNzEj/FLO1SKSrBy4yqHd2FDhoN9Cy', '<EMAIL>', '超级管理员', 'super_admin', 1);

-- 插入默认用户组
INSERT INTO `lp_user_groups` (`name`, `slug`, `description`, `price`, `days`, `sort`) VALUES
('游客', 'guest', '未注册用户', 0, 0, 0),
('注册会员', 'member', '注册用户', 0, 0, 1),
('天费会员', 'day_vip', '1天全站图片浏览权限', 100, 1, 2),
('周费会员', 'week_vip', '7天全站图片浏览权限', 500, 7, 3),
('月费会员', 'month_vip', '30天全站图片浏览权限', 1500, 30, 4),
('年费会员', 'year_vip', '365天全站图片浏览权限', 15000, 365, 5),
('三年会员', 'three_year_vip', '3年全站图片+视频+批量下载权限', 40000, 1095, 6),
('永久会员', 'lifetime_vip', '永久全站图片+视频+批量下载权限', 100000, 0, 7);

-- 插入用户组权限
INSERT INTO `lp_user_permissions` (`group_id`, `permission_key`, `permission_value`) VALUES
(1, 'view_image_count', '3'),
(1, 'view_video', '0'),
(1, 'download', '0'),
(2, 'view_image_count', '10'),
(2, 'view_video', '0'),
(2, 'download', '0'),
(3, 'view_image_count', '-1'),
(3, 'view_video', '0'),
(3, 'download', '0'),
(4, 'view_image_count', '-1'),
(4, 'view_video', '0'),
(4, 'download', '0'),
(5, 'view_image_count', '-1'),
(5, 'view_video', '0'),
(5, 'download', '0'),
(6, 'view_image_count', '-1'),
(6, 'view_video', '0'),
(6, 'download', '0'),
(7, 'view_image_count', '-1'),
(7, 'view_video', '1'),
(7, 'download', '1'),
(8, 'view_image_count', '-1'),
(8, 'view_video', '1'),
(8, 'download', '1');

-- 插入默认分类
INSERT INTO `lp_categories` (`name`, `slug`, `description`, `type`, `sort`) VALUES
('美女套图', 'beauty-albums', '精美美女套图', 'album', 1),
('性感写真', 'sexy-photos', '性感美女写真', 'album', 2),
('清纯美女', 'pure-beauty', '清纯美女图片', 'album', 3),
('美女视频', 'beauty-videos', '精美美女视频', 'video', 1),
('写真视频', 'photo-videos', '美女写真视频', 'video', 2);

-- 插入系统配置
INSERT INTO `lp_system_config` (`key`, `value`, `type`, `group`, `title`, `description`) VALUES
('site_name', '丽片网', 'string', 'basic', '网站名称', '网站名称设置'),
('site_description', '专业的美女套图视频网站', 'string', 'basic', '网站描述', '网站描述设置'),
('site_keywords', '美女,套图,视频,写真', 'string', 'basic', '网站关键词', '网站关键词设置'),
('register_points', '100', 'number', 'points', '注册奖励积分', '用户注册获得的积分'),
('invite_points', '50', 'number', 'points', '邀请奖励积分', '邀请用户注册获得的积分'),
('guest_view_count', '3', 'number', 'view', '游客浏览数量', '游客允许浏览的图片数量'),
('member_view_count', '10', 'number', 'view', '会员浏览数量', '注册会员允许浏览的图片数量'),
('ftp_host', '', 'string', 'ftp', 'FTP主机', 'FTP服务器地址'),
('ftp_port', '21', 'number', 'ftp', 'FTP端口', 'FTP服务器端口'),
('ftp_username', '', 'string', 'ftp', 'FTP用户名', 'FTP登录用户名'),
('ftp_password', '', 'string', 'ftp', 'FTP密码', 'FTP登录密码'),
('ftp_path', '/', 'string', 'ftp', 'FTP路径', 'FTP上传路径'),
('cdn_url', '', 'string', 'cdn', 'CDN地址', 'CDN访问地址'),
('enable_webp_convert', '1', 'boolean', 'upload', '启用WebP转换', '是否启用WebP格式转换'),
('image_quality', '85', 'number', 'image', '图片质量', '图片压缩质量（1-100）');

SET FOREIGN_KEY_CHECKS=1;
