-- 创建用户点赞表
CREATE TABLE IF NOT EXISTS `lp_user_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型 album套图 video视频',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`,`content_type`,`content_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';
