-- 创建评论表
CREATE TABLE IF NOT EXISTS `lp_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型 album套图 video视频',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `content` text NOT NULL COMMENT '评论内容',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0待审核 1已通过 2已拒绝',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';
