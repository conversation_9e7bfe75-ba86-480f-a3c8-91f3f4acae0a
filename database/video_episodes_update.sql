-- 视频分集功能数据库更新脚本

-- 1. 为视频表添加分集相关字段
ALTER TABLE `lp_videos` 
ADD COLUMN `is_series` tinyint(1) DEFAULT 0 COMMENT '是否为系列视频' AFTER `video_height`,
ADD COLUMN `total_episodes` int(11) DEFAULT 1 COMMENT '总集数' AFTER `is_series`,
ADD COLUMN `slug` varchar(100) DEFAULT NULL COMMENT 'URL别名' AFTER `title`,
ADD INDEX `idx_slug` (`slug`),
ADD INDEX `idx_is_series` (`is_series`);

-- 2. 创建视频分集表
CREATE TABLE `lp_video_episodes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) NOT NULL COMMENT '视频ID',
  `episode_number` int(11) NOT NULL COMMENT '集数',
  `title` varchar(200) NOT NULL COMMENT '分集标题',
  `description` text COMMENT '分集描述',
  `video_path` varchar(255) NOT NULL COMMENT '视频文件路径',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '分集封面',
  `video_size` bigint(20) DEFAULT 0 COMMENT '视频大小(字节)',
  `duration` int(11) DEFAULT 0 COMMENT '视频时长(秒)',
  `video_width` int(11) DEFAULT 0 COMMENT '视频宽度',
  `video_height` int(11) DEFAULT 0 COMMENT '视频高度',
  `view_count` int(11) DEFAULT 0 COMMENT '播放次数',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1发布 0下架',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_episode_number` (`episode_number`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  UNIQUE KEY `unique_video_episode` (`video_id`, `episode_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频分集表';

-- 3. 为现有视频生成slug
UPDATE `lp_videos` SET `slug` = CONCAT('video-', `id`) WHERE `slug` IS NULL OR `slug` = '';

-- 4. 为现有单集视频创建默认分集记录
INSERT INTO `lp_video_episodes` (`video_id`, `episode_number`, `title`, `description`, `video_path`, `cover_image`, `video_size`, `duration`, `video_width`, `video_height`, `view_count`, `sort_order`, `status`)
SELECT 
    `id` as `video_id`,
    1 as `episode_number`,
    `title` as `title`,
    `description` as `description`,
    `video_path` as `video_path`,
    `cover_image` as `cover_image`,
    `video_size` as `video_size`,
    `duration` as `duration`,
    `video_width` as `video_width`,
    `video_height` as `video_height`,
    `view_count` as `view_count`,
    1 as `sort_order`,
    `status` as `status`
FROM `lp_videos` 
WHERE `id` NOT IN (SELECT DISTINCT `video_id` FROM `lp_video_episodes`);

-- 5. 更新视频表的总集数
UPDATE `lp_videos` v 
SET `total_episodes` = (
    SELECT COUNT(*) 
    FROM `lp_video_episodes` e 
    WHERE e.video_id = v.id AND e.status = 1
)
WHERE v.id > 0;

-- 6. 创建视频播放记录表（用于记录用户观看进度）
CREATE TABLE `lp_video_watch_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `video_id` int(11) NOT NULL COMMENT '视频ID',
  `episode_id` int(11) NOT NULL COMMENT '分集ID',
  `watch_progress` int(11) DEFAULT 0 COMMENT '观看进度(秒)',
  `watch_duration` int(11) DEFAULT 0 COMMENT '观看时长(秒)',
  `is_completed` tinyint(1) DEFAULT 0 COMMENT '是否看完',
  `last_watch_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后观看时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_episode` (`user_id`, `episode_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_episode_id` (`episode_id`),
  KEY `idx_last_watch_time` (`last_watch_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频观看记录表';

-- 7. 更新分类表，添加type字段区分套图和视频分类
ALTER TABLE `lp_categories` 
ADD COLUMN `type` varchar(10) DEFAULT 'album' COMMENT '分类类型 album套图 video视频' AFTER `slug`,
ADD INDEX `idx_type` (`type`);

-- 8. 为现有分类设置默认类型
UPDATE `lp_categories` SET `type` = 'album' WHERE `type` IS NULL OR `type` = '';

-- 9. 插入一些默认的视频分类
INSERT INTO `lp_categories` (`name`, `slug`, `type`, `parent_id`, `description`, `sort_order`) VALUES
('短视频', 'short-videos', 'video', 0, '精彩短视频合集', 1),
('系列剧', 'series', 'video', 0, '连续剧集视频', 2),
('教程视频', 'tutorials', 'video', 0, '各类教程视频', 3),
('娱乐视频', 'entertainment', 'video', 0, '娱乐搞笑视频', 4);
