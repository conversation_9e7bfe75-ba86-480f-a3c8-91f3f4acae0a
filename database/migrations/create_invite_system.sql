-- 邀请分成系统数据库迁移

-- 1. 新增系统配置项
INSERT INTO `lp_system_config` (`key`, `value`, `type`, `group`, `title`, `description`, `sort`) VALUES
('invite_recharge_rate', '30', 'number', 'points', '充值奖励分成比例', '邀请用户充值时的分成百分比(1-100)', 10),
('withdraw_threshold', '1000', 'number', 'points', '提现门槛积分', '用户申请提现的最低积分要求', 11),
('withdraw_enabled', '1', 'boolean', 'points', '启用提现功能', '是否允许用户申请提现', 12),
('points_to_money_rate', '100', 'number', 'points', '积分兑换比例', '多少积分兑换1元人民币', 13),
('invite_level_enabled', '1', 'boolean', 'points', '启用多级邀请', '是否启用二级邀请奖励', 14),
('invite_level2_rate', '10', 'number', 'points', '二级邀请分成比例', '二级邀请人获得的分成百分比', 15),
('max_withdraw_daily', '10000', 'number', 'points', '每日最大提现积分', '单个用户每日最大提现积分数量', 16),
('withdraw_fee_rate', '0', 'number', 'points', '提现手续费比例', '提现手续费百分比(0-100)', 17);

-- 2. 提现申请表
CREATE TABLE IF NOT EXISTS `lp_withdraw_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points_amount` int(11) NOT NULL COMMENT '提现积分数量',
  `money_amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '手续费金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `account_type` enum('alipay','wechat','bank') NOT NULL COMMENT '收款方式',
  `account_info` text NOT NULL COMMENT '收款账号信息(JSON)',
  `status` enum('pending','processing','approved','rejected','completed') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `admin_note` text DEFAULT NULL COMMENT '管理员备注',
  `reject_reason` text DEFAULT NULL COMMENT '驳回原因',
  `processed_by` int(11) DEFAULT NULL COMMENT '处理管理员ID',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_processed_by` (`processed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 3. 邀请收益记录表
CREATE TABLE IF NOT EXISTS `lp_invite_earnings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `inviter_id` int(11) NOT NULL COMMENT '邀请人ID',
  `invitee_id` int(11) NOT NULL COMMENT '被邀请人ID',
  `type` enum('register','recharge','level2_register','level2_recharge') NOT NULL COMMENT '收益类型',
  `points_earned` int(11) NOT NULL COMMENT '获得积分',
  `recharge_amount` decimal(10,2) DEFAULT NULL COMMENT '充值金额(仅充值类型)',
  `commission_rate` decimal(5,2) DEFAULT NULL COMMENT '分成比例(仅充值类型)',
  `order_id` int(11) DEFAULT NULL COMMENT '关联订单ID(仅充值类型)',
  `level` tinyint(1) NOT NULL DEFAULT 1 COMMENT '邀请层级(1=直接邀请,2=二级邀请)',
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'completed' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_inviter_id` (`inviter_id`),
  KEY `idx_invitee_id` (`invitee_id`),
  KEY `idx_type` (`type`),
  KEY `idx_level` (`level`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请收益记录表';

-- 4. 邀请活动表
CREATE TABLE IF NOT EXISTS `lp_invite_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '活动标题',
  `description` text DEFAULT NULL COMMENT '活动描述',
  `type` enum('register_bonus','recharge_bonus','ranking_reward') NOT NULL COMMENT '活动类型',
  `bonus_multiplier` decimal(3,2) NOT NULL DEFAULT 1.00 COMMENT '奖励倍数',
  `min_recharge` decimal(10,2) DEFAULT NULL COMMENT '最低充值金额要求',
  `max_reward` int(11) DEFAULT NULL COMMENT '单用户最大奖励积分',
  `total_budget` int(11) DEFAULT NULL COMMENT '活动总预算积分',
  `used_budget` int(11) NOT NULL DEFAULT 0 COMMENT '已使用预算',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `end_time` timestamp NOT NULL COMMENT '结束时间',
  `status` enum('draft','active','paused','ended') NOT NULL DEFAULT 'draft' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请活动表';

-- 5. 用户收款账号表
CREATE TABLE IF NOT EXISTS `lp_user_payment_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `account_type` enum('alipay','wechat','bank') NOT NULL COMMENT '账号类型',
  `account_name` varchar(100) NOT NULL COMMENT '账户名称',
  `account_number` varchar(100) NOT NULL COMMENT '账号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '银行名称(仅银行卡)',
  `branch_name` varchar(200) DEFAULT NULL COMMENT '开户行(仅银行卡)',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认账号',
  `status` enum('active','disabled') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_account_type` (`account_type`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收款账号表';

-- 6. 风控记录表
CREATE TABLE IF NOT EXISTS `lp_invite_risk_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `risk_type` enum('frequent_register','same_ip','same_device','abnormal_recharge','batch_withdraw') NOT NULL COMMENT '风险类型',
  `risk_level` enum('low','medium','high','critical') NOT NULL COMMENT '风险等级',
  `description` text NOT NULL COMMENT '风险描述',
  `related_data` json DEFAULT NULL COMMENT '相关数据',
  `status` enum('pending','reviewed','ignored','blocked') NOT NULL DEFAULT 'pending' COMMENT '处理状态',
  `reviewed_by` int(11) DEFAULT NULL COMMENT '审核人员ID',
  `reviewed_at` timestamp NULL DEFAULT NULL COMMENT '审核时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_risk_type` (`risk_type`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邀请风控记录表';

-- 7. 积分流水表扩展（如果不存在则创建）
CREATE TABLE IF NOT EXISTS `lp_points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` enum('earn','spend','withdraw','refund','admin_adjust') NOT NULL COMMENT '类型',
  `source` varchar(50) NOT NULL COMMENT '来源',
  `amount` int(11) NOT NULL COMMENT '积分数量(正数为获得,负数为消费)',
  `balance_before` int(11) NOT NULL COMMENT '操作前余额',
  `balance_after` int(11) NOT NULL COMMENT '操作后余额',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `related_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_related_id` (`related_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分流水记录表';
