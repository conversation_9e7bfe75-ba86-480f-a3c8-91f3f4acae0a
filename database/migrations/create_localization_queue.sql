-- 本地化队列表
CREATE TABLE IF NOT EXISTS `lp_localization_queue` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `album_id` int(11) NOT NULL COMMENT '套图ID',
  `image_id` int(11) NOT NULL COMMENT '图片ID',
  `image_url` text NOT NULL COMMENT '原始图片URL',
  `priority` tinyint(4) NOT NULL DEFAULT 5 COMMENT '优先级(1-10，数字越小优先级越高)',
  `status` enum('pending','processing','completed','failed','retry') NOT NULL DEFAULT 'pending' COMMENT '状态',
  `attempts` tinyint(4) NOT NULL DEFAULT 0 COMMENT '尝试次数',
  `max_attempts` tinyint(4) NOT NULL DEFAULT 3 COMMENT '最大尝试次数',
  `local_file_url` varchar(500) DEFAULT NULL COMMENT '本地化后的文件URL',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小(字节)',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始处理时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_album_id` (`album_id`),
  KEY `idx_priority_status` (`priority`, `status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地化队列表';

-- 本地化批次表
CREATE TABLE IF NOT EXISTS `lp_localization_batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_name` varchar(255) NOT NULL COMMENT '批次名称',
  `album_ids` text DEFAULT NULL COMMENT '套图ID列表(JSON)',
  `total_images` int(11) NOT NULL DEFAULT 0 COMMENT '总图片数',
  `completed_images` int(11) NOT NULL DEFAULT 0 COMMENT '已完成图片数',
  `failed_images` int(11) NOT NULL DEFAULT 0 COMMENT '失败图片数',
  `status` enum('pending','processing','completed','failed','paused') NOT NULL DEFAULT 'pending',
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='本地化批次表';

-- 工作进程状态表
CREATE TABLE IF NOT EXISTS `lp_localization_workers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `worker_name` varchar(100) NOT NULL COMMENT '工作进程名称',
  `pid` int(11) DEFAULT NULL COMMENT '进程ID',
  `status` enum('idle','working','stopped','error') NOT NULL DEFAULT 'idle',
  `current_task_id` int(11) DEFAULT NULL COMMENT '当前处理的任务ID',
  `processed_count` int(11) NOT NULL DEFAULT 0 COMMENT '已处理数量',
  `last_heartbeat` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后心跳时间',
  `started_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_worker_name` (`worker_name`),
  KEY `idx_status` (`status`),
  KEY `idx_last_heartbeat` (`last_heartbeat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作进程状态表';
