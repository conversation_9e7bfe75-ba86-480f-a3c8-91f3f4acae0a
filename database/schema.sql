-- 丽片网数据库结构
-- MySQL 5.7

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
CREATE TABLE `lp_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `user_group` varchar(20) DEFAULT 'member' COMMENT '用户组',
  `points` int(11) DEFAULT 0 COMMENT '积分',
  `total_viewed_images` int(11) DEFAULT 0 COMMENT '已浏览图片总数',
  `images_per_page` int(11) DEFAULT 1 COMMENT '每页显示图片数',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP过期时间',
  `invite_code` varchar(20) DEFAULT NULL COMMENT '邀请码',
  `invited_by` int(11) DEFAULT NULL COMMENT '邀请人ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1正常 0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `invite_code` (`invite_code`),
  KEY `idx_user_group` (`user_group`),
  KEY `idx_status` (`status`),
  KEY `idx_invited_by` (`invited_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户组表
-- ----------------------------
CREATE TABLE `lp_user_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group_key` varchar(20) NOT NULL COMMENT '用户组标识',
  `group_name` varchar(50) NOT NULL COMMENT '用户组名称',
  `free_image_count` int(11) DEFAULT 0 COMMENT '免费浏览图片数量 -1表示无限制',
  `can_view_video` tinyint(1) DEFAULT 0 COMMENT '是否可以观看视频',
  `can_download` tinyint(1) DEFAULT 0 COMMENT '是否可以下载',
  `default_images_per_page` int(11) DEFAULT 1 COMMENT '默认每页显示图片数',
  `max_images_per_page` int(11) DEFAULT 1 COMMENT '最大每页显示图片数 -1表示无限制',
  `duration_days` int(11) DEFAULT 0 COMMENT '有效期天数 -1表示永久',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group_key` (`group_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户组表';

-- ----------------------------
-- 分类表
-- ----------------------------
CREATE TABLE `lp_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `slug` varchar(50) NOT NULL COMMENT '分类别名',
  `parent_id` int(11) DEFAULT 0 COMMENT '父分类ID',
  `description` text COMMENT '分类描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- ----------------------------
-- 套图表
-- ----------------------------
CREATE TABLE `lp_albums` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `total_images` int(11) DEFAULT 0 COMMENT '图片总数',
  `is_paid` tinyint(1) DEFAULT 0 COMMENT '是否收费',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `favorite_count` int(11) DEFAULT 0 COMMENT '收藏次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1发布 0下架',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_paid` (`is_paid`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_view_count` (`view_count`),
  KEY `idx_favorite_count` (`favorite_count`),
  KEY `idx_like_count` (`like_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套图表';

-- ----------------------------
-- 套图图片表
-- ----------------------------
CREATE TABLE `lp_album_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `album_id` int(11) NOT NULL COMMENT '套图ID',
  `image_path` varchar(255) NOT NULL COMMENT '图片路径',
  `image_size` int(11) DEFAULT 0 COMMENT '图片大小(字节)',
  `image_width` int(11) DEFAULT 0 COMMENT '图片宽度',
  `image_height` int(11) DEFAULT 0 COMMENT '图片高度',
  `is_webp` tinyint(1) DEFAULT 0 COMMENT '是否WebP格式',
  `original_format` varchar(10) DEFAULT NULL COMMENT '原始格式',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_album_id` (`album_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套图图片表';

-- ----------------------------
-- 视频表
-- ----------------------------
CREATE TABLE `lp_videos` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `video_path` varchar(255) NOT NULL COMMENT '视频路径',
  `video_size` bigint(20) DEFAULT 0 COMMENT '视频大小(字节)',
  `duration` int(11) DEFAULT 0 COMMENT '视频时长(秒)',
  `video_width` int(11) DEFAULT 0 COMMENT '视频宽度',
  `video_height` int(11) DEFAULT 0 COMMENT '视频高度',
  `is_paid` tinyint(1) DEFAULT 0 COMMENT '是否收费',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签',
  `view_count` int(11) DEFAULT 0 COMMENT '播放次数',
  `favorite_count` int(11) DEFAULT 0 COMMENT '收藏次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 1发布 0下架',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_paid` (`is_paid`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_view_count` (`view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频表';

-- ----------------------------
-- 充值卡表
-- ----------------------------
CREATE TABLE `lp_recharge_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_number` varchar(20) NOT NULL COMMENT '卡号',
  `card_password` varchar(20) NOT NULL COMMENT '卡密',
  `points` int(11) NOT NULL COMMENT '积分数量',
  `card_type` varchar(20) DEFAULT 'points' COMMENT '卡类型 points积分 vip会员',
  `vip_days` int(11) DEFAULT 0 COMMENT 'VIP天数',
  `user_group` varchar(20) DEFAULT NULL COMMENT '开通的用户组',
  `is_used` tinyint(1) DEFAULT 0 COMMENT '是否已使用',
  `used_by` int(11) DEFAULT NULL COMMENT '使用者ID',
  `used_at` datetime DEFAULT NULL COMMENT '使用时间',
  `created_by` int(11) DEFAULT NULL COMMENT '生成者ID',
  `batch_id` varchar(50) DEFAULT NULL COMMENT '批次ID',
  `expire_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `idx_is_used` (`is_used`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_expire_at` (`expire_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值卡表';

-- ----------------------------
-- 积分记录表
-- ----------------------------
CREATE TABLE `lp_points_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `points` int(11) NOT NULL COMMENT '积分变动数量',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `related_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `before_points` int(11) DEFAULT 0 COMMENT '变动前积分',
  `after_points` int(11) DEFAULT 0 COMMENT '变动后积分',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- ----------------------------
-- 收藏表
-- ----------------------------
CREATE TABLE `lp_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型 album套图 video视频',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`,`content_type`,`content_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏表';

-- ----------------------------
-- 浏览记录表
-- ----------------------------
CREATE TABLE `lp_view_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `viewed_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`),
  KEY `idx_viewed_at` (`viewed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览记录表';

-- ----------------------------
-- 管理员表
-- ----------------------------
CREATE TABLE `lp_admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `role` varchar(20) DEFAULT 'admin' COMMENT '角色',
  `permissions` text COMMENT '权限列表JSON',
  `last_login_time` datetime DEFAULT NULL,
  `last_login_ip` varchar(45) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
CREATE TABLE `lp_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_group` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `is_json` tinyint(1) DEFAULT 0 COMMENT '是否JSON格式',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 采集配置表
-- ----------------------------
CREATE TABLE `lp_spider_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '采集站点名称',
  `target_url` varchar(255) NOT NULL COMMENT '目标网站URL',
  `list_rule` text COMMENT '列表页采集规则JSON',
  `detail_rule` text COMMENT '详情页采集规则JSON',
  `category_id` int(11) DEFAULT NULL COMMENT '默认分类ID',
  `auto_webp` tinyint(1) DEFAULT 1 COMMENT '是否自动转WebP',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态',
  `last_run_time` datetime DEFAULT NULL COMMENT '最后运行时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采集配置表';

-- ----------------------------
-- 评论表
-- ----------------------------
CREATE TABLE `lp_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型 album套图 video视频',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `content` text NOT NULL COMMENT '评论内容',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态 0待审核 1已通过 2已拒绝',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- ----------------------------
-- 用户点赞表
-- ----------------------------
CREATE TABLE `lp_user_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content_type` varchar(10) NOT NULL COMMENT '内容类型 album套图 video视频',
  `content_id` int(11) NOT NULL COMMENT '内容ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_content` (`user_id`,`content_type`,`content_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_content` (`content_type`,`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- ----------------------------
-- 插入默认数据
-- ----------------------------

-- 默认用户组
INSERT INTO `lp_user_groups` (`group_key`, `group_name`, `free_image_count`, `can_view_video`, `can_download`, `default_images_per_page`, `max_images_per_page`, `duration_days`, `sort_order`) VALUES
('guest', '游客', 3, 0, 0, 1, 1, 0, 1),
('member', '注册会员', 10, 0, 0, 1, 1, 0, 2),
('vip_day', '天费会员', -1, 0, 0, 3, 10, 1, 3),
('vip_week', '周费会员', -1, 0, 0, 3, 10, 7, 4),
('vip_month', '月费会员', -1, 0, 0, 5, 20, 30, 5),
('vip_year', '年费会员', -1, 0, 0, 5, 20, 365, 6),
('vip_3year', '三年会员', -1, 1, 1, 10, 50, 1095, 7),
('vip_forever', '永久会员', -1, 1, 1, 20, -1, -1, 8);

-- 默认分类
INSERT INTO `lp_categories` (`name`, `slug`, `parent_id`, `description`, `sort_order`) VALUES
('写真套图', 'photo-sets', 0, '精美写真套图', 1),
('视频作品', 'videos', 0, '高清视频作品', 2),
('古风写真', 'ancient-style', 1, '古风主题写真', 1),
('时尚写真', 'fashion', 1, '时尚潮流写真', 2),
('艺术写真', 'artistic', 1, '艺术主题写真', 3);

-- 默认管理员（密码：admin123）
INSERT INTO `lp_admin_users` (`username`, `password`, `nickname`, `role`) VALUES
('admin', '$2y$10$WDDP6H96xzj/Qr33RY2G9uRiNzEj/FLO1SKSrBy4yqHd2FDhoN9Cy', '超级管理员', 'super_admin');

-- 系统配置
INSERT INTO `lp_system_config` (`config_key`, `config_value`, `config_group`, `description`) VALUES
('site_name', '丽片网', 'basic', '网站名称'),
('site_description', '精美写真图片和视频分享平台', 'basic', '网站描述'),
('site_keywords', '写真,美女,图片,视频', 'basic', '网站关键词'),
('guest_free_images', '3', 'user', '游客免费浏览图片数'),
('member_free_images', '10', 'user', '注册会员免费浏览图片数'),
('register_points', '100', 'points', '注册奖励积分'),
('invite_points', '50', 'points', '邀请奖励积分'),
('enable_webp_convert', '1', 'upload', '是否开启WebP转换'),
('cdn_url_prefix', 'https://cdn1.liapian.com', 'cdn', 'CDN地址前缀');

SET FOREIGN_KEY_CHECKS = 1;
