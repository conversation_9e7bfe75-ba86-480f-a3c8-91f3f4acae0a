<?php
/**
 * 公共函数文件
 * 包含所有页面都需要的通用函数
 */

require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../core/Cache.php';

/**
 * 获取分类列表
 */
function getCategories($type = 'album') {
    try {
        $db = Database::getInstance();
        return $db->fetchAll(
            "SELECT * FROM {$db->getPrefix()}categories WHERE type = :type AND status = 1 ORDER BY sort ASC",
            ['type' => $type]
        );
    } catch (Exception $e) {
        error_log("获取分类失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取系统配置
 */
function getConfig($key, $default = '') {
    try {
        $db = Database::getInstance();
        $cache = Cache::getInstance();
        
        $cacheKey = 'config:' . $key;
        $value = $cache->get($cacheKey);
        
        if ($value === false) {
            $config = $db->fetch(
                "SELECT config_value FROM {$db->getPrefix()}system_config WHERE config_key = :key",
                ['key' => $key]
            );
            $value = $config ? $config['config_value'] : $default;
            $cache->set($cacheKey, $value, 3600);
        }
        
        return $value;
    } catch (Exception $e) {
        error_log("获取配置失败: " . $e->getMessage());
        return $default;
    }
}

/**
 * 获取当前用户信息
 */
function getCurrentUser() {
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    try {
        $db = Database::getInstance();
        return $db->fetch(
            "SELECT * FROM {$db->getPrefix()}users WHERE id = :id",
            ['id' => $_SESSION['user_id']]
        );
    } catch (Exception $e) {
        error_log("获取用户信息失败: " . $e->getMessage());
        return null;
    }
}

/**
 * 检查用户权限
 */
function checkUserPermission($requiredLevel = 0) {
    $user = getCurrentUser();
    if (!$user) {
        return false;
    }
    
    return $user['group_id'] >= $requiredLevel;
}

/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' B';
    }
}

/**
 * 格式化时间
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return '刚刚';
    } elseif ($time < 3600) {
        return floor($time / 60) . '分钟前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . '小时前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . '天前';
    } else {
        return date('Y-m-d', strtotime($datetime));
    }
}

/**
 * 安全输出HTML
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * 验证邮箱格式
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 验证手机号格式
 */
function isValidPhone($phone) {
    return preg_match('/^1[3-9]\d{9}$/', $phone);
}

/**
 * 获取客户端IP
 */
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

/**
 * 记录操作日志
 */
function logAction($action, $details = '', $userId = null) {
    try {
        $db = Database::getInstance();
        $db->insert('action_logs', [
            'user_id' => $userId ?: ($_SESSION['user_id'] ?? null),
            'action' => $action,
            'details' => $details,
            'ip_address' => getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log("记录日志失败: " . $e->getMessage());
    }
}

/**
 * 发送邮件（简单版本）
 */
function sendEmail($to, $subject, $message) {
    // 这里可以集成邮件发送服务
    // 暂时只记录日志
    error_log("邮件发送: TO={$to}, SUBJECT={$subject}");
    return true;
}

/**
 * 生成缩略图URL
 */
function getThumbnailUrl($imagePath, $width = 300, $height = 200) {
    if (empty($imagePath)) {
        return '/assets/images/placeholder.svg';
    }
    
    if (strpos($imagePath, 'http') === 0) {
        return $imagePath;
    }
    
    // 这里可以集成图片处理服务
    return $imagePath;
}

/**
 * 检查是否为移动设备
 */
function isMobile() {
    return preg_match('/Mobile|Android|iPhone|iPad/', $_SERVER['HTTP_USER_AGENT'] ?? '');
}

/**
 * 生成面包屑导航
 */
function generateBreadcrumb($items) {
    $html = '<nav aria-label="breadcrumb" class="mb-4">';
    $html .= '<ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        if ($index === count($items) - 1) {
            $html .= '<li class="breadcrumb-item active">' . e($item['title']) . '</li>';
        } else {
            $html .= '<li class="breadcrumb-item"><a href="' . e($item['url']) . '" class="text-warning">' . e($item['title']) . '</a></li>';
        }
    }
    
    $html .= '</ol>';
    $html .= '</nav>';
    
    return $html;
}

/**
 * 分页HTML生成
 */
function generatePagination($currentPage, $totalPages, $baseUrl, $params = []) {
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '<nav class="mt-5"><ul class="pagination justify-content-center">';
    
    // 上一页
    if ($currentPage > 1) {
        $prevUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage - 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . e($prevUrl) . '"><i class="fas fa-chevron-left"></i></a></li>';
    }
    
    // 页码
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $pageUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $i]));
        $active = $i === $currentPage ? 'active' : '';
        $html .= '<li class="page-item ' . $active . '"><a class="page-link" href="' . e($pageUrl) . '">' . $i . '</a></li>';
    }
    
    // 下一页
    if ($currentPage < $totalPages) {
        $nextUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage + 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . e($nextUrl) . '"><i class="fas fa-chevron-right"></i></a></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}
?>
