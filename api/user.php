<?php
header('Content-Type: application/json');
session_start();

require_once __DIR__ . '/../core/Database.php';

$action = $_GET['action'] ?? '';

// 检查登录状态的API端点（不需要登录验证）
if ($action === 'check-login') {
    echo json_encode([
        'logged_in' => isset($_SESSION['user_id']),
        'user_id' => $_SESSION['user_id'] ?? null
    ]);
    exit;
}

// 其他API需要登录验证
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$userModel = new User();
$db = Database::getInstance();
$userId = $_SESSION['user_id'];

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($action) {
        case 'update':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            updateProfile($userModel, $userId);
            break;
            
        case 'change-password':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            changePassword($userModel, $userId);
            break;
            
        case 'upload-avatar':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            uploadAvatar($userModel, $userId);
            break;
            
        case 'favorite':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            toggleFavorite($db, $userId);
            break;
            
        case 'unfavorite':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            removeFavorite($db, $userId);
            break;
            
        case 'clear-history':
            if ($method !== 'POST') {
                throw new Exception('Method not allowed');
            }
            clearHistory($db, $userId);
            break;
            
        case 'get-favorites':
            getFavorites($db, $userId);
            break;
            
        case 'get-history':
            getHistory($db, $userId);
            break;
            
        case 'get-points':
            getPoints($db, $userId);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 更新用户资料
 */
function updateProfile($userModel, $userId) {
    $email = trim($_POST['email'] ?? '');
    $nickname = trim($_POST['nickname'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    
    // 验证邮箱
    if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱格式不正确');
    }
    
    // 验证手机号
    if ($phone && !preg_match('/^1[3-9]\d{9}$/', $phone)) {
        throw new Exception('手机号格式不正确');
    }
    
    $updateData = [
        'email' => $email,
        'nickname' => $nickname,
        'phone' => $phone,
        'bio' => $bio,
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    if ($userModel->update($userId, $updateData)) {
        echo json_encode(['success' => true, 'message' => '资料更新成功']);
    } else {
        throw new Exception('更新失败');
    }
}

/**
 * 修改密码
 */
function changePassword($userModel, $userId) {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        throw new Exception('所有字段都是必填的');
    }
    
    if ($newPassword !== $confirmPassword) {
        throw new Exception('两次输入的密码不一致');
    }
    
    if (strlen($newPassword) < 6) {
        throw new Exception('密码长度不能少于6位');
    }
    
    // 验证当前密码
    $user = $userModel->getById($userId);
    if (!$user || !password_verify($currentPassword, $user['password'])) {
        throw new Exception('当前密码不正确');
    }
    
    // 更新密码
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    if ($userModel->update($userId, ['password' => $hashedPassword, 'updated_at' => date('Y-m-d H:i:s')])) {
        echo json_encode(['success' => true, 'message' => '密码修改成功']);
    } else {
        throw new Exception('密码修改失败');
    }
}

/**
 * 上传头像
 */
function uploadAvatar($userModel, $userId) {
    if (!isset($_FILES['avatar'])) {
        throw new Exception('请选择头像文件');
    }
    
    $file = $_FILES['avatar'];
    
    // 检查文件错误
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('文件上传失败');
    }
    
    // 检查文件大小（2MB）
    if ($file['size'] > 2 * 1024 * 1024) {
        throw new Exception('头像文件不能超过2MB');
    }
    
    // 检查文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('只支持 JPG、PNG、GIF、WebP 格式的图片');
    }
    
    $imageHandler = new ImageHandler();
    $avatarUrl = $imageHandler->uploadAvatar($file, $userId);
    
    if ($avatarUrl) {
        $userModel->update($userId, ['avatar' => $avatarUrl, 'updated_at' => date('Y-m-d H:i:s')]);
        echo json_encode(['success' => true, 'message' => '头像上传成功', 'avatar_url' => $avatarUrl]);
    } else {
        throw new Exception('头像上传失败');
    }
}

/**
 * 切换收藏状态
 */
function toggleFavorite($db, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $type = $input['type'] ?? '';
    $contentId = intval($input['id'] ?? 0);
    
    if (!in_array($type, ['album', 'video']) || $contentId <= 0) {
        throw new Exception('参数错误');
    }
    
    // 检查是否已收藏
    $exists = $db->fetchValue(
        "SELECT id FROM {$db->getPrefix()}favorites
         WHERE user_id = :user_id AND target_type = :type AND target_id = :content_id",
        ['user_id' => $userId, 'type' => $type, 'content_id' => $contentId]
    );

    if ($exists) {
        // 取消收藏
        $db->execute(
            "DELETE FROM {$db->getPrefix()}favorites
             WHERE user_id = :user_id AND target_type = :type AND target_id = :content_id",
            ['user_id' => $userId, 'type' => $type, 'content_id' => $contentId]
        );

        // 更新收藏数
        $table = $type === 'album' ? 'albums' : 'videos';
        $db->execute(
            "UPDATE {$db->getPrefix()}{$table} SET favorite_count = favorite_count - 1 WHERE id = :id",
            ['id' => $contentId]
        );

        echo json_encode(['success' => true, 'action' => 'unfavorited', 'message' => '已取消收藏']);
    } else {
        // 添加收藏
        $db->execute(
            "INSERT INTO {$db->getPrefix()}favorites (user_id, target_type, target_id, created_at)
             VALUES (:user_id, :type, :content_id, :created_at)",
            [
                'user_id' => $userId,
                'type' => $type,
                'content_id' => $contentId,
                'created_at' => date('Y-m-d H:i:s')
            ]
        );

        // 更新收藏数
        $table = $type === 'album' ? 'albums' : 'videos';
        $db->execute(
            "UPDATE {$db->getPrefix()}{$table} SET favorite_count = favorite_count + 1 WHERE id = :id",
            ['id' => $contentId]
        );

        echo json_encode(['success' => true, 'action' => 'favorited', 'message' => '已添加收藏']);
    }
}

/**
 * 取消收藏
 */
function removeFavorite($db, $userId) {
    $input = json_decode(file_get_contents('php://input'), true);
    $type = $input['type'] ?? '';
    $contentId = intval($input['id'] ?? 0);
    
    if (!in_array($type, ['album', 'video']) || $contentId <= 0) {
        throw new Exception('参数错误');
    }
    
    $db->execute(
        "DELETE FROM {$db->getPrefix()}favorites
         WHERE user_id = :user_id AND target_type = :type AND target_id = :content_id",
        ['user_id' => $userId, 'type' => $type, 'content_id' => $contentId]
    );
    
    // 更新收藏数
    $table = $type === 'album' ? 'albums' : 'videos';
    $db->execute(
        "UPDATE {$db->getPrefix()}{$table} SET favorite_count = favorite_count - 1 WHERE id = :id",
        ['id' => $contentId]
    );
    
    echo json_encode(['success' => true, 'message' => '已取消收藏']);
}

/**
 * 清空浏览历史
 */
function clearHistory($db, $userId) {
    $db->execute(
        "DELETE FROM {$db->getPrefix()}user_view_history WHERE user_id = :user_id",
        ['user_id' => $userId]
    );
    
    echo json_encode(['success' => true, 'message' => '浏览历史已清空']);
}

/**
 * 获取收藏列表
 */
function getFavorites($db, $userId) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $pageSize = 20;
    $offset = ($page - 1) * $pageSize;
    
    $favorites = $db->fetchAll(
        "SELECT a.*, c.name as category_name, uf.created_at as favorited_at
         FROM {$db->getPrefix()}user_favorites uf 
         JOIN {$db->getPrefix()}albums a ON uf.content_id = a.id 
         LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
         WHERE uf.user_id = :user_id AND uf.content_type = 'album'
         ORDER BY uf.created_at DESC LIMIT :limit OFFSET :offset",
        ['user_id' => $userId, 'limit' => $pageSize, 'offset' => $offset]
    );
    
    $total = $db->fetchValue(
        "SELECT COUNT(*) FROM {$db->getPrefix()}user_favorites WHERE user_id = :user_id AND content_type = 'album'",
        ['user_id' => $userId]
    );
    
    echo json_encode([
        'success' => true,
        'data' => $favorites,
        'pagination' => [
            'page' => $page,
            'page_size' => $pageSize,
            'total' => $total,
            'total_pages' => ceil($total / $pageSize)
        ]
    ]);
}

/**
 * 获取浏览历史
 */
function getHistory($db, $userId) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $pageSize = 20;
    $offset = ($page - 1) * $pageSize;
    
    $history = $db->fetchAll(
        "SELECT a.*, c.name as category_name, vh.last_viewed_at
         FROM {$db->getPrefix()}user_view_history vh 
         JOIN {$db->getPrefix()}albums a ON vh.content_id = a.id 
         LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
         WHERE vh.user_id = :user_id AND vh.content_type = 'album'
         ORDER BY vh.last_viewed_at DESC LIMIT :limit OFFSET :offset",
        ['user_id' => $userId, 'limit' => $pageSize, 'offset' => $offset]
    );
    
    $total = $db->fetchValue(
        "SELECT COUNT(*) FROM {$db->getPrefix()}user_view_history WHERE user_id = :user_id AND content_type = 'album'",
        ['user_id' => $userId]
    );
    
    echo json_encode([
        'success' => true,
        'data' => $history,
        'pagination' => [
            'page' => $page,
            'page_size' => $pageSize,
            'total' => $total,
            'total_pages' => ceil($total / $pageSize)
        ]
    ]);
}

/**
 * 获取积分记录
 */
function getPoints($db, $userId) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $pageSize = 20;
    $offset = ($page - 1) * $pageSize;
    
    $points = $db->fetchAll(
        "SELECT * FROM {$db->getPrefix()}user_points 
         WHERE user_id = :user_id 
         ORDER BY created_at DESC LIMIT :limit OFFSET :offset",
        ['user_id' => $userId, 'limit' => $pageSize, 'offset' => $offset]
    );
    
    $total = $db->fetchValue(
        "SELECT COUNT(*) FROM {$db->getPrefix()}user_points WHERE user_id = :user_id",
        ['user_id' => $userId]
    );
    
    echo json_encode([
        'success' => true,
        'data' => $points,
        'pagination' => [
            'page' => $page,
            'page_size' => $pageSize,
            'total' => $total,
            'total_pages' => ceil($total / $pageSize)
        ]
    ]);
}
?>
