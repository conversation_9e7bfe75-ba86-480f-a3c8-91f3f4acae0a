<?php
session_start();
header('Content-Type: application/json');

// 添加错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录', 'session_id' => session_id()]);
    exit;
}

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    // 处理toggle收藏功能
    if ($action === 'toggle' && $method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);

        $contentType = $input['type'] ?? 'album';  // 支持前端传递的'type'参数
        $contentId = intval($input['id'] ?? 0);    // 支持前端传递的'id'参数

        if ($contentId <= 0) {
            throw new Exception('无效的内容ID');
        }

        // 检查是否已收藏
        $exists = $db->fetchColumn(
            "SELECT COUNT(*) FROM {$db->getPrefix()}favorites
             WHERE user_id = :user_id AND target_type = :target_type AND target_id = :target_id",
            [
                'user_id' => $_SESSION['user_id'],
                'target_type' => $contentType,
                'target_id' => $contentId
            ]
        );

        if ($exists) {
            // 取消收藏
            $result = $db->delete('favorites',
                'user_id = :user_id AND target_type = :target_type AND target_id = :target_id',
                [
                    'user_id' => $_SESSION['user_id'],
                    'target_type' => $contentType,
                    'target_id' => $contentId
                ]
            );

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'action' => 'removed',
                    'message' => '取消收藏成功'
                ]);
            } else {
                throw new Exception('取消收藏失败');
            }
        } else {
            // 添加收藏
            $result = $db->insert('favorites', [
                'user_id' => $_SESSION['user_id'],
                'target_type' => $contentType,
                'target_id' => $contentId,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'action' => 'added',
                    'message' => '收藏成功'
                ]);
            } else {
                throw new Exception('收藏失败');
            }
        }
        exit;
    }

    switch ($method) {
        case 'POST':
            // 添加收藏
            $input = json_decode(file_get_contents('php://input'), true);
            $contentType = $input['content_type'] ?? 'album';
            $contentId = intval($input['content_id'] ?? 0);
            
            if ($contentId <= 0) {
                throw new Exception('无效的内容ID');
            }
            
            // 检查是否已收藏
            $exists = $db->fetchColumn(
                "SELECT COUNT(*) FROM {$db->getPrefix()}favorites 
                 WHERE user_id = :user_id AND content_type = :content_type AND content_id = :content_id",
                [
                    'user_id' => $_SESSION['user_id'],
                    'content_type' => $contentType,
                    'content_id' => $contentId
                ]
            );
            
            if ($exists) {
                echo json_encode(['error' => '已经收藏过了']);
                exit;
            }
            
            // 添加收藏
            $result = $db->insert('favorites', [
                'user_id' => $_SESSION['user_id'],
                'content_type' => $contentType,
                'content_id' => $contentId,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => '收藏成功']);
            } else {
                throw new Exception('收藏失败');
            }
            break;
            
        case 'DELETE':
            // 取消收藏
            $contentType = $_GET['content_type'] ?? 'album';
            $contentId = intval($_GET['content_id'] ?? 0);
            
            if ($contentId <= 0) {
                throw new Exception('无效的内容ID');
            }
            
            $result = $db->delete('favorites', 
                'user_id = :user_id AND content_type = :content_type AND content_id = :content_id',
                [
                    'user_id' => $_SESSION['user_id'],
                    'content_type' => $contentType,
                    'content_id' => $contentId
                ]
            );
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => '取消收藏成功']);
            } else {
                echo json_encode(['error' => '取消收藏失败']);
            }
            break;
            
        case 'GET':
            // 获取收藏列表
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = 12;
            $offset = ($page - 1) * $limit;
            $contentType = $_GET['content_type'] ?? 'album';
            
            if ($contentType === 'album') {
                $favorites = $db->fetchAll(
                    "SELECT a.*, c.name as category_name, f.created_at as favorited_at
                     FROM {$db->getPrefix()}favorites f 
                     JOIN {$db->getPrefix()}albums a ON f.content_id = a.id 
                     LEFT JOIN {$db->getPrefix()}categories c ON a.category_id = c.id
                     WHERE f.user_id = :user_id AND f.content_type = 'album'
                     ORDER BY f.created_at DESC 
                     LIMIT $limit OFFSET $offset",
                    ['user_id' => $_SESSION['user_id']]
                );
            } else {
                $favorites = $db->fetchAll(
                    "SELECT v.*, c.name as category_name, f.created_at as favorited_at
                     FROM {$db->getPrefix()}favorites f 
                     JOIN {$db->getPrefix()}videos v ON f.content_id = v.id 
                     LEFT JOIN {$db->getPrefix()}categories c ON v.category_id = c.id
                     WHERE f.user_id = :user_id AND f.content_type = 'video'
                     ORDER BY f.created_at DESC 
                     LIMIT $limit OFFSET $offset",
                    ['user_id' => $_SESSION['user_id']]
                );
            }
            
            // 获取总数
            $total = $db->fetchColumn(
                "SELECT COUNT(*) FROM {$db->getPrefix()}favorites 
                 WHERE user_id = :user_id AND content_type = :content_type",
                [
                    'user_id' => $_SESSION['user_id'],
                    'content_type' => $contentType
                ]
            );
            
            echo json_encode([
                'success' => true,
                'data' => $favorites,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
