<?php
session_start();
header('Content-Type: application/json');

// 添加错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 检查session
    $sessionInfo = [
        'session_id' => session_id(),
        'user_id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null
    ];

    // 对于测试，不要求登录
    $requireLogin = $_GET['require_login'] ?? false;
    
    // 尝试连接数据库
    require_once __DIR__ . '/../core/Database.php';
    $db = Database::getInstance();

    // 测试数据库查询
    $user = null;
    if (isset($_SESSION['user_id'])) {
        $user = $db->fetch("SELECT id, username FROM lp_users WHERE id = :id", ['id' => $_SESSION['user_id']]);
    }

    echo json_encode([
        'success' => true,
        'message' => '测试成功',
        'session_info' => $sessionInfo,
        'user_info' => $user,
        'method' => $_SERVER['REQUEST_METHOD'],
        'get_params' => $_GET,
        'post_params' => $_POST,
        'input' => json_decode(file_get_contents('php://input'), true)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
?>
