<?php
session_start();
header('Content-Type: application/json');

// 添加错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$method = $_SERVER['REQUEST_METHOD'];

try {
    // 检查是否已登录
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('请先登录');
    }
    
    if ($method === 'POST') {
        $feedbackId = intval($_POST['feedback_id'] ?? 0);
        $content = trim($_POST['content'] ?? '');
        $userId = $_SESSION['user_id'];
        
        // 验证必填字段
        if (!$feedbackId || empty($content)) {
            throw new Exception('请填写所有必填字段');
        }
        
        // 验证工单是否存在且属于当前用户
        $feedback = $db->fetch("
            SELECT * FROM {$db->getPrefix()}feedback 
            WHERE id = :id AND user_id = :user_id
        ", ['id' => $feedbackId, 'user_id' => $userId]);
        
        if (!$feedback) {
            throw new Exception('工单不存在或您没有权限访问');
        }
        
        // 检查工单状态是否允许回复
        if (in_array($feedback['status'], ['resolved', 'closed'])) {
            throw new Exception('此工单已' . ($feedback['status'] === 'resolved' ? '解决' : '关闭') . '，无法继续回复');
        }
        
        // 插入回复记录
        $result = $db->insert('feedback_replies', [
            'feedback_id' => $feedbackId,
            'user_id' => $userId,
            'content' => $content
        ]);
        
        if ($result) {
            // 更新工单状态为处理中（如果当前是待处理）
            if ($feedback['status'] === 'pending') {
                $db->update('feedback', 
                    ['status' => 'processing'], 
                    'id = :id', 
                    ['id' => $feedbackId]
                );
                
                // 记录状态变更日志
                $db->insert('feedback_status_logs', [
                    'feedback_id' => $feedbackId,
                    'old_status' => 'pending',
                    'new_status' => 'processing',
                    'note' => '用户回复后自动更新为处理中'
                ]);
            }
            
            // 如果是AJAX请求，返回JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                echo json_encode([
                    'success' => true,
                    'message' => '回复提交成功'
                ]);
            } else {
                // 普通表单提交，重定向回工单详情页
                header('Location: /support?action=view&id=' . $feedbackId . '&success=reply');
            }
        } else {
            throw new Exception('回复提交失败，请稍后重试');
        }
    } else {
        throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    error_log("工单回复API错误: " . $e->getMessage());
    
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } else {
        // 普通表单提交，重定向回工单详情页并显示错误
        $feedbackId = intval($_POST['feedback_id'] ?? 0);
        if ($feedbackId > 0) {
            header('Location: /support?action=view&id=' . $feedbackId . '&error=' . urlencode($e->getMessage()));
        } else {
            header('Location: /support?error=' . urlencode($e->getMessage()));
        }
    }
}
?>
