<?php
header('Content-Type: application/json; charset=utf-8');

require_once __DIR__ . '/../models/Album.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../core/ImageHandler.php';

/**
 * 火车头发布接口
 * 支持套图和视频的批量发布
 */

class TrainPublishAPI {
    private $db;
    private $albumModel;
    private $imageHandler;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->albumModel = new Album();
        $this->imageHandler = new ImageHandler();
    }
    
    /**
     * 处理发布请求
     */
    public function handleRequest() {
        // 验证请求方法
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->returnError('只支持POST请求');
        }
        
        // 获取请求数据
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->returnError('JSON格式错误');
        }
        
        // 验证必需字段
        if (empty($data['type']) || empty($data['title'])) {
            $this->returnError('缺少必需字段');
        }
        
        // 验证API密钥（可选，建议配置）
        if (!$this->validateApiKey($data['api_key'] ?? '')) {
            $this->returnError('API密钥无效');
        }
        
        try {
            switch ($data['type']) {
                case 'album':
                    $result = $this->publishAlbum($data);
                    break;
                case 'video':
                    $result = $this->publishVideo($data);
                    break;
                default:
                    $this->returnError('不支持的发布类型');
            }
            
            // 记录发布日志
            $this->logPublish($data['type'], $result['id'], $data['title'], true);
            
            $this->returnSuccess($result);
        } catch (Exception $e) {
            // 记录错误日志
            $this->logPublish($data['type'], 0, $data['title'], false, $e->getMessage());
            $this->returnError($e->getMessage());
        }
    }
    
    /**
     * 发布套图
     */
    private function publishAlbum($data) {
        // 验证分类
        $categoryId = $this->validateCategory($data['category_id'] ?? 0, 'album');
        
        // 准备套图数据
        $albumData = [
            'title' => $data['title'],
            'description' => $data['description'] ?? '',
            'content' => $data['content'] ?? '',
            'category_id' => $categoryId,
            'tags' => $data['tags'] ?? '',
            'is_free' => $data['is_free'] ?? 1,
            'status' => $data['status'] ?? 1,
            'admin_id' => $data['admin_id'] ?? null
        ];
        
        // 创建套图
        $albumId = $this->albumModel->create($albumData);
        if (!$albumId) {
            throw new Exception('套图创建失败');
        }
        
        // 处理图片
        $imageCount = 0;
        if (!empty($data['images']) && is_array($data['images'])) {
            $enableWebp = $data['enable_webp'] ?? null;
            
            foreach ($data['images'] as $index => $imageUrl) {
                $result = $this->imageHandler->processImageFromUrl($imageUrl, $albumId, $enableWebp);
                if ($result['success']) {
                    $imageCount++;
                    
                    // 更新图片排序
                    $this->db->update('album_images', [
                        'sort' => $index + 1
                    ], 'id = :id', ['id' => $result['image_id']]);
                    
                    // 如果是第一张图片，设为封面
                    if ($index === 0) {
                        $this->albumModel->update($albumId, [
                            'cover' => $result['image_data']['file_url']
                        ]);
                    }
                }
            }
        }
        
        // 更新图片数量
        $this->albumModel->updateImageCount($albumId);
        
        return [
            'id' => $albumId,
            'type' => 'album',
            'image_count' => $imageCount,
            'message' => '套图发布成功'
        ];
    }
    
    /**
     * 发布视频
     */
    private function publishVideo($data) {
        // 验证分类
        $categoryId = $this->validateCategory($data['category_id'] ?? 0, 'video');
        
        // 验证视频URL
        if (empty($data['video_url'])) {
            throw new Exception('视频URL不能为空');
        }
        
        // 处理封面图片
        $coverUrl = '';
        if (!empty($data['cover_url'])) {
            $coverResult = $this->imageHandler->processImageFromUrl($data['cover_url'], 0, $data['enable_webp'] ?? null);
            if ($coverResult['success']) {
                $coverUrl = $coverResult['image_data']['file_url'];
            }
        }
        
        // 准备视频数据
        $videoData = [
            'title' => $data['title'],
            'description' => $data['description'] ?? '',
            'content' => $data['content'] ?? '',
            'category_id' => $categoryId,
            'cover' => $coverUrl,
            'video_url' => $data['video_url'],
            'duration' => $data['duration'] ?? 0,
            'file_size' => $data['file_size'] ?? 0,
            'tags' => $data['tags'] ?? '',
            'is_free' => $data['is_free'] ?? 1,
            'status' => $data['status'] ?? 1,
            'admin_id' => $data['admin_id'] ?? null
        ];
        
        // 设置发布时间
        if ($videoData['status'] == 1) {
            $videoData['published_at'] = date('Y-m-d H:i:s');
        }
        
        // 创建视频
        $videoId = $this->db->insert('videos', $videoData);
        if (!$videoId) {
            throw new Exception('视频创建失败');
        }
        
        return [
            'id' => $videoId,
            'type' => 'video',
            'message' => '视频发布成功'
        ];
    }
    
    /**
     * 验证分类
     */
    private function validateCategory($categoryId, $type) {
        if (empty($categoryId)) {
            // 获取默认分类
            $defaultCategory = $this->db->fetch(
                "SELECT id FROM {$this->db->getPrefix()}categories WHERE type = :type AND status = 1 ORDER BY sort ASC LIMIT 1",
                ['type' => $type]
            );
            
            if (!$defaultCategory) {
                throw new Exception('找不到有效的分类');
            }
            
            return $defaultCategory['id'];
        }
        
        // 验证分类是否存在且有效
        $category = $this->db->fetch(
            "SELECT id FROM {$this->db->getPrefix()}categories WHERE id = :id AND type = :type AND status = 1",
            ['id' => $categoryId, 'type' => $type]
        );
        
        if (!$category) {
            throw new Exception('指定的分类不存在或已禁用');
        }
        
        return $categoryId;
    }
    
    /**
     * 验证API密钥
     */
    private function validateApiKey($apiKey) {
        // 从配置中获取API密钥
        $configApiKey = $this->getConfig('train_api_key', '');
        
        // 如果没有配置API密钥，则跳过验证
        if (empty($configApiKey)) {
            return true;
        }
        
        return $apiKey === $configApiKey;
    }
    
    /**
     * 记录发布日志
     */
    private function logPublish($type, $targetId, $title, $success, $errorMsg = '') {
        $this->db->insert('train_publish_log', [
            'type' => $type,
            'target_id' => $targetId,
            'title' => $title,
            'status' => $success ? 1 : 0,
            'error_msg' => $errorMsg,
            'ip_address' => $this->getClientIp()
        ]);
    }
    
    /**
     * 返回成功响应
     */
    private function returnSuccess($data) {
        echo json_encode([
            'code' => 200,
            'message' => 'success',
            'data' => $data
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 返回错误响应
     */
    private function returnError($message, $code = 400) {
        echo json_encode([
            'code' => $code,
            'message' => $message,
            'data' => null
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'] ?? '';
        }
    }
    
    /**
     * 获取配置
     */
    private function getConfig($key, $default = null) {
        $config = $this->db->fetch(
            "SELECT value FROM {$this->db->getPrefix()}system_config WHERE `key` = :key",
            ['key' => $key]
        );
        
        return $config ? $config['value'] : $default;
    }
}

// 处理请求
$api = new TrainPublishAPI();
$api->handleRequest();
