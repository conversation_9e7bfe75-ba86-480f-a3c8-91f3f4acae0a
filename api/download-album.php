<?php
session_start();
require_once __DIR__ . '/../core/Database.php';
require_once __DIR__ . '/../models/Album.php';

// 设置执行时间和内存限制
set_time_limit(300); // 5分钟
ini_set('memory_limit', '512M');

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => '只允许POST请求']);
    exit;
}

// 获取参数
$albumId = intval($_POST['album_id'] ?? 0);

if ($albumId <= 0) {
    http_response_code(400);
    echo json_encode(['error' => '无效的套图ID']);
    exit;
}

try {
    $albumModel = new Album();
    $album = $albumModel->getById($albumId);
    
    if (!$album) {
        http_response_code(404);
        echo json_encode(['error' => '套图不存在']);
        exit;
    }
    
    // 获取所有图片，限制数量避免超时
    $images = $albumModel->getImages($albumId, 1, 50); // 限制最多50张

    if (empty($images)) {
        http_response_code(404);
        echo json_encode(['error' => '套图中没有图片']);
        exit;
    }
    
    // 检查ZipArchive扩展
    if (!class_exists('ZipArchive')) {
        http_response_code(500);
        echo json_encode(['error' => '服务器不支持ZIP压缩功能']);
        exit;
    }
    
    // 创建临时ZIP文件
    $tempDir = sys_get_temp_dir();
    $zipFileName = 'album_' . $albumId . '_' . date('YmdHis') . '.zip';
    $zipFilePath = $tempDir . '/' . $zipFileName;
    
    $zip = new ZipArchive();
    $result = $zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE);
    
    if ($result !== TRUE) {
        http_response_code(500);
        echo json_encode(['error' => '无法创建ZIP文件: ' . $result]);
        exit;
    }
    
    // 添加图片到ZIP
    $successCount = 0;
    $totalCount = count($images);
    
    foreach ($images as $index => $image) {
        $imageUrl = $image['file_url'];

        // 跳过空的URL
        if (empty($imageUrl)) {
            continue;
        }

        try {
            // 获取图片内容
            $context = stream_context_create([
                'http' => [
                    'timeout' => 15, // 减少超时时间
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'follow_location' => true,
                    'max_redirects' => 3
                ]
            ]);

            $imageContent = @file_get_contents($imageUrl, false, $context);

            if ($imageContent !== false && strlen($imageContent) > 0) {
                // 生成文件名
                $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
                if (empty($extension)) {
                    $extension = 'jpg'; // 默认扩展名
                }

                $fileName = sprintf('%s_%03d.%s',
                    preg_replace('/[^a-zA-Z0-9\-_]/', '_', $album['title']),
                    $index + 1,
                    $extension
                );

                // 添加到ZIP
                if ($zip->addFromString($fileName, $imageContent)) {
                    $successCount++;
                }
            }
        } catch (Exception $e) {
            // 记录错误但继续处理其他图片
            error_log("下载图片失败: {$imageUrl} - " . $e->getMessage());
        }

        // 释放内存
        unset($imageContent);
    }
    
    // 添加说明文件
    $readme = "套图名称: " . $album['title'] . "\n";
    $readme .= "图片数量: " . $totalCount . "\n";
    $readme .= "成功下载: " . $successCount . "\n";
    $readme .= "下载时间: " . date('Y-m-d H:i:s') . "\n";
    $readme .= "来源网站: " . $_SERVER['HTTP_HOST'] . "\n";
    
    $zip->addFromString('README.txt', $readme);
    
    // 关闭ZIP文件
    $zip->close();
    
    if ($successCount === 0) {
        @unlink($zipFilePath);
        http_response_code(500);
        echo json_encode(['error' => '没有成功下载任何图片']);
        exit;
    }
    
    // 更新下载统计
    $db = Database::getInstance();
    $db->query("UPDATE {$db->getPrefix()}albums SET download_count = download_count + 1 WHERE id = :id", ['id' => $albumId]);
    
    // 检查文件是否存在
    if (!file_exists($zipFilePath)) {
        http_response_code(500);
        echo json_encode(['error' => 'ZIP文件创建失败']);
        exit;
    }
    
    $fileSize = filesize($zipFilePath);
    
    // 返回下载信息
    echo json_encode([
        'success' => true,
        'download_url' => '/api/download-file.php?file=' . urlencode($zipFileName),
        'filename' => $zipFileName,
        'size' => $fileSize,
        'total_images' => $totalCount,
        'success_images' => $successCount
    ]);
    
} catch (Exception $e) {
    error_log('下载套图失败: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => '下载失败: ' . $e->getMessage()]);
}
?>
