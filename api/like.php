<?php
// 点赞API
session_start();
header('Content-Type: application/json');

// 添加错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../core/Database.php';



// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$db = Database::getInstance();
$userId = $_SESSION['user_id'];

try {
    if ($method === 'POST') {
        // 切换点赞状态
        $input = json_decode(file_get_contents('php://input'), true);
        if (!$input) {
            $input = $_POST;
        }

        $type = $input['type'] ?? '';
        $contentId = intval($input['id'] ?? 0);

        if (!in_array($type, ['album', 'video']) || $contentId <= 0) {
            throw new Exception('参数错误');
        }
        
        // 检查是否已点赞
        $exists = $db->fetchColumn(
            "SELECT id FROM {$db->getPrefix()}user_likes
             WHERE user_id = :user_id AND content_type = :type AND content_id = :content_id",
            ['user_id' => $userId, 'type' => $type, 'content_id' => $contentId]
        );
        
        if ($exists) {
            // 取消点赞
            $db->query(
                "DELETE FROM {$db->getPrefix()}user_likes
                 WHERE user_id = :user_id AND content_type = :type AND content_id = :content_id",
                ['user_id' => $userId, 'type' => $type, 'content_id' => $contentId]
            );

            // 更新点赞数
            $table = $type === 'album' ? 'albums' : 'videos';
            $db->query(
                "UPDATE {$db->getPrefix()}{$table} SET like_count = like_count - 1 WHERE id = :id",
                ['id' => $contentId]
            );
            
            // 获取新的点赞数
            $newCount = $db->fetchColumn(
                "SELECT like_count FROM {$db->getPrefix()}{$table} WHERE id = :id",
                ['id' => $contentId]
            );
            
            echo json_encode([
                'success' => true, 
                'action' => 'unliked', 
                'message' => '已取消点赞',
                'count' => intval($newCount)
            ]);
        } else {
            // 添加点赞
            $db->query(
                "INSERT INTO {$db->getPrefix()}user_likes (user_id, content_type, content_id, created_at)
                 VALUES (:user_id, :type, :content_id, :created_at)",
                [
                    'user_id' => $userId,
                    'type' => $type,
                    'content_id' => $contentId,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            );

            // 更新点赞数
            $table = $type === 'album' ? 'albums' : 'videos';
            $db->query(
                "UPDATE {$db->getPrefix()}{$table} SET like_count = like_count + 1 WHERE id = :id",
                ['id' => $contentId]
            );
            
            // 获取新的点赞数
            $newCount = $db->fetchColumn(
                "SELECT like_count FROM {$db->getPrefix()}{$table} WHERE id = :id",
                ['id' => $contentId]
            );
            
            echo json_encode([
                'success' => true, 
                'action' => 'liked', 
                'message' => '点赞成功',
                'count' => intval($newCount)
            ]);
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => '不支持的请求方法']);
    }
    
} catch (Exception $e) {
    error_log('点赞操作失败: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
