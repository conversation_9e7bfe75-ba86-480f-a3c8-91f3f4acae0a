<?php
session_start();
header('Content-Type: application/json');

// 添加错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../core/Database.php';

$db = Database::getInstance();
$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'POST') {
        // 获取表单数据
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $priority = trim($_POST['priority'] ?? 'low');
        $userId = $_SESSION['user_id'] ?? null;
        
        // 验证必填字段
        if (empty($name) || empty($email) || empty($subject) || empty($message)) {
            throw new Exception('请填写所有必填字段');
        }
        
        // 验证邮箱格式
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('请输入有效的邮箱地址');
        }
        
        // 验证反馈类型
        $validSubjects = ['bug', 'feature', 'content', 'account', 'payment', 'technical', 'other'];
        if (!in_array($subject, $validSubjects)) {
            throw new Exception('请选择有效的反馈类型');
        }

        // 验证优先级
        $validPriorities = ['low', 'medium', 'high'];
        if (!in_array($priority, $validPriorities)) {
            $priority = 'low';
        }
        
        // 创建反馈表（如果不存在）
        $createTableSql = "
            CREATE TABLE IF NOT EXISTS {$db->getPrefix()}feedback (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) DEFAULT NULL,
                name varchar(100) NOT NULL,
                email varchar(255) NOT NULL,
                subject varchar(50) NOT NULL,
                message text NOT NULL,
                priority enum('low','medium','high') DEFAULT 'low',
                status enum('pending','processing','resolved','closed') DEFAULT 'pending',
                admin_reply text DEFAULT NULL,
                created_at timestamp DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY idx_user_id (user_id),
                KEY idx_status (status),
                KEY idx_priority (priority),
                KEY idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈表'
        ";
        $db->query($createTableSql);
        
        // 插入反馈记录
        $result = $db->insert('feedback', [
            'user_id' => $userId,
            'name' => $name,
            'email' => $email,
            'subject' => $subject,
            'message' => $message,
            'priority' => $priority,
            'status' => 'pending'
        ]);
        
        if ($result) {
            // 如果是AJAX请求，返回JSON
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                echo json_encode([
                    'success' => true,
                    'message' => '反馈提交成功，我们会尽快处理您的问题'
                ]);
            } else {
                // 普通表单提交，重定向回联系页面
                header('Location: /contact?success=1');
            }
        } else {
            throw new Exception('反馈提交失败，请稍后重试');
        }
    } else {
        throw new Exception('不支持的请求方法');
    }
} catch (Exception $e) {
    error_log("联系我们API错误: " . $e->getMessage());
    
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    } else {
        // 普通表单提交，重定向回联系页面并显示错误
        header('Location: /contact?error=' . urlencode($e->getMessage()));
    }
}
?>
