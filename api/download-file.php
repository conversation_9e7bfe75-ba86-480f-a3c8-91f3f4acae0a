<?php
// 文件下载处理器
session_start();

$fileName = $_GET['file'] ?? '';

if (empty($fileName)) {
    http_response_code(400);
    echo '无效的文件名';
    exit;
}

// 安全检查：只允许下载指定格式的文件
if (!preg_match('/^album_\d+_\d{14}\.zip$/', $fileName)) {
    http_response_code(400);
    echo '无效的文件格式';
    exit;
}

$tempDir = sys_get_temp_dir();
$filePath = $tempDir . '/' . $fileName;

// 检查文件是否存在
if (!file_exists($filePath)) {
    http_response_code(404);
    echo '文件不存在或已过期';
    exit;
}

// 检查文件年龄（超过1小时的文件自动删除）
$fileAge = time() - filemtime($filePath);
if ($fileAge > 3600) {
    @unlink($filePath);
    http_response_code(404);
    echo '文件已过期';
    exit;
}

// 设置下载头
header('Content-Type: application/zip');
header('Content-Disposition: attachment; filename="' . $fileName . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// 输出文件内容
readfile($filePath);

// 下载完成后删除临时文件
@unlink($filePath);
exit;
?>
