<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <style>
        body { background: #000; color: #fff; font-family: Arial, sans-serif; }
        .test-info { padding: 20px; max-width: 800px; margin: 0 auto; }
        .test-button { 
            background: #007bff; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            margin: 10px; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        .test-button:hover { background: #0056b3; }
        .status { 
            background: #333; 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            font-family: monospace; 
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>图片拖拽功能测试页面</h1>
        
        <div class="status">
            <h3>测试说明：</h3>
            <p>1. 点击"打开测试图片"按钮</p>
            <p>2. 图片会在灯箱中以全屏模式打开（可拖拽模式）</p>
            <p>3. 尝试拖拽图片，应该能正常拖拽</p>
            <p>4. 点击图片切换到适应模式（不可拖拽）</p>
            <p>5. 再次点击图片切换回全屏模式</p>
            <p>6. <span class="warning">重点测试：此时拖拽功能应该仍然有效</span></p>
        </div>
        
        <button class="test-button" onclick="openTestImage()">📸 打开测试图片</button>
        <button class="test-button" onclick="runDragTest()">🔧 运行拖拽测试</button>
        <button class="test-button" onclick="debugCurrentState()">🐛 调试当前状态</button>
        <button class="test-button" onclick="quickToggleTest()">⚡ 快速切换测试</button>
        
        <div id="testResults" class="status">
            <p>等待测试...</p>
        </div>
        
        <div class="status">
            <h3>修复内容：</h3>
            <p class="success">✅ 修复了事件处理器的闭包问题</p>
            <p class="success">✅ 改进了事件清理机制</p>
            <p class="success">✅ 确保拖拽偏移量在模式切换时正确保持</p>
            <p class="success">✅ 增强了触摸事件的处理</p>
            <p class="success">✅ 添加了详细的调试日志</p>
        </div>
        
        <div class="status">
            <h3>访问实际页面进行测试：</h3>
            <p><a href="/album/1" style="color: #007bff;">访问套图详情页面</a></p>
            <p>在浏览器控制台中运行以下测试命令：</p>
            <ul>
                <li><code>testDragFunction()</code> - 测试拖拽功能</li>
                <li><code>quickToggleTest()</code> - 快速切换测试</li>
                <li><code>debugImages()</code> - 调试图片状态</li>
            </ul>
        </div>
    </div>
</body>
</html>
