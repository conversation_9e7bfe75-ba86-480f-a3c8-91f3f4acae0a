<?php
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

require_once __DIR__ . '/core/Database.php';
require_once __DIR__ . '/core/InviteManager.php';
require_once __DIR__ . '/includes/functions.php';

$db = Database::getInstance();
$inviteManager = new InviteManager();
$userId = $_SESSION['user_id'];

// 获取用户信息
$user = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $userId]);
if (!$user) {
    header('Location: /login');
    exit;
}

// 确保用户有邀请码
if (empty($user['invite_code'])) {
    $inviteCode = $inviteManager->generateInviteCode($userId);
    $user['invite_code'] = $inviteCode;
}

// 处理表单提交
$message = '';
$messageType = '';

if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'withdraw':
                $pointsAmount = intval($_POST['points_amount']);
                $accountType = $_POST['account_type'];
                $accountInfo = [
                    'name' => trim($_POST['account_name']),
                    'number' => trim($_POST['account_number']),
                    'bank_name' => trim($_POST['bank_name'] ?? ''),
                    'branch_name' => trim($_POST['branch_name'] ?? '')
                ];
                
                $result = $inviteManager->requestWithdraw($userId, $pointsAmount, $accountType, $accountInfo);
                
                if ($result['success']) {
                    $message = $result['message'] . "，实际到账金额：¥" . number_format($result['actual_amount'], 2);
                    $messageType = 'success';
                    // 刷新用户信息
                    $user = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $userId]);
                } else {
                    $message = $result['error'];
                    $messageType = 'error';
                }
                break;
        }
    } catch (Exception $e) {
        $message = '操作失败：' . $e->getMessage();
        $messageType = 'error';
    }
}

// 获取统计数据
$stats = $inviteManager->getUserInviteStats($userId);

// 获取邀请收益记录
$earningsHistory = $inviteManager->getUserEarningsHistory($userId, 10);

// 获取邀请用户列表
$invitees = $inviteManager->getUserInvitees($userId, 10);

// 获取提现记录
$withdrawHistory = $inviteManager->getUserWithdrawHistory($userId, 10);

// 获取系统设置
$withdrawThresholdResult = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'withdraw_threshold'");
$withdrawThreshold = $withdrawThresholdResult ? $withdrawThresholdResult['value'] : 1000;

$pointsRateResult = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'points_to_money_rate'");
$pointsRate = $pointsRateResult ? $pointsRateResult['value'] : 100;

$rechargeRateResult = $db->fetch("SELECT value FROM {$db->getPrefix()}system_config WHERE `key` = 'invite_recharge_rate'");
$rechargeRate = $rechargeRateResult ? $rechargeRateResult['value'] : 30;

// 生成邀请链接
$siteUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$inviteUrl = $siteUrl . '/register?invite=' . $user['invite_code'];

$pageTitle = '邀请分钱';
$pageDescription = '邀请好友注册充值，获得丰厚分成奖励';

// 使用布局模板
ob_start();
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">首页</a></li>
                    <li class="breadcrumb-item active">邀请分钱</li>
                </ol>
            </nav>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-<?php echo $messageType === 'success' ? 'success' : 'danger'; ?> alert-dismissible">
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['direct_invites']; ?></h4>
                            <p class="mb-0">直接邀请</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['direct_earnings']; ?></h4>
                            <p class="mb-0">直接收益</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['level2_invites']; ?></h4>
                            <p class="mb-0">二级邀请</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sitemap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4><?php echo $stats['total_earnings']; ?></h4>
                            <p class="mb-0">总收益</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：邀请链接和提现 -->
        <div class="col-md-6">
            <!-- 邀请链接 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-link"></i> 我的邀请链接
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">邀请码</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?php echo $user['invite_code']; ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo $user['invite_code']; ?>')">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">邀请链接</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<?php echo $inviteUrl; ?>" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo $inviteUrl; ?>')">
                                <i class="fas fa-copy"></i> 复制
                            </button>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 邀请奖励说明</h6>
                        <ul class="mb-0">
                            <li>好友通过您的链接注册，您可获得注册奖励积分</li>
                            <li>好友充值时，您可获得 <strong><?php echo $rechargeRate; ?>%</strong> 的分成奖励</li>
                            <li>支持二级邀请，您的好友邀请他人时您也有收益</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 提现申请 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave"></i> 申请提现
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>当前积分：</span>
                            <strong><?php echo number_format($user['points']); ?></strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>可提现金额：</span>
                            <strong>¥<?php echo number_format($user['points'] / $pointsRate, 2); ?></strong>
                        </div>
                        <small class="text-muted">提现门槛：<?php echo number_format($withdrawThreshold); ?> 积分</small>
                    </div>

                    <?php if ($user['points'] >= $withdrawThreshold): ?>
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                            <i class="fas fa-hand-holding-usd"></i> 申请提现
                        </button>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            您的积分未达到提现门槛（<?php echo number_format($withdrawThreshold); ?> 积分）
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 右侧：记录列表 -->
        <div class="col-md-6">
            <!-- 收益记录 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> 最近收益
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($earningsHistory)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无收益记录</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>类型</th>
                                        <th>用户</th>
                                        <th>积分</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($earningsHistory as $earning): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo match($earning['type']) {
                                                        'register' => 'primary',
                                                        'recharge' => 'success',
                                                        'level2_register' => 'info',
                                                        'level2_recharge' => 'warning',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php 
                                                        echo match($earning['type']) {
                                                            'register' => '注册',
                                                            'recharge' => '充值',
                                                            'level2_register' => '二级注册',
                                                            'level2_recharge' => '二级充值',
                                                            default => $earning['type']
                                                        };
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($earning['invitee_username'] ?: '未知'); ?></td>
                                            <td>+<?php echo $earning['points_earned']; ?></td>
                                            <td><?php echo date('m-d H:i', strtotime($earning['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 提现记录 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-receipt"></i> 提现记录
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($withdrawHistory)): ?>
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无提现记录</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($withdrawHistory as $withdraw): ?>
                                        <tr>
                                            <td>¥<?php echo number_format($withdraw['actual_amount'], 2); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo match($withdraw['status']) {
                                                        'pending' => 'warning',
                                                        'processing' => 'info',
                                                        'approved' => 'primary',
                                                        'completed' => 'success',
                                                        'rejected' => 'danger',
                                                        default => 'secondary'
                                                    };
                                                ?>">
                                                    <?php 
                                                        echo match($withdraw['status']) {
                                                            'pending' => '待处理',
                                                            'processing' => '处理中',
                                                            'approved' => '已审核',
                                                            'completed' => '已完成',
                                                            'rejected' => '已驳回',
                                                            default => $withdraw['status']
                                                        };
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('m-d H:i', strtotime($withdraw['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提现申请模态框 -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title">申请提现</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="action" value="withdraw">

                    <div class="mb-3">
                        <label class="form-label">提现积分</label>
                        <input type="number" name="points_amount" class="form-control"
                               min="<?php echo $withdrawThreshold; ?>"
                               max="<?php echo $user['points']; ?>"
                               value="<?php echo $withdrawThreshold; ?>" required>
                        <small class="text-muted">
                            可提现：<?php echo number_format($user['points']); ?> 积分
                            (约 ¥<?php echo number_format($user['points'] / $pointsRate, 2); ?>)
                        </small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">收款方式</label>
                        <select name="account_type" class="form-select" required onchange="toggleAccountFields(this.value)">
                            <option value="">请选择</option>
                            <option value="alipay">支付宝</option>
                            <option value="wechat">微信</option>
                            <option value="bank">银行卡</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">账户姓名</label>
                        <input type="text" name="account_name" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">账号</label>
                        <input type="text" name="account_number" class="form-control" required>
                    </div>

                    <div class="mb-3 bank-fields" style="display: none;">
                        <label class="form-label">银行名称</label>
                        <input type="text" name="bank_name" class="form-control">
                    </div>

                    <div class="mb-3 bank-fields" style="display: none;">
                        <label class="form-label">开户行</label>
                        <input type="text" name="branch_name" class="form-control">
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>提现说明：</strong>
                        <ul class="mb-0 mt-2">
                            <li>提现申请提交后，我们将在1-3个工作日内处理</li>
                            <li>请确保收款信息准确无误，错误信息可能导致提现失败</li>
                            <li>提现过程中积分将被冻结，审核通过后扣除</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-success">提交申请</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check"></i> 复制成功！
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(function(err) {
        alert('复制失败，请手动复制');
    });
}

function toggleAccountFields(accountType) {
    const bankFields = document.querySelectorAll('.bank-fields');
    if (accountType === 'bank') {
        bankFields.forEach(field => field.style.display = 'block');
        document.querySelector('input[name="bank_name"]').required = true;
        document.querySelector('input[name="branch_name"]').required = true;
    } else {
        bankFields.forEach(field => field.style.display = 'none');
        document.querySelector('input[name="bank_name"]').required = false;
        document.querySelector('input[name="branch_name"]').required = false;
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/templates/layout.php';
?>
