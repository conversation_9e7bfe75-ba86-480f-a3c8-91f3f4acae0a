<?php
session_start();
require_once __DIR__ . '/core/Database.php';

$db = Database::getInstance();

echo "<h2>修复验证测试</h2>";

// 1. 检查comments表是否存在
echo "<h3>1. 检查comments表</h3>";
try {
    $result = $db->query("SHOW TABLES LIKE 'lp_comments'");
    if ($result->rowCount() > 0) {
        echo "✅ comments表存在<br>";
        
        // 检查表结构
        $columns = $db->fetchAll("DESCRIBE lp_comments");
        echo "表结构：<br>";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})<br>";
        }
    } else {
        echo "❌ comments表不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ 检查comments表失败: " . $e->getMessage() . "<br>";
}

// 2. 检查user_likes表是否存在
echo "<h3>2. 检查user_likes表</h3>";
try {
    $result = $db->query("SHOW TABLES LIKE 'lp_user_likes'");
    if ($result->rowCount() > 0) {
        echo "✅ user_likes表存在<br>";
        
        // 检查表结构
        $columns = $db->fetchAll("DESCRIBE lp_user_likes");
        echo "表结构：<br>";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})<br>";
        }
    } else {
        echo "❌ user_likes表不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ 检查user_likes表失败: " . $e->getMessage() . "<br>";
}

// 3. 检查favorites表结构
echo "<h3>3. 检查favorites表</h3>";
try {
    $result = $db->query("SHOW TABLES LIKE 'lp_favorites'");
    if ($result->rowCount() > 0) {
        echo "✅ favorites表存在<br>";
        
        // 检查表结构
        $columns = $db->fetchAll("DESCRIBE lp_favorites");
        echo "表结构：<br>";
        foreach ($columns as $col) {
            echo "- {$col['Field']} ({$col['Type']})<br>";
        }
    } else {
        echo "❌ favorites表不存在<br>";
    }
} catch (Exception $e) {
    echo "❌ 检查favorites表失败: " . $e->getMessage() . "<br>";
}

// 4. 测试API响应
echo "<h3>4. API测试</h3>";
echo "请在浏览器中手动测试点赞和收藏功能<br>";
echo "如果仍有问题，请检查浏览器控制台的错误信息<br>";

?>
