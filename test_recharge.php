<?php
session_start();
require_once __DIR__ . '/core/Database.php';

$db = Database::getInstance();

// 模拟登录用户
$_SESSION['user_id'] = 1;

// 测试VIP充值卡查询（365天年费会员卡）
$cardNumber = 'CFB7E04F585295B8';
$cardPassword = 'C407B108EB6B';

echo "<h2>充值卡测试</h2>";

// 查找充值卡
$card = $db->fetch("
    SELECT * FROM {$db->getPrefix()}recharge_cards 
    WHERE card_number = :card_number AND card_password = :card_password AND used_at IS NULL AND status = 1
", [
    'card_number' => $cardNumber,
    'card_password' => $cardPassword
]);

if ($card) {
    echo "<h3>✅ 充值卡找到了！</h3>";
    echo "<p><strong>卡号：</strong>" . htmlspecialchars($card['card_number']) . "</p>";
    echo "<p><strong>密码：</strong>" . htmlspecialchars($card['card_password']) . "</p>";
    echo "<p><strong>类型：</strong>" . htmlspecialchars($card['type']) . "</p>";
    echo "<p><strong>积分：</strong>" . $card['points'] . "</p>";
    echo "<p><strong>VIP天数：</strong>" . $card['group_days'] . "</p>";
    echo "<p><strong>用户组ID：</strong>" . $card['group_id'] . "</p>";
    echo "<p><strong>状态：</strong>" . ($card['used_at'] ? '已使用' : '未使用') . "</p>";

    // 检查目标用户组是否存在
    if ($card['group_id']) {
        $targetGroup = $db->fetch("SELECT * FROM {$db->getPrefix()}user_groups WHERE id = :id", ['id' => $card['group_id']]);
        if ($targetGroup) {
            echo "<p><strong>目标用户组：</strong>" . htmlspecialchars($targetGroup['group_name']) . "</p>";
        } else {
            echo "<p><strong>⚠️ 目标用户组不存在！</strong></p>";
        }
    }
    
    // 测试用户信息
    $user = $db->fetch("SELECT * FROM {$db->getPrefix()}users WHERE id = :id", ['id' => $_SESSION['user_id']]);
    if ($user) {
        echo "<h3>✅ 用户信息正常</h3>";
        echo "<p><strong>用户名：</strong>" . htmlspecialchars($user['username']) . "</p>";
        echo "<p><strong>当前积分：</strong>" . $user['points'] . "</p>";
    } else {
        echo "<h3>❌ 用户不存在</h3>";
    }
} else {
    echo "<h3>❌ 充值卡未找到</h3>";
    echo "<p>查询条件：</p>";
    echo "<ul>";
    echo "<li>卡号：" . htmlspecialchars($cardNumber) . "</li>";
    echo "<li>密码：" . htmlspecialchars($cardPassword) . "</li>";
    echo "<li>条件：used_at IS NULL AND status = 1</li>";
    echo "</ul>";
    
    // 检查是否存在这张卡
    $anyCard = $db->fetch("
        SELECT * FROM {$db->getPrefix()}recharge_cards 
        WHERE card_number = :card_number AND card_password = :card_password
    ", [
        'card_number' => $cardNumber,
        'card_password' => $cardPassword
    ]);
    
    if ($anyCard) {
        echo "<p><strong>卡片存在但不符合条件：</strong></p>";
        echo "<ul>";
        echo "<li>used_at: " . ($anyCard['used_at'] ?: 'NULL') . "</li>";
        echo "<li>status: " . $anyCard['status'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p><strong>卡片完全不存在</strong></p>";
    }
}

echo "<hr>";
echo "<h3>数据库中的充值卡列表（前5张）：</h3>";
$allCards = $db->fetchAll("SELECT card_number, card_password, type, points, used_at, status FROM {$db->getPrefix()}recharge_cards LIMIT 5");
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>卡号</th><th>密码</th><th>类型</th><th>积分</th><th>使用时间</th><th>状态</th></tr>";
foreach ($allCards as $c) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($c['card_number']) . "</td>";
    echo "<td>" . htmlspecialchars($c['card_password']) . "</td>";
    echo "<td>" . htmlspecialchars($c['type']) . "</td>";
    echo "<td>" . $c['points'] . "</td>";
    echo "<td>" . ($c['used_at'] ?: 'NULL') . "</td>";
    echo "<td>" . $c['status'] . "</td>";
    echo "</tr>";
}
echo "</table>";
?>
